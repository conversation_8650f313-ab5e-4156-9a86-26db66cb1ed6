{% extends "base.html" %}

{% block title %}配置表测试 - 游戏测试平台{% endblock %}

{% block extra_head %}
<!-- Flatpickr CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

<style>
    /* 标签页样式 */
    .tab-container {
        margin-bottom: 2rem;
    }
    
    .tab-list {
        display: flex;
        border-bottom: 2px solid var(--border-color);
        margin-bottom: 2rem;
    }
    
    .tab-button {
        padding: 0.75rem 1.5rem;
        background: none;
        border: none;
        border-bottom: 2px solid transparent;
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition);
        position: relative;
    }
    
    .tab-button:hover {
        color: var(--primary-color);
        background-color: rgba(37, 99, 235, 0.05);
    }
    
    .tab-button.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
        background-color: rgba(37, 99, 235, 0.05);
    }
    
    .tab-content {
        display: none;
    }
    
    .tab-content.active {
        display: block;
    }
    
    /* 配置对比页面样式 */
    .compare-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    
    .compare-config {
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
    }
    
    .date-input-group {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .date-input {
        flex: 1;
    }
    
    .date-input label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }
    
    .date-input input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        transition: var(--transition);
    }
    
    .date-input input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
    
    /* 进度条样式 */
    .progress-container {
        margin: 1.5rem 0;
        padding: 1.5rem;
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        display: none;
    }
    
    .progress-container.show {
        display: block;
    }
    
    .progress-bar-container {
        background-color: var(--border-color);
        border-radius: var(--radius-md);
        height: 8px;
        overflow: hidden;
        margin: 1rem 0;
    }
    
    .progress-bar-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), #06b6d4);
        border-radius: var(--radius-md);
        transition: width 0.3s ease;
        width: 0%;
    }
    
    .progress-text {
        font-size: 0.875rem;
        color: var(--text-secondary);
        text-align: center;
    }
    
    /* 结果展示区域 */
    .results-container {
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        min-height: 300px;
    }
    
    .results-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .results-content {
        max-height: 400px;
        overflow-y: auto;
    }
    
    /* 测试用例展示样式 */
    .test-cases-container {
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .test-case-item {
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        margin-bottom: 0.75rem;
        background: var(--background-color);
    }
    
    .test-case-item:last-child {
        margin-bottom: 0;
    }
    
    .test-case-name {
        font-weight: 500;
        color: var(--text-primary);
    }
    
    .test-case-desc {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
    }
    
    /* 自定义下拉选择器样式 */
    .custom-select-container {
        position: relative;
        display: inline-block;
        width: 100%;
    }
    
    .custom-select {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 0.5rem 2rem 0.5rem 0.75rem;
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        color: var(--text-primary);
        cursor: pointer;
        transition: var(--transition);
        min-height: 38px;
        position: relative;
    }
    
    .custom-select:focus,
    .custom-select.open {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
    }
    
    .custom-select:disabled {
        background-color: var(--background-color);
        color: var(--text-light);
        cursor: not-allowed;
    }
    
    .custom-select::after {
        content: '';
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%) rotate(0deg);
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 6px solid #6b7280;
        transition: transform 0.2s ease;
        pointer-events: none;
    }
    
    .custom-select.open::after {
        transform: translateY(-50%) rotate(180deg);
    }
    
    .custom-select-text {
        flex: 1;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .custom-select-options {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-top: none;
        border-radius: 0 0 var(--radius-md) var(--radius-md);
        box-shadow: var(--shadow-lg);
        z-index: 1000;
        max-height: 400px;
        overflow-y: auto;
        display: none;
    }
    
    .custom-select-options.show {
        display: block;
    }
    
    .custom-select-option {
        padding: 0.5rem 0.75rem;
        cursor: pointer;
        border-bottom: 1px solid var(--border-color);
        transition: var(--transition);
        font-size: 0.875rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .custom-select-option:last-child {
        border-bottom: none;
    }
    
    .custom-select-option:hover {
        background: rgba(37, 99, 235, 0.1);
        color: var(--primary-color);
    }
    
    .custom-select-option.selected {
        background: var(--primary-color);
        color: white;
        font-weight: 500;
    }
    
    .custom-select-option.placeholder {
        color: var(--text-light);
        font-style: italic;
    }
    
    /* 滚动条样式 */
    .custom-select-options::-webkit-scrollbar {
        width: 6px;
    }
    
    .custom-select-options::-webkit-scrollbar-track {
        background: var(--background-color);
        border-radius: 3px;
    }
    
    .custom-select-options::-webkit-scrollbar-thumb {
        background: var(--border-color);
        border-radius: 3px;
    }
    
    .custom-select-options::-webkit-scrollbar-thumb:hover {
        background: var(--text-light);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .compare-container {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        .date-input-group {
            flex-direction: column;
            gap: 0.75rem;
        }
        
        .tab-button {
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
        }
        
        .custom-select-options {
            max-height: 300px;
        }
    }

    /* 对比结果表格样式 */
    .compare-results-table {
        margin-top: 1.5rem;
    }
    
    .table-header {
        margin-bottom: 1rem;
    }
    
    .summary-stats {
        display: flex;
        gap: 2rem;
        padding: 1rem;
        background: var(--background-color);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-color);
    }
    
    .stat-item {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }
    
    .stat-item.added {
        color: #059669;
    }
    
    .stat-item.deleted {
        color: #dc2626;
    }
    
    .stat-item.changed {
        color: #d97706;
    }
    
    .stat-item strong {
        color: inherit;
        font-weight: 600;
    }
    
    .table-container {
        background: var(--surface-color);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        overflow: hidden;
    }
    
    .results-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .results-table th {
        background: var(--background-color);
        padding: 0.75rem 1rem;
        text-align: left;
        font-weight: 500;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border-color);
        font-size: 0.875rem;
    }
    
    .results-table td {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-color);
        font-size: 0.875rem;
    }
    
    .results-table tr:last-child td {
        border-bottom: none;
    }
    
    .results-table tr:hover {
        background: rgba(37, 99, 235, 0.05);
    }
    
    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: var(--radius-sm);
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }
    
    .status-added {
        background: #dcfce7;
        color: #059669;
    }
    
    .status-deleted {
        background: #fee2e2;
        color: #dc2626;
    }
    
    .status-changed {
        background: #fef3c7;
        color: #d97706;
    }
    

    
    /* 操作按钮样式 */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        align-items: center;
    }
    
    .action-buttons .btn {
        white-space: nowrap;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .status-error {
        background: #fee2e2;
        color: #dc2626;
    }
    
    /* 进度显示样式 */
    .compare-progress-container {
        margin-top: 1rem;
        padding: 1.5rem;
        background: #f8fafc;  /* 使用明确的浅色背景 */
        border: 2px solid #e2e8f0;  /* 增加边框宽度 */
        border-radius: 8px;
        animation: slideDown 0.3s ease-out;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);  /* 添加阴影 */
    }
    
    .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .progress-label {
        font-size: 1rem;
        font-weight: 600;
        color: #374151;  /* 使用明确的深灰色 */
    }

    .progress-percentage {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1d4ed8;  /* 使用明确的蓝色 */
        background: #dbeafe;  /* 添加背景色 */
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
    }
    
    .progress-bar {
        width: 100%;
        height: 16px;  /* 增加高度使其更明显 */
        background: #e5e7eb;  /* 使用明确的灰色背景 */
        border: 1px solid #d1d5db;  /* 添加边框 */
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 0.5rem;
        position: relative;
    }

    .progress-bar-fill {
        height: 100%;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);  /* 使用明确的蓝色 */
        border-radius: 7px;
        transition: width 0.3s ease;
        min-width: 2px;  /* 确保即使是0%也有一点显示 */
    }
    
    .progress-message {
        font-size: 0.9rem;
        color: #6b7280;  /* 使用明确的中灰色 */
        text-align: center;
        font-weight: 500;
        margin-top: 0.5rem;
        padding: 0.5rem;
        background: #f3f4f6;  /* 添加背景色 */
        border-radius: 4px;
    }
    
    /* 历史对比样式 */
    .results-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }
    
    /* 历史对比弹窗样式 */
    .history-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .history-modal.show {
        opacity: 1;
        visibility: visible;
    }
    
    .history-modal-content {
        background: var(--surface-color);
        border-radius: var(--radius-lg);
        width: 90%;
        max-width: 1000px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: scale(0.95);
        transition: transform 0.3s ease;
    }
    
    .history-modal.show .history-modal-content {
        transform: scale(1);
    }
    
    .history-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.5rem;
        border-bottom: 1px solid var(--border-color);
    }
    
    .history-modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .history-modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-secondary);
        padding: 0.25rem;
        border-radius: var(--radius-sm);
        transition: var(--transition);
    }
    
    .history-modal-close:hover {
        color: var(--text-primary);
        background: var(--background-color);
    }
    
    .history-modal-body {
        padding: 1.5rem;
        max-height: 60vh;
        overflow-y: auto;
    }
    
    .history-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .history-item {
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--background-color);
        transition: var(--transition);
        cursor: pointer;
    }
    
    .history-item:hover {
        border-color: var(--primary-color);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .history-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .history-item-title {
        font-weight: 500;
        color: var(--text-primary);
    }
    
    .history-item-time {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }
    
    .history-item-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .history-item-detail {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }
    
    .history-item-detail strong {
        color: var(--text-primary);
    }
    
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* 加载动画 */
    .loading-spinner {
        width: 24px;
        height: 24px;
        border: 2px solid var(--border-color);
        border-top: 2px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 数据库管理样式 */
    .database-stats-section {
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--background-color);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        background: rgba(var(--primary-rgb), 0.05);
    }

    .stat-label {
        font-size: 0.8rem;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
    }

    .stat-value {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .database-cleanup-section {
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--background-color);
    }

    .cleanup-config {
        margin-top: 1rem;
    }

    .config-row {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        flex-wrap: wrap;
    }

    .config-row label {
        min-width: 140px;
        font-weight: 500;
        color: var(--text-primary);
    }

    .config-note {
        font-size: 0.8rem;
        color: var(--text-secondary);
        font-style: italic;
    }

    .database-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    #cleanup-result {
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        background: var(--background-color);
    }

    #cleanup-result-content {
        font-family: monospace;
        font-size: 0.9rem;
        white-space: pre-wrap;
        background: #f8f9fa;
        padding: 1rem;
        border-radius: var(--radius-sm);
        border: 1px solid #dee2e6;
    }
</style>
{% endblock %}

{% block breadcrumb %}
<button class="btn btn-outline" onclick="window.location.href='/'">
    <span class="btn-icon">←</span>
    返回首页
</button>
<span class="breadcrumb-separator">/</span>
<span class="page-title">
    <span class="title-icon">📋</span>
    配置表测试
</span>
{% endblock %}

{% block content %}
<!-- 标签页导航 -->
<div class="tab-container">
    <div class="tab-list">
        <button class="tab-button active" data-tab="config-compare">
            <span>📊</span> 配置对比
        </button>
        <button class="tab-button" data-tab="config-test">
            <span>🧪</span> 配置表功能测试
        </button>
    </div>
</div>

<!-- 配置对比页面 -->
<div id="config-compare" class="tab-content active">
    <div class="compare-container">
        <!-- 左侧配置面板 -->
        <div class="compare-config">
            <div class="panel-header">
                <h3>配置对比设置</h3>
            </div>

            <div class="config-form">
                <!-- 分支选择 -->
                <div class="form-group">
                    <label for="branch-select">分支选择</label>
                    <div class="custom-select-container">
                        <div class="custom-select" id="branch-select" data-requires-project tabindex="0">
                            <span class="custom-select-text">请先选择项目</span>
                            <div class="custom-select-options" id="branch-select-options"></div>
                        </div>
                    </div>
                </div>

                <!-- 时间选择 -->
                <div class="form-group">
                    <label>时间范围选择</label>
                    <div class="date-input-group">
                        <div class="date-input">
                            <label for="start-time">开始时间</label>
                            <input type="text" id="start-time" class="modern-input flatpickr-input" placeholder="选择开始时间" readonly>
                        </div>
                        <div class="date-input">
                            <label for="end-time">结束时间</label>
                            <input type="text" id="end-time" class="modern-input flatpickr-input" placeholder="选择结束时间" readonly>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button class="btn btn-primary btn-lg" id="start-compare" disabled data-requires-project>
                        <span class="btn-icon">🔍</span>
                        开始对比
                    </button>
                    <button class="btn btn-secondary" id="test-progress" style="margin-left: 10px;">
                        <span class="btn-icon">🧪</span>
                        测试进度条
                    </button>
                    
                    <!-- 对比进度显示 -->
                    <div class="compare-progress-container" id="compare-progress-container" style="display: none;">
                        <div class="progress-info">
                            <span class="progress-label">对比进度</span>
                            <span class="progress-percentage" id="compare-progress-percentage">0%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-bar-fill" id="compare-progress-fill" style="width: 0%;"></div>
                        </div>
                        <div class="progress-message" id="compare-progress-message">准备开始...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧结果面板 -->
        <div class="results-container">
            <div class="results-header">
                <h3>对比结果</h3>
                <div class="results-actions">
                    <button class="btn btn-sm btn-outline" id="view-history-compare" title="查看历史对比记录">
                        <span class="btn-icon">📊</span>
                        历史对比
                    </button>
                    <button class="btn btn-sm btn-outline" id="database-management" title="数据库管理和清理">
                        <span class="btn-icon">🗂️</span>
                        数据库管理
                    </button>
                    <button class="btn btn-sm btn-outline" id="download-compare-report" disabled>下载报告</button>
                </div>
            </div>
            <div class="results-content" id="compare-results">
                <div class="log-message log-info">
                    <span class="log-time">[{{ current_time }}]</span>
                    <span class="log-text">请配置参数后开始对比</span>
                </div>
            </div>
            
            <!-- 对比结果表格 -->
            <div class="compare-results-table" id="compare-results-table" style="display: none;">
                <div class="table-header">
                    <div class="summary-stats" id="compare-summary">
                        <span class="stat-item added">新增: <strong id="added-count">0</strong></span>
                        <span class="stat-item deleted">删除: <strong id="deleted-count">0</strong></span>
                        <span class="stat-item changed">变更: <strong id="changed-count">0</strong></span>
                    </div>
                </div>
                <div class="table-container">
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="compare-results-tbody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置表功能测试页面 -->
<div id="config-test" class="tab-content">
    <div class="compare-container">
        <!-- 左侧配置面板 -->
        <div class="compare-config">
            <div class="panel-header">
                <h3>测试配置</h3>
            </div>

            <div class="config-form">
                <!-- 测试用例展示 -->
                <div class="form-group">
                    <label>测试用例</label>
                    <div class="test-cases-container" id="test-cases-display">
                        <div class="test-case-item">
                            <div class="test-case-name">示例用例1: 基础配置表验证</div>
                            <div class="test-case-desc">验证配置表的基本结构和字段完整性</div>
                        </div>
                        <div class="test-case-item">
                            <div class="test-case-name">示例用例2: 数据类型检查</div>
                            <div class="test-case-desc">检查配置表中各字段的数据类型是否正确</div>
                        </div>
                        <div class="test-case-item">
                            <div class="test-case-name">示例用例3: 关联性验证</div>
                            <div class="test-case-desc">验证不同配置表之间的关联关系</div>
                        </div>
                    </div>
                </div>

                <!-- 分支选择 -->
                <div class="form-group">
                    <label for="test-branch-select">分支选择</label>
                    <div class="custom-select-container">
                        <div class="custom-select" id="test-branch-select" data-requires-project tabindex="0">
                            <span class="custom-select-text">请先选择项目</span>
                            <div class="custom-select-options" id="test-branch-select-options"></div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button class="btn btn-primary btn-lg" id="start-config-test" disabled data-requires-project>
                        <span class="btn-icon">🧪</span>
                        开始测试
                    </button>
                </div>
            </div>
        </div>

        <!-- 右侧结果面板 -->
        <div class="results-container">
            <div class="results-header">
                <h3>测试结果</h3>
                <button class="btn btn-sm btn-outline" id="download-test-report" disabled>下载报告</button>
            </div>
            <div class="results-content" id="test-results">
                <div class="log-message log-info">
                    <span class="log-time">[{{ current_time }}]</span>
                    <span class="log-text">请选择分支后开始测试</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 进度条 -->
    <div class="progress-container" id="test-progress">
        <h4>测试进度</h4>
        <div class="progress-bar-container">
            <div class="progress-bar-fill" id="test-progress-fill"></div>
        </div>
        <div class="progress-text" id="test-progress-text">准备开始...</div>
    </div>
</div>

<!-- 历史对比弹窗 -->
<div class="history-modal" id="history-modal">
    <div class="history-modal-content">
        <div class="history-modal-header">
            <h3 class="history-modal-title">历史对比记录</h3>
            <button class="history-modal-close" onclick="closeHistoryModal()">&times;</button>
        </div>
        <div class="history-modal-body">
            <div id="history-loading" style="text-align: center; padding: 2rem;">
                <div class="loading-spinner"></div>
                <div style="margin-top: 1rem; color: var(--text-secondary);">加载历史记录...</div>
            </div>
            <div class="history-list" id="history-list" style="display: none;">
                <!-- 历史记录将在这里动态生成 -->
            </div>
            <div id="history-empty" style="display: none; text-align: center; padding: 2rem; color: var(--text-secondary);">
                暂无历史对比记录
            </div>
        </div>
    </div>
</div>

<!-- 数据库管理弹窗 -->
<div class="history-modal" id="database-modal">
    <div class="history-modal-content" style="max-width: 800px;">
        <div class="history-modal-header">
            <h3 class="history-modal-title">数据库管理</h3>
            <button class="history-modal-close" onclick="closeDatabaseModal()">&times;</button>
        </div>
        <div class="history-modal-body">
            <!-- 数据库统计信息 -->
            <div class="database-stats-section">
                <h4>📊 存储统计</h4>
                <div id="database-stats-loading" style="text-align: center; padding: 2rem;">
                    <div class="loading-spinner"></div>
                    <div style="margin-top: 1rem; color: var(--text-secondary);">加载统计信息...</div>
                </div>
                <div id="database-stats-content" style="display: none;">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">总记录数</div>
                            <div class="stat-value" id="total-records">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">包含HTML的记录</div>
                            <div class="stat-value" id="records-with-html">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">最近7天记录</div>
                            <div class="stat-value" id="records-last-7-days">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">最近30天记录</div>
                            <div class="stat-value" id="records-last-30-days">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">表总大小</div>
                            <div class="stat-value" id="table-size">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">预估节省空间</div>
                            <div class="stat-value" id="estimated-savings">-</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 清理配置 -->
            <div class="database-cleanup-section" style="margin-top: 2rem;">
                <h4>🗑️ 清理配置</h4>
                <div class="cleanup-config">
                    <div class="config-row">
                        <label for="html-retention-days">HTML报告保留天数:</label>
                        <input type="number" id="html-retention-days" value="30" min="1" max="365" style="width: 80px;">
                        <span class="config-note">超过此天数的HTML报告将被清理（保留元数据）</span>
                    </div>
                    <div class="config-row">
                        <label for="metadata-retention-days">完整记录保留天数:</label>
                        <input type="number" id="metadata-retention-days" value="90" min="1" max="1095" style="width: 80px;">
                        <span class="config-note">超过此天数的记录将被完全删除</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="database-actions" style="margin-top: 2rem; text-align: center;">
                <button class="btn btn-primary" onclick="executeCleanup()" id="cleanup-btn">
                    🧹 执行清理
                </button>
                <button class="btn btn-outline" onclick="refreshDatabaseStats()">
                    🔄 刷新统计
                </button>
                <button class="btn btn-outline" onclick="scheduleAutoCleanup()">
                    ⏰ 启动定期清理
                </button>
            </div>

            <!-- 清理结果 -->
            <div id="cleanup-result" style="display: none; margin-top: 2rem;">
                <h4>清理结果</h4>
                <div id="cleanup-result-content"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // 配置表测试页面专用JavaScript
    let compareInProgress = false;
    let testInProgress = false;
    
    // 防抖相关变量
    let lastRequestTime = 0;
    let requestDebounceDelay = 1000; // 1秒防抖
    let branchLoadingInProgress = false;

    // 立即定义所有需要的全局函数，而不等待DOMContentLoaded
    
    // 选择选项函数
    function selectOption(selectElement, value, text) {
        const textElement = selectElement.querySelector('.custom-select-text');
        const options = selectElement.querySelector('.custom-select-options');
        
        // 更新显示文本
        if (textElement) {
            textElement.textContent = text;
        }
        
        // 更新选中状态
        if (options) {
            options.querySelectorAll('.custom-select-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            const selectedOption = options.querySelector(`[data-value="${value}"]`);
            if (selectedOption) {
                selectedOption.classList.add('selected');
            }
        }
        
        // 设置值属性
        selectElement.dataset.value = value;
        
        // 强制关闭下拉列表
        selectElement.classList.remove('open');
        if (options) {
            options.classList.remove('show');
            // 确保下拉列表完全隐藏
            setTimeout(() => {
                options.style.display = 'none';
                setTimeout(() => {
                    options.style.display = '';
                }, 10);
            }, 100);
        }
        
        // 触发change事件
        const changeEvent = new CustomEvent('change', {
            detail: { value: value, text: text }
        });
        selectElement.dispatchEvent(changeEvent);
    }

    // 更新自定义分支选择器
    function updateBranchSelects(branches) {
        const branchSelects = [
            { id: 'branch-select', optionsId: 'branch-select-options' },
            { id: 'test-branch-select', optionsId: 'test-branch-select-options' }
        ];
        
        branchSelects.forEach(({ id, optionsId }) => {
            const select = document.getElementById(id);
            const optionsContainer = document.getElementById(optionsId);
            
            if (select && optionsContainer) {
                // 清空并重新填充选项
                optionsContainer.innerHTML = '';
                
                // 添加占位符选项
                const placeholderOption = document.createElement('div');
                placeholderOption.className = 'custom-select-option placeholder';
                placeholderOption.dataset.value = '';
                placeholderOption.textContent = '请选择分支';
                placeholderOption.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    selectOption(select, '', '请选择分支');
                });
                optionsContainer.appendChild(placeholderOption);
                
                // 添加分支选项
                branches.forEach(branch => {
                    const option = document.createElement('div');
                    option.className = 'custom-select-option';
                    option.dataset.value = branch;
                    option.textContent = branch;
                    option.title = branch; // 添加悬停提示显示完整分支名
                    
                    option.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        selectOption(select, branch, branch);
                    });
                    
                    optionsContainer.appendChild(option);
                });
                
                // 重置选择器状态
                const textElement = select.querySelector('.custom-select-text');
                if (textElement) {
                    textElement.textContent = '请选择分支';
                }
                select.dataset.value = '';
            }
        });
        
        console.log(`配置表测试页面：已更新分支选择器，共 ${branches.length} 个分支:`, branches);
    }

    // 全局分支加载函数（带重试机制）
    window.loadBranches = async function loadBranches(projectName, retryCount = 0) {
        console.log(`配置表测试页面：开始为项目 ${projectName} 加载分支数据 (重试次数: ${retryCount})`);
        
        if (!projectName) {
            console.error('配置表测试页面：项目名称不能为空');
            window.configTestLoading = false; // 重置状态
            return;
        }
        
        // 防止重复加载相同项目的数据（更严格的检查）
        if (window.lastLoadedProject === projectName && window.branchesLoaded && !window.forceReload) {
            console.log(`配置表测试页面：项目 ${projectName} 的分支数据已加载，跳过重复加载`);
            window.configTestLoading = false; // 重置状态
            return;
        }
        
        // 防止并发加载
        if (window.configTestLoading && retryCount === 0) {
            console.log(`配置表测试页面：分支数据正在加载中，跳过重复调用`);
            return;
        }
        
        window.configTestLoading = true;
        window.forceReload = false; // 重置强制重载标志
        
        const maxRetries = 3;
        const retryDelay = 1000; // 1秒延迟
        
        try {
            const url = `/api/config_test/branches?project=${encodeURIComponent(projectName)}`;
            console.log(`配置表测试页面：请求URL: ${url}`);
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
            
            const response = await fetch(url, {
                signal: controller.signal,
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });
            
            clearTimeout(timeoutId);
            console.log(`配置表测试页面：收到响应，状态: ${response.status}`);
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
            }
            
            const result = await response.json();
            console.log(`配置表测试页面：响应数据:`, result);
            
            if (result.success) {
                console.log(`配置表测试页面：成功加载 ${result.branches.length} 个分支`);
                updateBranchSelects(result.branches);
                // 标记数据已加载
                window.lastLoadedProject = projectName;
                window.branchesLoaded = true;
                console.log(`配置表测试页面：分支选择器已更新，当前项目: ${projectName}`);
            } else {
                console.error('配置表测试页面：加载分支失败:', result.message);
                throw new Error('加载分支失败: ' + (result.message || '未知错误'));
            }
        } catch (error) {
            console.error('配置表测试页面：加载分支时发生错误:', error);
            
            // 如果是网络错误且还有重试次数，则重试
            if (retryCount < maxRetries && (error.name === 'TypeError' || error.name === 'AbortError' || error.message.includes('Failed to fetch'))) {
                console.log(`配置表测试页面：${retryDelay}ms后进行第${retryCount + 1}次重试...`);
                window.configTestLoading = false; // 重置状态以允许重试
                
                setTimeout(() => {
                    window.loadBranches(projectName, retryCount + 1);
                }, retryDelay);
                return;
            }
            
            // 最终失败，显示错误信息
            if (typeof window.showError === 'function') {
                window.showError('加载分支时发生错误: ' + error.message);
            } else {
            alert('加载分支时发生错误: ' + error.message);
            }
        } finally {
            // 确保在函数结束时重置加载状态
            if (retryCount >= maxRetries || !error) {
            window.configTestLoading = false;
            console.log('配置表测试页面：分支加载完成，重置加载状态');
            }
        }
    };

    document.addEventListener('DOMContentLoaded', function() {
        initializeConfigTest();
    });

    function initializeConfigTest() {
        setupTabs();
        setupEventListeners();
        initCustomSelects();
        
        // 简化初始化逻辑，避免重复加载
        const savedProject = (typeof getCurrentProject === 'function') 
            ? getCurrentProject() 
            : localStorage.getItem('currentProject');
        
        if (savedProject) {
            console.log(`配置表测试页面：初始化时发现保存的项目=${savedProject}`);
            // 设置项目选择器
            const projectSelect = document.getElementById('project-select');
            if (projectSelect) {
                projectSelect.value = savedProject;
            }
            
            // 只在页面首次加载时加载分支数据，避免重复调用
            if (!window.pageInitialized) {
                window.pageInitialized = true;
                // 延迟加载分支数据，确保页面完全初始化
                setTimeout(() => {
                    if (typeof window.loadBranches === 'function') {
                        window.loadBranches(savedProject);
                    }
                }, 200);
            }
        }
    }

    // 设置标签页切换
    function setupTabs() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');
                
                // 移除所有活动状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // 添加当前活动状态
                this.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 开始对比按钮
        const startCompareBtn = document.getElementById('start-compare');
        if (startCompareBtn) {
            startCompareBtn.addEventListener('click', startConfigCompare);
        }

        // 测试进度条按钮
        const testProgressBtn = document.getElementById('test-progress');
        if (testProgressBtn) {
            testProgressBtn.addEventListener('click', testProgressBar);
        }
        
        // 历史对比按钮
        const historyCompareBtn = document.getElementById('view-history-compare');
        if (historyCompareBtn) {
            historyCompareBtn.addEventListener('click', openHistoryModal);
        }

        // 开始测试按钮
        const startTestBtn = document.getElementById('start-config-test');
        if (startTestBtn) {
            startTestBtn.addEventListener('click', startConfigTest);
        }

        // 监听项目变化事件，当项目切换时重新加载分支数据
        window.addEventListener('projectChanged', function(event) {
            console.log('配置表测试页面：收到项目变化事件:', event.detail.project);
            if (event.detail && event.detail.project) {
                // 防止重复处理相同的项目变化事件
                if (window.lastProjectChangeEvent === event.detail.project && window.projectChangeHandling) {
                    console.log('配置表测试页面：项目变化事件正在处理中，跳过重复处理');
                    return;
                }
                
                window.lastProjectChangeEvent = event.detail.project;
                window.projectChangeHandling = true;
                
                // 重置分支加载状态，确保新项目的数据会被加载
                window.branchesLoaded = false;
                window.lastLoadedProject = null;
                window.configTestLoading = false; // 重置加载状态
                window.forceReload = true; // 设置强制重载标志
                
                console.log(`配置表测试页面：准备为项目 ${event.detail.project} 加载分支数据`);
                console.log(`配置表测试页面：状态重置 - branchesLoaded=false, lastLoadedProject=null, configTestLoading=false, forceReload=true`);
                
                // 延迟执行以避免与main.js中的加载冲突
                setTimeout(() => {
                    // 确保loadBranches函数存在后再调用
                    if (typeof window.loadBranches === 'function') {
                        console.log('配置表测试页面：延迟调用loadBranches');
                        window.loadBranches(event.detail.project);
                    } else {
                        console.error('配置表测试页面：loadBranches函数不存在');
                    }
                    window.projectChangeHandling = false;
                }, 100);
            } else {
                console.log('配置表测试页面：项目变化事件没有有效的项目信息');
            }
        });
    }

    // 自定义下拉选择器功能
    function initCustomSelects() {
        const selects = document.querySelectorAll('.custom-select');
        
        selects.forEach(select => {
            // 点击展开/收起
            select.addEventListener('click', function(e) {
                if (this.hasAttribute('disabled')) return;
                
                // 阻止事件冒泡，避免与选项点击冲突
                e.stopPropagation();
                
                const isOpen = this.classList.contains('open');
                
                // 关闭所有其他下拉列表
                selects.forEach(s => {
                    s.classList.remove('open');
                    const options = s.querySelector('.custom-select-options');
                    if (options) {
                        options.classList.remove('show');
                    }
                });
                
                // 切换当前下拉列表
                if (!isOpen) {
                    this.classList.add('open');
                    const options = this.querySelector('.custom-select-options');
                    if (options) {
                        options.classList.add('show');
                    }
                }
            });
            
            // 键盘导航
            select.addEventListener('keydown', function(e) {
                if (this.hasAttribute('disabled')) return;
                
                const options = this.querySelector('.custom-select-options');
                const isOpen = this.classList.contains('open');
                
                switch(e.key) {
                    case 'Enter':
                    case ' ':
                        e.preventDefault();
                        this.click();
                        break;
                    case 'Escape':
                        this.classList.remove('open');
                        if (options) options.classList.remove('show');
                        break;
                    case 'ArrowDown':
                    case 'ArrowUp':
                        e.preventDefault();
                        if (isOpen && options) {
                            const optionElements = options.querySelectorAll('.custom-select-option:not(.placeholder)');
                            const currentIndex = Array.from(optionElements).findIndex(opt => opt.classList.contains('selected'));
                            let newIndex = e.key === 'ArrowDown' ? currentIndex + 1 : currentIndex - 1;
                            
                            if (newIndex < 0) newIndex = optionElements.length - 1;
                            if (newIndex >= optionElements.length) newIndex = 0;
                            
                            if (optionElements[newIndex]) {
                                selectOption(this, optionElements[newIndex].dataset.value, optionElements[newIndex].textContent);
                            }
                        }
                        break;
                }
            });
        });
        
        // 点击外部关闭下拉列表
        document.addEventListener('click', function(e) {
            // 检查点击是否在任何下拉选择器外部
            if (!e.target.closest('.custom-select-container')) {
                selects.forEach(select => {
                    if (select.classList.contains('open')) {
                        select.classList.remove('open');
                        const options = select.querySelector('.custom-select-options');
                        if (options) {
                            options.classList.remove('show');
                            // 强制隐藏
                            setTimeout(() => {
                                options.style.display = 'none';
                                setTimeout(() => {
                                    options.style.display = '';
                                }, 10);
                            }, 50);
                        }
                    }
                });
            }
        });
    }

    // selectOption和updateBranchSelects函数已在脚本开头定义

    // 开始配置对比
    async function startConfigCompare() {
        // 防抖检查
        const currentTime = Date.now();
        if (currentTime - lastRequestTime < requestDebounceDelay) {
            showError('请求过于频繁，请稍后再试');
            return;
        }
        lastRequestTime = currentTime;
        
        if (compareInProgress) {
            showError('配置对比正在进行中，请稍等');
            return;
        }

        const branch = document.getElementById('branch-select').dataset.value;
        const startTime = document.getElementById('start-time').value;
        const endTime = document.getElementById('end-time').value;

        if (!branch || !startTime || !endTime) {
            showError('请填写完整的对比参数');
            return;
        }

        if (new Date(startTime) >= new Date(endTime)) {
            showError('开始时间必须小于结束时间');
            return;
        }

        compareInProgress = true;
        
        // 清空右边的对比结果
        clearCompareResults();
        
        showCompareProgress();
        updateCompareProgress(5, '准备开始对比...');  // 立即显示5%进度

        // 强制刷新进度条显示
        setTimeout(() => {
            updateCompareProgress(10, '正在初始化...');
        }, 500);

        try {
            // 转换时间格式为ISO字符串
            const startTimeISO = new Date(startTime).toISOString();
            const endTimeISO = new Date(endTime).toISOString();
            
            const response = await fetch('/api/config_test/compare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    project: getCurrentProject(),
                    branch: branch,
                    start_time: startTimeISO,
                    end_time: endTimeISO
                })
            });

            const result = await response.json();
            
            if (result.success) {
                console.log(`对比任务启动成功，task_id: ${result.task_id}`);
                // 稍等一下让任务完全注册后再开始轮询进度
                setTimeout(() => {
                    console.log(`开始轮询任务进度: ${result.task_id}`);
                pollCompareProgress(result.task_id);
                }, 1000); // 等待1秒
            } else {
                showError(result.message || '启动对比失败');
                hideCompareProgress();
                compareInProgress = false;
            }
        } catch (error) {
            showError('启动对比时发生错误: ' + error.message);
            hideCompareProgress();
            compareInProgress = false;
        }
    }

    // 开始配置表功能测试
    async function startConfigTest() {
        if (testInProgress) return;

        const branch = document.getElementById('test-branch-select').dataset.value;

        if (!branch) {
            showError('请选择测试分支');
            return;
        }

        testInProgress = true;
        showTestProgress();
        updateTestProgress(0, '准备开始测试...');

        try {
            const response = await fetch('/api/config_test/function_test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    project: getCurrentProject(),
                    branch: branch
                })
            });

            const result = await response.json();
            
            if (result.success) {
                showSuccess('测试参数已发送到后端');
                updateTestResults(result.message || '测试已启动');
                updateTestProgress(100, '测试完成');
                setTimeout(() => {
                    hideTestProgress();
                    testInProgress = false;
                }, 2000);
            } else {
                showError(result.message || '启动测试失败');
                hideTestProgress();
                testInProgress = false;
            }
        } catch (error) {
            showError('启动测试时发生错误: ' + error.message);
            hideTestProgress();
            testInProgress = false;
        }
    }

    // 轮询对比进度（带重试机制）
    async function pollCompareProgress(taskId, retryCount = 0) {
        const maxRetries = 3;
        
        try {
            console.log(`轮询任务进度，task_id: ${taskId}, 重试次数: ${retryCount}`);
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 8000); // 8秒超时
            
            const response = await fetch(`/api/config_test/compare_progress?task_id=${taskId}`, {
                signal: controller.signal,
                headers: {
                    'Cache-Control': 'no-cache'
                }
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            console.log(`进度API响应:`, result);
            
            if (result.success) {
                // 确保数据类型正确
                const progress = parseInt(result.progress) || 0;
                const message = result.message || '';
                const isRunning = Boolean(result.is_running);

                console.log(`更新进度: ${progress}%, 消息: ${message}, 运行中: ${isRunning}`);
                updateCompareProgress(progress, message);

                if (progress < 100 && isRunning) {
                    // 继续轮询
                    setTimeout(() => pollCompareProgress(taskId, 0), 2000); // 重置重试计数
                } else {
                    // 对比完成
                    console.log('任务完成，获取最终结果...');
                    
                    if (result.result) {
                        displayCompareResults(result.result);
                    } else {
                        // 获取完整结果
                        try {
                            const resultResponse = await fetch(`/api/config_test/compare_result?task_id=${taskId}`);
                            const resultData = await resultResponse.json();
                            if (resultData.success && resultData.result) {
                                displayCompareResults(resultData.result);
                            } else {
                                console.warn('未能获取到最终结果，但任务已完成');
                                updateCompareResults('配置对比已完成，但未能获取详细结果');
                            }
                        } catch (resultError) {
                            console.error('获取最终结果失败:', resultError);
                            updateCompareResults('配置对比已完成，但获取结果时出现错误');
                        }
                    }
                    hideCompareProgress();
                    compareInProgress = false;
                }
            } else {
                const errorMsg = result.message || '获取进度失败';
                console.error('进度API返回错误:', errorMsg);
                
                // 如果是网络错误且还有重试次数，则重试
                if (retryCount < maxRetries && (errorMsg.includes('任务不存在') === false)) {
                    console.log(`${1000}ms后进行第${retryCount + 1}次重试...`);
                    setTimeout(() => pollCompareProgress(taskId, retryCount + 1), 1000);
                    return;
                }
                
                showError(errorMsg);
                hideCompareProgress();
                compareInProgress = false;
            }
        } catch (error) {
            console.error('轮询进度失败:', error);
            
            // 如果是网络错误且还有重试次数，则重试
            if (retryCount < maxRetries && (error.name === 'TypeError' || error.name === 'AbortError' || error.message.includes('Failed to fetch'))) {
                console.log(`网络错误，${2000}ms后进行第${retryCount + 1}次重试...`);
                setTimeout(() => pollCompareProgress(taskId, retryCount + 1), 2000);
                return;
            }
            
            // 最终失败
            showError(`进度查询失败: ${error.message}`);
            hideCompareProgress();
            compareInProgress = false;
        }
    }

    // 显示对比结果
    function displayCompareResults(resultData) {
        console.log('显示对比结果:', resultData);
        
        // 隐藏初始消息，显示结果表格
        const resultsContainer = document.getElementById('compare-results');
        const resultsTable = document.getElementById('compare-results-table');
        
        resultsContainer.style.display = 'none';
        resultsTable.style.display = 'block';
        
        // 更新统计信息
        document.getElementById('added-count').textContent = resultData.added_files || 0;
        document.getElementById('deleted-count').textContent = resultData.deleted_files || 0;
        document.getElementById('changed-count').textContent = resultData.changed_files || 0;
        
        // 生成表格内容
        const tbody = document.getElementById('compare-results-tbody');
        tbody.innerHTML = '';
        
        if (resultData.results && resultData.results.length > 0) {
            resultData.results.forEach(file => {
                const row = document.createElement('tr');
                
                // 文件名
                const nameCell = document.createElement('td');
                nameCell.textContent = file.file_name || file.file_path.split('/').pop();
                row.appendChild(nameCell);
                
                // 状态
                const statusCell = document.createElement('td');
                const statusBadge = document.createElement('span');
                statusBadge.className = 'status-badge';
                
                switch(file.status) {
                    case 'added':
                        statusBadge.className += ' status-added';
                        statusBadge.textContent = '新增';
                        break;
                    case 'deleted':
                        statusBadge.className += ' status-deleted';
                        statusBadge.textContent = '删除';
                        break;
                    case 'success':
                    case 'modified':
                    case 'changed':
                        statusBadge.className += ' status-changed';
                        statusBadge.textContent = '变更';
                        break;
                    case 'compare_failed':
                        statusBadge.className += ' status-error';
                        statusBadge.textContent = '对比失败';
                        break;
                    case 'error':
                        statusBadge.className += ' status-error';
                        statusBadge.textContent = '错误';
                        break;
                    default:
                        statusBadge.textContent = file.status;
                }
                
                statusCell.appendChild(statusBadge);
                row.appendChild(statusCell);
                
                // 操作
                const actionCell = document.createElement('td');
                const actionContainer = document.createElement('div');
                actionContainer.className = 'action-buttons';
                
                // 对于有对比报告的文件，显示"查看报告"按钮
                if (file.report_url) {
                    const reportBtn = document.createElement('button');
                    reportBtn.className = 'btn btn-sm btn-primary';
                    reportBtn.textContent = '查看报告';
                    reportBtn.onclick = () => openCompareReport(file);
                    actionContainer.appendChild(reportBtn);
                }
                
                // 对于新增和删除的文件，只显示一个按钮
                if (file.status === 'added' || file.status === 'deleted') {
                    if (file.commit_url) {
                        const commitBtn = document.createElement('button');
                        commitBtn.className = 'btn btn-sm btn-outline';
                        commitBtn.textContent = file.status === 'added' ? '查看新增提交' : '查看删除提交';
                        commitBtn.onclick = () => window.open(file.commit_url, '_blank');
                        actionContainer.appendChild(commitBtn);
                    } else if (file.file_history_url) {
                        const historyBtn = document.createElement('button');
                        historyBtn.className = 'btn btn-sm btn-outline';
                        historyBtn.textContent = '文件历史';
                        historyBtn.onclick = () => window.open(file.file_history_url, '_blank');
                        actionContainer.appendChild(historyBtn);
                    }
                } else {
                    // 对于修改的文件，显示查看提交按钮
                    if (file.commit_url) {
                        const commitBtn = document.createElement('button');
                        commitBtn.className = 'btn btn-sm btn-outline';
                        commitBtn.textContent = '查看提交';
                        commitBtn.onclick = () => window.open(file.commit_url, '_blank');
                        actionContainer.appendChild(commitBtn);
                    }

                    // 文件历史链接（仅对修改的文件显示）
                    if (file.file_history_url) {
                        const historyBtn = document.createElement('button');
                        historyBtn.className = 'btn btn-sm btn-outline';
                        historyBtn.textContent = '文件历史';

                        historyBtn.onclick = () => {
                            window.open(file.file_history_url, '_blank');
                        };
                        actionContainer.appendChild(historyBtn);
                    }
                }
                
                // 如果没有任何操作按钮，显示"-"
                if (actionContainer.children.length === 0) {
                    actionContainer.textContent = '-';
                    actionContainer.style.color = 'var(--text-light)';
                }
                
                actionCell.appendChild(actionContainer);
                
                row.appendChild(actionCell);
                tbody.appendChild(row);
            });
        } else {
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 3;
            cell.textContent = '没有找到文件变化';
            cell.style.textAlign = 'center';
            cell.style.color = 'var(--text-light)';
            row.appendChild(cell);
            tbody.appendChild(row);
        }
    }

    // 打开对比报告
    function openCompareReport(file) {
        if (!file.report_url) {
            console.error('没有可用的报告路径');
            return;
        }
        
        // 检查是否是历史记录的报告URL
        if (file.report_url.startsWith('/static/reports/') && window.currentHistoryTaskId) {
            // 对于历史记录，使用新的API端点
            const currentProject = localStorage.getItem('currentProject');
            if (!currentProject) {
                alert('请先选择项目');
                return;
            }
            
            // 从file对象中获取文件路径
            const filePath = file.file_path || file.file_name;
            if (!filePath) {
                console.error('无法获取文件路径');
                return;
            }
            
            // 构建新的API URL
            const encodedFilePath = encodeURIComponent(filePath);
            const apiUrl = `/api/config_test/report/${encodeURIComponent(window.currentHistoryTaskId)}/${encodedFilePath}?project=${encodeURIComponent(currentProject)}`;
            
            console.log('使用API获取历史HTML报告:', apiUrl);
            window.open(apiUrl, '_blank');
        } else {
            // 对于当前对比结果，直接使用原始URL
            window.open(file.report_url, '_blank');
        }
    }

    // 显示/隐藏进度条
    function showCompareProgress() {
        const progressContainer = document.getElementById('compare-progress-container');
        if (progressContainer) {
            progressContainer.style.display = 'block';
            console.log('进度条容器已显示');
        } else {
            console.error('找不到进度条容器元素: compare-progress-container');
        }
    }

    function hideCompareProgress() {
        const progressContainer = document.getElementById('compare-progress-container');
        if (progressContainer) {
            progressContainer.style.display = 'none';
            console.log('进度条容器已隐藏');
        } else {
            console.error('找不到进度条容器元素: compare-progress-container');
        }
    }

    function clearCompareResults() {
        // 隐藏结果表格，显示默认日志信息
        document.getElementById('compare-results').style.display = 'block';
        document.getElementById('compare-results-table').style.display = 'none';
        
        // 清除历史记录标识
        if (window.currentHistoryTaskId) {
            delete window.currentHistoryTaskId;
            console.log('清除历史记录Task ID标识');
        }
        
        // 重置统计数据
        document.getElementById('added-count').textContent = '0';
        document.getElementById('deleted-count').textContent = '0';
        document.getElementById('changed-count').textContent = '0';
        
        // 清空表格内容
        const tbody = document.getElementById('compare-results-tbody');
        if (tbody) {
            tbody.innerHTML = '';
        }
        
        // 显示准备开始的消息
        const resultsContainer = document.getElementById('compare-results');
        resultsContainer.innerHTML = `
            <div class="log-message log-info">
                <span class="log-time">[${new Date().toLocaleTimeString()}]</span>
                <span class="log-text">正在开始对比...</span>
            </div>
        `;
    }

    // 更新对比结果表格（用于历史记录）
    function updateCompareResultsTable(resultData) {
        console.log('更新对比结果表格:', resultData);
        
        // 更新统计信息
        document.getElementById('added-count').textContent = resultData.added_files || 0;
        document.getElementById('deleted-count').textContent = resultData.deleted_files || 0;
        document.getElementById('changed-count').textContent = resultData.changed_files || 0;
        
        // 生成表格内容
        const tbody = document.getElementById('compare-results-tbody');
        tbody.innerHTML = '';
        
        if (resultData.results && resultData.results.length > 0) {
            resultData.results.forEach(file => {
                const row = document.createElement('tr');
                
                // 文件名
                const nameCell = document.createElement('td');
                nameCell.textContent = file.file_name || file.file_path.split('/').pop();
                row.appendChild(nameCell);
                
                // 状态
                const statusCell = document.createElement('td');
                const statusBadge = document.createElement('span');
                statusBadge.className = 'status-badge';
                
                switch(file.status) {
                    case 'added':
                        statusBadge.className += ' status-added';
                        statusBadge.textContent = '新增';
                        break;
                    case 'deleted':
                        statusBadge.className += ' status-deleted';
                        statusBadge.textContent = '删除';
                        break;
                    case 'success':
                    case 'modified':
                    case 'changed':
                        statusBadge.className += ' status-changed';
                        statusBadge.textContent = '变更';
                        break;
                    case 'compare_failed':
                        statusBadge.className += ' status-error';
                        statusBadge.textContent = '对比失败';
                        break;
                    case 'error':
                        statusBadge.className += ' status-error';
                        statusBadge.textContent = '错误';
                        break;
                    default:
                        statusBadge.textContent = file.status;
                }
                
                statusCell.appendChild(statusBadge);
                row.appendChild(statusCell);
                
                // 操作
                const actionCell = document.createElement('td');
                const actionContainer = document.createElement('div');
                actionContainer.className = 'action-buttons';
                
                // 对于有对比报告的文件，显示"查看报告"按钮
                if (file.report_url) {
                    const reportBtn = document.createElement('button');
                    reportBtn.className = 'btn btn-sm btn-primary';
                    reportBtn.textContent = '查看报告';
                    reportBtn.onclick = () => openCompareReport(file);
                    actionContainer.appendChild(reportBtn);
                }
                
                // 对于新增和删除的文件，只显示一个按钮
                if (file.status === 'added' || file.status === 'deleted') {
                    if (file.commit_url) {
                        const commitBtn = document.createElement('button');
                        commitBtn.className = 'btn btn-sm btn-outline';
                        commitBtn.textContent = file.status === 'added' ? '查看新增提交' : '查看删除提交';
                        commitBtn.onclick = () => window.open(file.commit_url, '_blank');
                        actionContainer.appendChild(commitBtn);
                    } else if (file.file_history_url) {
                        const historyBtn = document.createElement('button');
                        historyBtn.className = 'btn btn-sm btn-outline';
                        historyBtn.textContent = '文件历史';
                        historyBtn.onclick = () => window.open(file.file_history_url, '_blank');
                        actionContainer.appendChild(historyBtn);
                    }
                } else {
                    // 对于修改的文件，显示查看提交按钮
                    if (file.commit_url) {
                        const commitBtn = document.createElement('button');
                        commitBtn.className = 'btn btn-sm btn-outline';
                        commitBtn.textContent = '查看提交';
                        commitBtn.onclick = () => window.open(file.commit_url, '_blank');
                        actionContainer.appendChild(commitBtn);
                    }

                    // 文件历史链接（仅对修改的文件显示）
                    if (file.file_history_url) {
                        const historyBtn = document.createElement('button');
                        historyBtn.className = 'btn btn-sm btn-outline';
                        historyBtn.textContent = '文件历史';
                        historyBtn.onclick = () => window.open(file.file_history_url, '_blank');
                        actionContainer.appendChild(historyBtn);
                    }
                }
                
                // 如果没有任何操作按钮，显示"-"
                if (actionContainer.children.length === 0) {
                    actionContainer.textContent = '-';
                    actionContainer.style.color = 'var(--text-light)';
                }
                
                actionCell.appendChild(actionContainer);
                row.appendChild(actionCell);
                tbody.appendChild(row);
            });
        } else {
            // 没有结果时显示空消息
            const row = document.createElement('tr');
            const cell = document.createElement('td');
            cell.colSpan = 3;
            cell.textContent = '没有找到变更的文件';
            cell.style.textAlign = 'center';
            cell.style.color = 'var(--text-light)';
            row.appendChild(cell);
            tbody.appendChild(row);
        }
    }

    function showTestProgress() {
        document.getElementById('test-progress').classList.add('show');
    }

    function hideTestProgress() {
        document.getElementById('test-progress').classList.remove('show');
    }

    // 更新进度
    function updateCompareProgress(progress, message) {
        console.log(`updateCompareProgress 被调用: progress=${progress}, message=${message}`);

        const progressFill = document.getElementById('compare-progress-fill');
        const progressPercentage = document.getElementById('compare-progress-percentage');
        const progressMessage = document.getElementById('compare-progress-message');

        if (progressFill) {
            progressFill.style.width = progress + '%';
            console.log(`进度条宽度设置为: ${progress}%`);
        } else {
            console.error('找不到进度条填充元素: compare-progress-fill');
        }

        if (progressPercentage) {
            progressPercentage.textContent = progress + '%';
            console.log(`进度百分比设置为: ${progress}%`);
        } else {
            console.error('找不到进度百分比元素: compare-progress-percentage');
        }

        if (progressMessage) {
            progressMessage.textContent = message;
            console.log(`进度消息设置为: ${message}`);
        } else {
            console.error('找不到进度消息元素: compare-progress-message');
        }
    }

    function updateTestProgress(progress, message) {
        document.getElementById('test-progress-fill').style.width = progress + '%';
        document.getElementById('test-progress-text').textContent = message;
    }

    // 更新结果
    function updateCompareResults(data) {
        const resultsContainer = document.getElementById('compare-results');
        const resultsTable = document.getElementById('compare-results-table');
        if (data) {
            resultsContainer.style.display = 'none';
            resultsTable.style.display = 'block';
            resultsTable.innerHTML = `
                <div class="log-message log-success">
                    <span class="log-time">[${new Date().toLocaleTimeString()}]</span>
                    <span class="log-text">对比完成</span>
                </div>
                <div class="log-message log-info">
                    <span class="log-text">${data}</span>
                </div>
            `;
        }
    }

    function updateTestResults(data) {
        const resultsContainer = document.getElementById('test-results');
        if (data) {
            resultsContainer.innerHTML = `
                <div class="log-message log-success">
                    <span class="log-time">[${new Date().toLocaleTimeString()}]</span>
                    <span class="log-text">测试完成</span>
                </div>
                <div class="log-message log-info">
                    <span class="log-text">${data}</span>
                </div>
            `;
        }
    }

    // 启用下载按钮
    function enableDownloadButton(buttonId, url) {
        const button = document.getElementById(buttonId);
        if (button && url) {
            button.disabled = false;
            button.onclick = () => window.open(url);
        }
    }

    // 辅助函数
    function getCurrentProject() {
        return localStorage.getItem('currentProject') || '';
    }

    function showError(message) {
        // 修复递归调用问题
        if (typeof window.showError === 'function' && window.showError !== showError) {
            window.showError(message);
        } else {
            alert('错误: ' + message);
        }
    }

    function showSuccess(message) {
        // 修复递归调用问题
        if (typeof window.showSuccess === 'function' && window.showSuccess !== showSuccess) {
            window.showSuccess(message);
        } else {
            alert('成功: ' + message);
        }
    }

    // 调试函数 - 添加到全局作用域
    window.debugCheckStatus = function() {
        console.log('=== 当前状态检查 ===');
        console.log('当前项目:', localStorage.getItem('currentProject'));
        console.log('branchesLoaded:', window.branchesLoaded);
        console.log('lastLoadedProject:', window.lastLoadedProject);
        console.log('configTestLoading:', window.configTestLoading);
        console.log('projectJustChanged:', window.projectJustChanged);
        console.log('pageInitialized:', window.pageInitialized);
        
        // 检查分支选择器状态
        const branchSelect = document.getElementById('branch-select');
        const testBranchSelect = document.getElementById('test-branch-select');
        
        if (branchSelect) {
            console.log('branch-select 当前值:', branchSelect.dataset.value);
            console.log('branch-select 显示文本:', branchSelect.querySelector('.custom-select-text')?.textContent);
        }
        
        if (testBranchSelect) {
            console.log('test-branch-select 当前值:', testBranchSelect.dataset.value);
            console.log('test-branch-select 显示文本:', testBranchSelect.querySelector('.custom-select-text')?.textContent);
        }
        
        // 检查分支选项数量
        const branchOptions = document.getElementById('branch-select-options');
        const testBranchOptions = document.getElementById('test-branch-select-options');
        
        if (branchOptions) {
            console.log('branch-select-options 选项数量:', branchOptions.children.length);
        }
        
        if (testBranchOptions) {
            console.log('test-branch-select-options 选项数量:', testBranchOptions.children.length);
        }
    };

    window.debugSwitchProject = function(projectName) {
        console.log(`调试：手动切换到项目 ${projectName}`);
        
        // 模拟项目切换
        if (typeof switchProject === 'function') {
            switchProject(projectName);
        } else {
            console.error('switchProject函数不存在');
        }
    };

    window.debugTriggerProjectChange = function(projectName) {
        console.log(`调试：手动触发项目变化事件，项目=${projectName}`);
        window.dispatchEvent(new CustomEvent('projectChanged', { 
            detail: { project: projectName } 
        }));
    };

    window.debugLoadBranches = function(projectName) {
        console.log(`调试：手动调用loadBranches，项目=${projectName}`);
        if (typeof window.loadBranches === 'function') {
            window.loadBranches(projectName);
        } else {
            console.error('loadBranches函数不存在');
        }
    };

    window.debugResetState = function() {
        console.log('调试：重置所有状态');
        window.branchesLoaded = false;
        window.lastLoadedProject = null;
        window.configTestLoading = false;
        window.projectJustChanged = false;
        window.pageInitialized = false;
        console.log('状态已重置');
    };

    // 输出调试函数信息
    console.log('=== 调试函数已加载 ===');
    console.log('可用的调试函数:');
    console.log('- debugCheckStatus(): 检查当前状态');
    console.log('- debugSwitchProject(projectName): 手动切换项目');
    console.log('- debugTriggerProjectChange(projectName): 手动触发项目变化事件');
    console.log('- debugLoadBranches(projectName): 手动加载分支数据');
    console.log('- debugResetState(): 重置所有状态');
    console.log('示例: debugSwitchProject("Merge")');

    // 历史对比相关函数
    function openHistoryModal() {
        const modal = document.getElementById('history-modal');
        modal.classList.add('show');
        loadHistoryRecords();
    }

    function closeHistoryModal() {
        const modal = document.getElementById('history-modal');
        modal.classList.remove('show');
    }

    async function loadHistoryRecords() {
        const loadingDiv = document.getElementById('history-loading');
        const listDiv = document.getElementById('history-list');
        const emptyDiv = document.getElementById('history-empty');
        
        // 显示加载状态
        loadingDiv.style.display = 'block';
        listDiv.style.display = 'none';
        emptyDiv.style.display = 'none';
        
        try {
            const currentProject = localStorage.getItem('currentProject');
            if (!currentProject) {
                throw new Error('请先选择项目');
            }
            
            const response = await fetch(`/api/config_test/history?project=${encodeURIComponent(currentProject)}`);
            const data = await response.json();
            
            loadingDiv.style.display = 'none';
            
            if (data.success && data.records && data.records.length > 0) {
                renderHistoryRecords(data.records);
                listDiv.style.display = 'block';
            } else {
                emptyDiv.style.display = 'block';
            }
        } catch (error) {
            console.error('加载历史记录失败:', error);
            loadingDiv.style.display = 'none';
            emptyDiv.textContent = '加载失败: ' + error.message;
            emptyDiv.style.display = 'block';
        }
    }

    function renderHistoryRecords(records) {
        const listDiv = document.getElementById('history-list');
        listDiv.innerHTML = '';
        
        console.log('渲染历史记录，数量:', records.length);
        
        records.forEach((record, index) => {
            console.log(`历史记录 ${index}:`, record);
            
            const item = document.createElement('div');
            item.className = 'history-item';
            
            // 添加点击事件处理
            const taskId = record.task_id;
            console.log(`为历史记录 ${index} 绑定点击事件，Task ID:`, taskId);
            
            item.onclick = () => {
                console.log('历史记录项被点击，Task ID:', taskId);
                viewHistoryRecord(taskId);
            };
            
            const completedTime = record.completed_at ? new Date(record.completed_at).toLocaleString() : 'Unknown';
            const startTime = record.start_time ? new Date(record.start_time).toLocaleString() : 'Unknown';
            const endTime = record.end_time ? new Date(record.end_time).toLocaleString() : 'Unknown';
            
            item.innerHTML = `
                <div class="history-item-header">
                    <div class="history-item-title">${record.project_name || 'Unknown'} - ${record.branch || 'Unknown'}</div>
                    <div class="history-item-time">${completedTime}</div>
                </div>
                <div class="history-item-details">
                    <div class="history-item-detail">开始时间: <strong>${startTime}</strong></div>
                    <div class="history-item-detail">结束时间: <strong>${endTime}</strong></div>
                    <div class="history-item-detail">总文件: <strong>${record.total_files || 0}</strong></div>
                    <div class="history-item-detail">变更: <strong>${record.changed_files || 0}</strong></div>
                    <div class="history-item-detail">新增: <strong>${record.added_files || 0}</strong></div>
                    <div class="history-item-detail">删除: <strong>${record.deleted_files || 0}</strong></div>
                </div>
            `;
            
            listDiv.appendChild(item);
        });
        
        console.log('历史记录渲染完成');
    }

    async function viewHistoryRecord(taskId) {
        try {
            console.log('点击历史记录，Task ID:', taskId);
            
            if (!taskId) {
                alert('任务ID无效');
                return;
            }
            
            const currentProject = localStorage.getItem('currentProject');
            if (!currentProject) {
                alert('请先选择项目');
                return;
            }
            
            console.log('当前项目:', currentProject);
            
            // URL编码task_id，因为它可能包含斜杠等特殊字符
            const encodedTaskId = encodeURIComponent(taskId);
            const url = `/api/config_test/history/${encodedTaskId}?project=${encodeURIComponent(currentProject)}`;
            console.log('请求URL:', url);
            
            const response = await fetch(url);
            console.log('响应状态码:', response.status);
            
            const data = await response.json();
            console.log('响应数据:', data);
            
            if (data.success && data.result) {
                // 关闭历史弹窗
                closeHistoryModal();
                
                // 设置全局变量，标识当前显示的是历史记录
                window.currentHistoryTaskId = taskId;
                console.log('设置历史记录Task ID:', taskId);
                
                // 显示历史记录的结果
                updateCompareResultsTable(data.result);
                
                // 显示结果表格
                document.getElementById('compare-results').style.display = 'none';
                document.getElementById('compare-results-table').style.display = 'block';
                
                console.log('配置表测试页面：显示历史对比记录', taskId);
            } else {
                console.error('API返回错误:', data);
                alert('获取历史记录详情失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('获取历史记录详情失败:', error);
            alert('获取历史记录详情失败: ' + error.message);
        }
    }

    // 点击弹窗外部关闭
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('history-modal');
        if (event.target === modal) {
            closeHistoryModal();
        }
        
        const databaseModal = document.getElementById('database-modal');
        if (event.target === databaseModal) {
            closeDatabaseModal();
        }
    });

    // 数据库管理功能
    function openDatabaseModal() {
        const modal = document.getElementById('database-modal');
        modal.classList.add('show');
        
        // 加载数据库统计信息
        refreshDatabaseStats();
    }

    function closeDatabaseModal() {
        const modal = document.getElementById('database-modal');
        modal.classList.remove('show');
    }

    async function refreshDatabaseStats() {
        const loadingDiv = document.getElementById('database-stats-loading');
        const contentDiv = document.getElementById('database-stats-content');
        
        loadingDiv.style.display = 'block';
        contentDiv.style.display = 'none';
        
        try {
            const currentProject = localStorage.getItem('currentProject');
            if (!currentProject) {
                throw new Error('请先选择项目');
            }
            
            const response = await fetch(`/api/config_test/database/stats?project=${encodeURIComponent(currentProject)}`);
            const data = await response.json();
            
            loadingDiv.style.display = 'none';
            
            if (data.success && data.stats) {
                updateStatsDisplay(data.stats);
                contentDiv.style.display = 'block';
            } else {
                throw new Error(data.message || '获取统计信息失败');
            }
        } catch (error) {
            console.error('获取数据库统计失败:', error);
            loadingDiv.style.display = 'none';
            alert('获取数据库统计失败: ' + error.message);
        }
    }

    function updateStatsDisplay(stats) {
        const basicStats = stats.basic_stats || {};
        const spaceStats = stats.space_stats || {};
        const recommendations = stats.cleanup_recommendations || {};
        
        document.getElementById('total-records').textContent = basicStats.total_records || 0;
        document.getElementById('records-with-html').textContent = basicStats.records_with_html || 0;
        document.getElementById('records-last-7-days').textContent = basicStats.records_last_7_days || 0;
        document.getElementById('records-last-30-days').textContent = basicStats.records_last_30_days || 0;
        document.getElementById('table-size').textContent = spaceStats.total_table_size || '-';
        document.getElementById('estimated-savings').textContent = 
            recommendations.estimated_space_savings_mb > 0 ? 
            `${recommendations.estimated_space_savings_mb} MB` : '-';
        
        // 更新推荐的保留天数
        if (recommendations.recommended_html_retention_days) {
            document.getElementById('html-retention-days').value = recommendations.recommended_html_retention_days;
        }
        if (recommendations.recommended_metadata_retention_days) {
            document.getElementById('metadata-retention-days').value = recommendations.recommended_metadata_retention_days;
        }
    }

    async function executeCleanup() {
        const currentProject = localStorage.getItem('currentProject');
        if (!currentProject) {
            alert('请先选择项目');
            return;
        }
        
        const htmlRetentionDays = parseInt(document.getElementById('html-retention-days').value);
        const metadataRetentionDays = parseInt(document.getElementById('metadata-retention-days').value);
        
        if (htmlRetentionDays <= 0 || metadataRetentionDays <= 0) {
            alert('请输入有效的保留天数');
            return;
        }
        
        if (metadataRetentionDays <= htmlRetentionDays) {
            alert('完整记录保留天数应该大于HTML报告保留天数');
            return;
        }
        
        const confirmMessage = `确认执行数据库清理？\n\n` +
            `- HTML报告保留: ${htmlRetentionDays} 天\n` +
            `- 完整记录保留: ${metadataRetentionDays} 天\n\n` +
            `此操作不可逆，请确认！`;
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        const cleanupBtn = document.getElementById('cleanup-btn');
        const originalText = cleanupBtn.textContent;
        cleanupBtn.disabled = true;
        cleanupBtn.textContent = '⏳ 清理中...';
        
        try {
            const response = await fetch('/api/config_test/database/cleanup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    project: currentProject,
                    retention_config: {
                        html_reports_retention_days: htmlRetentionDays,
                        metadata_retention_days: metadataRetentionDays,
                        enable_cleanup: true
                    }
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showCleanupResult(data.result);
                // 刷新统计信息
                refreshDatabaseStats();
            } else {
                alert('清理失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('数据库清理失败:', error);
            alert('数据库清理失败: ' + error.message);
        } finally {
            cleanupBtn.disabled = false;
            cleanupBtn.textContent = originalText;
        }
    }

    function showCleanupResult(result) {
        const resultDiv = document.getElementById('cleanup-result');
        const contentDiv = document.getElementById('cleanup-result-content');
        
        const resultText = `清理完成！

项目: ${result.project}
清理时间: ${new Date(result.cleaned_at).toLocaleString()}

清理结果:
- HTML报告清理: ${result.html_reports_cleared} 条记录
- 完整记录删除: ${result.records_deleted} 条记录
- 剩余记录: ${result.remaining_records} 条
- 剩余带HTML记录: ${result.records_with_html} 条
- 当前表大小: ${result.table_size}
- 数据范围: ${result.date_range_days.toFixed(1)} 天

配置:
- HTML保留天数: ${result.retention_config.html_reports_retention_days} 天
- 元数据保留天数: ${result.retention_config.metadata_retention_days} 天`;
        
        contentDiv.textContent = resultText;
        resultDiv.style.display = 'block';
        
        // 滚动到结果区域
        resultDiv.scrollIntoView({ behavior: 'smooth' });
    }

    async function scheduleAutoCleanup() {
        const currentProject = localStorage.getItem('currentProject');
        if (!currentProject) {
            alert('请先选择项目');
            return;
        }
        
        const htmlRetentionDays = parseInt(document.getElementById('html-retention-days').value);
        const metadataRetentionDays = parseInt(document.getElementById('metadata-retention-days').value);
        
        try {
            const response = await fetch('/api/config_test/database/schedule_cleanup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    project: currentProject,
                    retention_config: {
                        html_reports_retention_days: htmlRetentionDays,
                        metadata_retention_days: metadataRetentionDays,
                        enable_cleanup: true
                    }
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert('定期清理任务已启动！\n每周会自动执行一次数据库清理。');
            } else {
                alert('启动定期清理失败: ' + (data.message || '未知错误'));
            }
        } catch (error) {
            console.error('启动定期清理失败:', error);
            alert('启动定期清理失败: ' + error.message);
        }
    }

    // 绑定数据库管理按钮事件
    document.addEventListener('DOMContentLoaded', function() {
        const databaseBtn = document.getElementById('database-management');
        if (databaseBtn) {
            databaseBtn.addEventListener('click', openDatabaseModal);
        }

        // 初始化日期选择器
        initDatePickers();
    });

    // 初始化Flatpickr日期选择器
    function initDatePickers() {
        // 加载Flatpickr脚本
        if (typeof flatpickr === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/flatpickr';
            script.onload = function() {
                setupDatePickers();
            };
            document.head.appendChild(script);

            // 加载中文语言包
            const langScript = document.createElement('script');
            langScript.src = 'https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js';
            langScript.onload = function() {
                if (window.flatpickr) {
                    flatpickr.localize(flatpickr.l10ns.zh);
                }
            };
            document.head.appendChild(langScript);
        } else {
            setupDatePickers();
        }
    }

    function setupDatePickers() {
        // 获取当前时间
        const now = new Date();
        const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        // 初始化开始时间选择器
        const startTimePicker = flatpickr("#start-time", {
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            time_24hr: true,
            defaultDate: oneWeekAgo,
            locale: "zh",
            clickOpens: true,
            allowInput: false,
            placeholder: "选择开始时间"
        });

        // 初始化结束时间选择器
        const endTimePicker = flatpickr("#end-time", {
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            time_24hr: true,
            defaultDate: now,
            locale: "zh",
            clickOpens: true,
            allowInput: false,
            placeholder: "选择结束时间"
        });

        console.log('日期选择器初始化完成');
    }

    // 测试进度条功能
    function testProgressBar() {
        console.log('开始测试进度条');
        showCompareProgress();

        let progress = 0;
        const messages = [
            '准备开始对比...',
            '正在获取文件列表...',
            '正在下载文件...',
            '正在转换Excel文件...',
            '正在执行对比...',
            '正在生成报告...',
            '对比完成！'
        ];

        const interval = setInterval(() => {
            const messageIndex = Math.floor(progress / 15);
            const message = messages[messageIndex] || messages[messages.length - 1];

            updateCompareProgress(progress, message);
            console.log(`测试进度: ${progress}% - ${message}`);

            progress += 5;

            if (progress > 100) {
                clearInterval(interval);
                setTimeout(() => {
                    hideCompareProgress();
                    console.log('进度条测试完成');
                }, 2000);
            }
        }, 200);
    }
</script>
{% endblock %}