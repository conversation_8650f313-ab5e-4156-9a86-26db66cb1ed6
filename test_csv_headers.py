#!/usr/bin/env python3
"""
测试CSV标题行配置
"""

import os
import sys
from pathlib import Path
sys.path.append('.')

def test_bcompare_script():
    """测试BCompare脚本配置"""
    print("测试BCompare脚本配置...")
    
    script_path = Path("config/compare_folder.txt")
    
    if not script_path.exists():
        print("❌ BCompare脚本文件不存在")
        return
    
    print("✅ BCompare脚本文件存在")
    
    # 读取脚本内容
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n=== 脚本内容 ===")
    print(content)
    
    # 检查关键配置
    checks = [
        ("criteria rules-based", "基础比较规则"),
        ("load %1 %2", "加载文件夹"),
        ("expand all", "展开所有子文件夹"),
        ("file-format csv header-row", "CSV标题行设置"),
        ("select all.diff", "选择差异文件"),
        ("file-report", "生成报告"),
        ("column-names", "显示列名"),
        ("html-color", "HTML彩色输出")
    ]
    
    print("\n=== 配置检查 ===")
    for check_text, description in checks:
        if check_text in content:
            print(f"✅ {description}: {check_text}")
        else:
            print(f"❌ {description}: {check_text} (未找到)")
    
    # 检查是否有多余的注释
    lines = content.split('\n')
    active_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]
    
    print(f"\n=== 脚本统计 ===")
    print(f"总行数: {len(lines)}")
    print(f"有效命令行数: {len(active_lines)}")
    print(f"注释行数: {len(lines) - len(active_lines)}")
    
    print(f"\n=== 有效命令 ===")
    for i, line in enumerate(active_lines, 1):
        print(f"{i}. {line}")

def test_config_cleanup():
    """测试配置文件清理"""
    print("\n=== 配置文件清理检查 ===")
    
    config_dir = Path("config")
    compare_files = list(config_dir.glob("compare_*.txt"))
    
    print(f"找到的compare文件: {len(compare_files)}")
    for file in compare_files:
        print(f"  {file.name}")
    
    if len(compare_files) == 1:
        print("✅ 配置文件清理成功，只保留一个主配置文件")
    else:
        print(f"❌ 仍有 {len(compare_files)} 个配置文件，建议只保留一个")

if __name__ == "__main__":
    test_bcompare_script()
    test_config_cleanup()
