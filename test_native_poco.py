"""
测试原生Poco功能的简单脚本
验证新添加的get_native_poco方法是否正常工作
"""

import sys
import os

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_import():
    """测试导入功能"""
    try:
        from services.airtest_func.PocoManager import PocoManager, SmartPoco
        from services.airtest_func.poco_unity import PocoUnity
        from services.airtest_func.poco_android import pocoAndroid
        print("✓ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ 模块导入失败: {str(e)}")
        return False

def test_method_existence():
    """测试方法是否存在"""
    try:
        from services.airtest_func.PocoManager import PocoManager, SmartPoco
        from services.airtest_func.poco_unity import PocoUnity
        from services.airtest_func.poco_android import pocoAndroid
        
        # 检查PocoManager是否有get_native_poco方法
        assert hasattr(PocoManager, 'get_native_poco'), "PocoManager缺少get_native_poco方法"
        print("✓ PocoManager.get_native_poco方法存在")
        
        # 检查SmartPoco是否有相关方法
        smart_poco = SmartPoco.__new__(SmartPoco)  # 创建实例但不调用__init__
        assert hasattr(smart_poco, 'get_native_poco'), "SmartPoco缺少get_native_poco方法"
        assert hasattr(smart_poco, 'get_native_unity_poco'), "SmartPoco缺少get_native_unity_poco方法"
        assert hasattr(smart_poco, 'get_native_android_poco'), "SmartPoco缺少get_native_android_poco方法"
        print("✓ SmartPoco相关方法存在")
        
        # 检查PocoUnity是否有get_native_poco方法
        poco_unity = PocoUnity.__new__(PocoUnity)  # 创建实例但不调用__init__
        assert hasattr(poco_unity, 'get_native_poco'), "PocoUnity缺少get_native_poco方法"
        print("✓ PocoUnity.get_native_poco方法存在")
        
        # 检查pocoAndroid是否有get_native_poco方法
        poco_android = pocoAndroid.__new__(pocoAndroid)  # 创建实例但不调用__init__
        assert hasattr(poco_android, 'get_native_poco'), "pocoAndroid缺少get_native_poco方法"
        print("✓ pocoAndroid.get_native_poco方法存在")
        
        return True
    except Exception as e:
        print(f"✗ 方法存在性检查失败: {str(e)}")
        return False

def test_method_signatures():
    """测试方法签名"""
    try:
        from services.airtest_func.PocoManager import PocoManager
        import inspect
        
        # 检查PocoManager.get_native_poco的签名
        sig = inspect.signature(PocoManager.get_native_poco)
        params = list(sig.parameters.keys())
        expected_params = ['device_id', 'poco_type', 'kwargs']
        
        # 检查参数是否包含预期的参数
        for param in expected_params[:-1]:  # kwargs是**kwargs，不在parameters中显示为'kwargs'
            assert param in params, f"PocoManager.get_native_poco缺少参数: {param}"
        
        print("✓ PocoManager.get_native_poco方法签名正确")
        return True
    except Exception as e:
        print(f"✗ 方法签名检查失败: {str(e)}")
        return False

def test_documentation():
    """测试文档字符串"""
    try:
        from services.airtest_func.PocoManager import PocoManager, SmartPoco
        from services.airtest_func.poco_unity import PocoUnity
        from services.airtest_func.poco_android import pocoAndroid
        
        # 检查文档字符串是否存在
        assert PocoManager.get_native_poco.__doc__ is not None, "PocoManager.get_native_poco缺少文档"
        assert "原生" in PocoManager.get_native_poco.__doc__, "PocoManager.get_native_poco文档不包含'原生'"
        print("✓ PocoManager.get_native_poco文档存在")
        
        # 创建实例检查实例方法文档
        poco_unity = PocoUnity.__new__(PocoUnity)
        assert poco_unity.get_native_poco.__doc__ is not None, "PocoUnity.get_native_poco缺少文档"
        print("✓ PocoUnity.get_native_poco文档存在")
        
        poco_android = pocoAndroid.__new__(pocoAndroid)
        assert poco_android.get_native_poco.__doc__ is not None, "pocoAndroid.get_native_poco缺少文档"
        print("✓ pocoAndroid.get_native_poco文档存在")
        
        return True
    except Exception as e:
        print(f"✗ 文档检查失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试原生Poco功能...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_import),
        ("方法存在性测试", test_method_existence),
        ("方法签名测试", test_method_signatures),
        ("文档测试", test_documentation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行 {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！原生Poco功能添加成功！")
        print("\n使用方法:")
        print("1. 通过PocoManager获取原生poco:")
        print("   native_poco = PocoManager.get_native_poco(device_id='your_device', poco_type='unity')")
        print("   native_poco('SR_Canvas').offspring('SR_Content').child('Category(Clone)')[1].click()")
        print("\n2. 通过实例获取原生poco:")
        print("   unity_poco = PocoManager.get_poco(device_id='your_device', poco_type='unity')")
        print("   native_poco = unity_poco.get_native_poco()")
        print("   native_poco(text='卡包Id').click()")
        print("\n3. 通过SmartPoco获取原生poco:")
        print("   smart_poco = SmartPoco(device_id='your_device')")
        print("   native_poco = smart_poco.get_native_poco('game')")
        print("   native_poco('StartButton').click()")
    else:
        print("❌ 部分测试失败，请检查代码实现")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
