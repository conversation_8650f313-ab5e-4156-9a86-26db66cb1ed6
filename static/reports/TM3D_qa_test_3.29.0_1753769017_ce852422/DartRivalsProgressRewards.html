<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
.Wrap { white-space: nowrap; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.DataItemHeader { color: #000000; background-color: #FFFFFF; background-color: #E7E7E7; padding-top: 8px; }
td.DataItemInsigDiff { color: #000000; background-color: #EEEEFF; }
td.DataItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.DataItemNum { color: #696969; background-color: #F0F0F0; }
td.DataItemSame { color: #000000; background-color: #FFFFFF; }
td.DataItemSigDiff { color: #000000; background-color: #FFE3E3; }
td.DataItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.DataSegInsigDiff { color: #0000FF; }
.DataSegSigDiff { color: #FF0000; }
td.DataItemInsigDiffError { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.DataItemInsigOrphanError { color: #000000; background-color: #FAEEFF; text-decoration: line-through; }
td.DataItemSameError { color: #000000; background-color: #FFFFFF; text-decoration: line-through; }
td.DataItemSigDiffError { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
td.DataItemSigOrphanError { color: #000000; background-color: #F1E3FF; text-decoration: line-through; }
</style>
<title>DartRivalsProgressRewards</title>
</head>
<body>
DartRivalsProgressRewards<br>
Produced: 2025/7/29 14:05:04<br>
&nbsp; &nbsp;
<br>
Mode:&nbsp; Differences &nbsp;
<br>
Left base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgressRewards\old &nbsp;
<br>
Right base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgressRewards\new &nbsp;
<br>
&nbsp; &nbsp;
<br>
File: DartRivalsProgressRewards_DartRivalsProgressRewards.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">&nbsp;</td>
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">10</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">3</span>,<span class="DataSegSigDiff">30</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">3</span>,<span class="DataSegSigDiff">30</span></td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">10</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">4</span>,<span class="DataSegSigDiff">15</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">4</span>,<span class="DataSegSigDiff">15</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">11</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>00<span class="DataSegSigDiff">03</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>0003,1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">11</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">8</span>0<span class="DataSegSigDiff">2</span>0<span class="DataSegSigDiff">2</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>0003,1<span class="DataSegSigDiff">5</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">12</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">8</span>0<span class="DataSegSigDiff">2</span>03,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">1</span>,1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">12</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">30</span>003,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">3</span>,1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">13</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">4</span>,<span class="DataSegSigDiff">30</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">4</span>,<span class="DataSegSigDiff">30</span></td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">13</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">2</span>,<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">2</span>,<span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">14</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">1</span>,<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">1</span>,<span class="DataSegSigDiff">1</span></td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">14</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">3</span>,<span class="DataSegSigDiff">30</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">3</span>,<span class="DataSegSigDiff">30</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">15</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>00<span class="DataSegSigDiff">02</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">2</span>,1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">15</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">8</span>0<span class="DataSegSigDiff">2</span>0<span class="DataSegSigDiff">3</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">4</span>,1<span class="DataSegSigDiff">5</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">16</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">1</span>,5<span class="DataSegSigDiff">00</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">1</span>,5<span class="DataSegSigDiff">00</span></td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">16</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">3</span>,<span class="DataSegSigDiff">1</span>5</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">3</span>,<span class="DataSegSigDiff">1</span>5</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">18</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">8</span>0<span class="DataSegSigDiff">2</span>0<span class="DataSegSigDiff">4</span>,<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>0003,30</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">18</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">10</span>00<span class="DataSegSigDiff">3</span>,<span class="DataSegSigDiff">30</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>0003,30</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">19</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>00<span class="DataSegSigDiff">03</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">3</span>,1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">19</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">8</span>0<span class="DataSegSigDiff">2</span>0<span class="DataSegSigDiff">4</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">2</span>,1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">20</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">1</span>,<span class="DataSegSigDiff">1000</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">1</span>,<span class="DataSegSigDiff">1000</span></td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">20</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">3</span>,<span class="DataSegSigDiff">3</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">3</span>,<span class="DataSegSigDiff">3</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">3</td>
<td class="DataItemSigDiff AlignLeft Wrap">80201,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">2</span>,1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">3</td>
<td class="DataItemSigDiff AlignLeft Wrap">80201,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">3</span>,1<span class="DataSegSigDiff">5</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">5</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">1</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">1</span>,1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">5</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">2</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">2</span>,1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">6</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">2</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">2</span>,1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">6</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">4</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">3000<span class="DataSegSigDiff">4</span>,1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">7</td>
<td class="DataItemSigDiff AlignLeft Wrap">20<span class="DataSegSigDiff">003</span>,<span class="DataSegSigDiff">30</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">20003,<span class="DataSegSigDiff">30</span></td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">7</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">80</span>20<span class="DataSegSigDiff">2</span>,<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">20003,<span class="DataSegSigDiff">15</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">8</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">8</span>0<span class="DataSegSigDiff">2</span>0<span class="DataSegSigDiff">2</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">2</span>,1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">8</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">10</span>00<span class="DataSegSigDiff">3</span>,1<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span>000<span class="DataSegSigDiff">3</span>,1<span class="DataSegSigDiff">5</span></td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSigDiff AlignLeft Wrap">9</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">4</span>,1</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span>000<span class="DataSegSigDiff">4</span>,1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">9</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">3</span>,1<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span>000<span class="DataSegSigDiff">3</span>,1<span class="DataSegSigDiff">5</span></td>
</tr>
</table>
<br>
</body>
</html>
