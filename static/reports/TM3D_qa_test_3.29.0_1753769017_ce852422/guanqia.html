<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
.Wrap { white-space: nowrap; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.DataItemHeader { color: #000000; background-color: #FFFFFF; background-color: #E7E7E7; padding-top: 8px; }
td.DataItemInsigDiff { color: #000000; background-color: #EEEEFF; }
td.DataItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.DataItemNum { color: #696969; background-color: #F0F0F0; }
td.DataItemSame { color: #000000; background-color: #FFFFFF; }
td.DataItemSigDiff { color: #000000; background-color: #FFE3E3; }
td.DataItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.DataSegInsigDiff { color: #0000FF; }
.DataSegSigDiff { color: #FF0000; }
td.DataItemInsigDiffError { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.DataItemInsigOrphanError { color: #000000; background-color: #FAEEFF; text-decoration: line-through; }
td.DataItemSameError { color: #000000; background-color: #FFFFFF; text-decoration: line-through; }
td.DataItemSigDiffError { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
td.DataItemSigOrphanError { color: #000000; background-color: #F1E3FF; text-decoration: line-through; }
</style>
<title>guanqia</title>
</head>
<body>
guanqia<br>
Produced: 2025/7/29 14:05:07<br>
&nbsp; &nbsp;
<br>
Mode:&nbsp; Differences &nbsp;
<br>
Left base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753769017_ce852422\guanqia\old &nbsp;
<br>
Right base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753769017_ce852422\guanqia\new &nbsp;
<br>
&nbsp; &nbsp;
<br>
File: guanqia_guanqia.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">&nbsp;</td>
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6031</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">197401,197402,197403,197404,197405,197406</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6032</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">197407,197408,197409,197410,260502,260503</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6033</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">260504,260506,260507,260508,260509,260510</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6034</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">261502,261503,261504,261506,261507,261508</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6035</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">261509,261510,261802,261803,261804,261806</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6036</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">261807,261808,261809,261810,262101,262103</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6037</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">262105,262106,262107,262108,262109,262110</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6038</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">262702,262703,262704,262706,262707,262708</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6039</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">262709,262710,264301,264303,264305,264306</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6040</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">264307,264308,264309,264310,265101,265302</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6041</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">265303,265304,265306,265307,265308,265309</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6042</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">265310,265601,265603,265604,265606,265607</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">6043</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">265608,265609,265610</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
</tr>
</table>
<br>
</body>
</html>
