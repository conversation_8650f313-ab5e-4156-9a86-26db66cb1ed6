<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
.Wrap { white-space: nowrap; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.DataItemHeader { color: #000000; background-color: #FFFFFF; background-color: #E7E7E7; padding-top: 8px; }
td.DataItemInsigDiff { color: #000000; background-color: #EEEEFF; }
td.DataItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.DataItemNum { color: #696969; background-color: #F0F0F0; }
td.DataItemSame { color: #000000; background-color: #FFFFFF; }
td.DataItemSigDiff { color: #000000; background-color: #FFE3E3; }
td.DataItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.DataSegInsigDiff { color: #0000FF; }
.DataSegSigDiff { color: #FF0000; }
td.DataItemInsigDiffError { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.DataItemInsigOrphanError { color: #000000; background-color: #FAEEFF; text-decoration: line-through; }
td.DataItemSameError { color: #000000; background-color: #FFFFFF; text-decoration: line-through; }
td.DataItemSigDiffError { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
td.DataItemSigOrphanError { color: #000000; background-color: #F1E3FF; text-decoration: line-through; }
</style>
<title>MatchModelV2</title>
</head>
<body>
MatchModelV2<br>
Produced: 2025/7/29 14:21:46<br>
&nbsp; &nbsp;
<br>
Mode:&nbsp; Differences &nbsp;
<br>
Left base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753770011_264d24ac\MatchModelV2\old &nbsp;
<br>
Right base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753770011_264d24ac\MatchModelV2\new &nbsp;
<br>
&nbsp; &nbsp;
<br>
File: MatchModelV2_MatchModelV2.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader Wrap">1: 索引</td>
<td class="DataItemHeader Wrap">2: 原始名</td>
<td class="DataItemHeader Wrap">3: 一级干扰物</td>
<td class="DataItemHeader Wrap">4: 二级干扰物</td>
<td class="DataItemHeader Wrap">5: fbx换色</td>
<td class="DataItemHeader Wrap">6: 一级干扰物同关卡出现</td>
<td class="DataItemHeader Wrap">7: 描述</td>
<td class="DataItemHeader Wrap">8: fbx名称</td>
<td class="DataItemHeader Wrap">9: 模型名字</td>
<td class="DataItemHeader Wrap">10: 贴图尺寸</td>
<td class="DataItemHeader Wrap">11: 开启等级</td>
<td class="DataItemHeader Wrap">12: 模型类型</td>
<td class="DataItemHeader Wrap">13: 道具类型</td>
<td class="DataItemHeader Wrap">14: 投放主题</td>
<td class="DataItemHeader Wrap">15: 二级主题</td>
<td class="DataItemHeader Wrap">16: 一级主题</td>
<td class="DataItemHeader Wrap">17: Category1</td>
<td class="DataItemHeader Wrap">18: Category2</td>
<td class="DataItemHeader Wrap">19: 尺寸</td>
<td class="DataItemHeader Wrap">20: 尺寸标签</td>
<td class="DataItemHeader Wrap">21: 一二级混淆颜色</td>
<td class="DataItemHeader Wrap">22: 第1大面积颜色</td>
<td class="DataItemHeader Wrap">23: Color1</td>
<td class="DataItemHeader Wrap">24: Color2</td>
<td class="DataItemHeader Wrap">25: 形状</td>
<td class="DataItemHeader Wrap">26: 缩放比例</td>
<td class="DataItemHeader Wrap">27: 收集栏旋转角度</td>
<td class="DataItemHeader Wrap">28: 收集栏缩放系数</td>
<td class="DataItemHeader Wrap">29: 收集栏偏移轴</td>
<td class="DataItemHeader Wrap">30: 目标图旋转角度</td>
<td class="DataItemHeader Wrap">31: 目标图缩放比例</td>
<td class="DataItemHeader Wrap">32: 面数</td>
<td class="DataItemHeader Wrap">33: 生成初始角度x</td>
<td class="DataItemHeader Wrap">34: 生成初始角度z</td>
<td class="DataItemHeader Wrap">35: fbx_ref</td>
<td class="DataItemHeader Wrap">36: 生成初始角度new</td>
<td class="DataItemHeader Wrap">37: status</td>
<td class="DataItemHeader Wrap">38: colorNumber</td>
<td class="DataItemHeader Wrap">39: colorMain</td>
<td class="DataItemHeader Wrap">40: 换色面积检查</td>
<td class="DataItemHeader Wrap">41: 合并fbx</td>
<td class="DataItemHeader Wrap">42: 开启等级检查</td>
<td class="DataItemHeader Wrap">&nbsp;</td>
<td class="DataItemHeader Wrap">1: 索引</td>
<td class="DataItemHeader Wrap">2: 原始名</td>
<td class="DataItemHeader Wrap">3: 一级干扰物</td>
<td class="DataItemHeader Wrap">4: 二级干扰物</td>
<td class="DataItemHeader Wrap">5: fbx换色</td>
<td class="DataItemHeader Wrap">6: 一级干扰物同关卡出现</td>
<td class="DataItemHeader Wrap">7: 描述</td>
<td class="DataItemHeader Wrap">8: fbx名称</td>
<td class="DataItemHeader Wrap">9: 模型名字</td>
<td class="DataItemHeader Wrap">10: 贴图尺寸</td>
<td class="DataItemHeader Wrap">11: 开启等级</td>
<td class="DataItemHeader Wrap">12: 模型类型</td>
<td class="DataItemHeader Wrap">13: 道具类型</td>
<td class="DataItemHeader Wrap">14: 投放主题</td>
<td class="DataItemHeader Wrap">15: 二级主题</td>
<td class="DataItemHeader Wrap">16: 一级主题</td>
<td class="DataItemHeader Wrap">17: Category1</td>
<td class="DataItemHeader Wrap">18: Category2</td>
<td class="DataItemHeader Wrap">19: 尺寸</td>
<td class="DataItemHeader Wrap">20: 尺寸标签</td>
<td class="DataItemHeader Wrap">21: 一二级混淆颜色</td>
<td class="DataItemHeader Wrap">22: 第1大面积颜色</td>
<td class="DataItemHeader Wrap">23: Color1</td>
<td class="DataItemHeader Wrap">24: Color2</td>
<td class="DataItemHeader Wrap">25: 形状</td>
<td class="DataItemHeader Wrap">26: 缩放比例</td>
<td class="DataItemHeader Wrap">27: 收集栏旋转角度</td>
<td class="DataItemHeader Wrap">28: 收集栏缩放系数</td>
<td class="DataItemHeader Wrap">29: 收集栏偏移轴</td>
<td class="DataItemHeader Wrap">30: 目标图旋转角度</td>
<td class="DataItemHeader Wrap">31: 目标图缩放比例</td>
<td class="DataItemHeader Wrap">32: 面数</td>
<td class="DataItemHeader Wrap">33: 生成初始角度x</td>
<td class="DataItemHeader Wrap">34: 生成初始角度z</td>
<td class="DataItemHeader Wrap">35: fbx_ref</td>
<td class="DataItemHeader Wrap">36: 生成初始角度new</td>
<td class="DataItemHeader Wrap">37: status</td>
<td class="DataItemHeader Wrap">38: colorNumber</td>
<td class="DataItemHeader Wrap">39: colorMain</td>
<td class="DataItemHeader Wrap">40: 换色面积检查</td>
<td class="DataItemHeader Wrap">41: 合并fbx</td>
<td class="DataItemHeader Wrap">42: 开启等级检查</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197401</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1blue3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197401</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1blue3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197402</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1brown3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197402</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1brown3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197403</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">green</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1green3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197403</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">green</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1green3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197404</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1purple3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197404</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1purple3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197405</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">red</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1red3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197405</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">red</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1red3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197406</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1pink3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197406</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1pink3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197407</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1yellow3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197407</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1yellow3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197408</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1orange3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197408</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1orange3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197409</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">green</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1blue3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197409</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">green</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1blue3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">197410</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_grey1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">grey1</td>
<td class="DataItemSigDiff AlignLeft Wrap">grey1</td>
<td class="DataItemSigDiff AlignLeft Wrap">grey</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3,1.3,1.3</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1blue3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">197410</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">行李箱大1luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1_grey1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">large</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">grey1</td>
<td class="DataItemSigDiff AlignLeft Wrap">grey1</td>
<td class="DataItemSigDiff AlignLeft Wrap">grey</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">cube</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span>,1.3<span class="DataSegSigDiff">5</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,170,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.133,1.133,1.133</td>
<td class="DataItemSigDiff AlignLeft Wrap">2522</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">XL</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">new</td>
<td class="DataItemSigDiff AlignLeft Wrap">largecube</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1blue3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">luggageAdult1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">265601</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1blue3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265601</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue1</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1blue3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">265602</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_blue2</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">99999</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue2</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue2</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1blue99999</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265602</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_blue2</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">99999</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue2</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue2</td>
<td class="DataItemSigDiff AlignLeft Wrap">blue</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1blue99999</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">265603</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1brown3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265603</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown1</td>
<td class="DataItemSigDiff AlignLeft Wrap">brown</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1brown3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">265604</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">green</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1green3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265604</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">green1</td>
<td class="DataItemSigDiff AlignLeft Wrap">green</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1green3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">265605</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">99999</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">green</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1green99999</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265605</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">99999</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">green2</td>
<td class="DataItemSigDiff AlignLeft Wrap">green</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1green99999</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">265606</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1orange3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265606</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange1</td>
<td class="DataItemSigDiff AlignLeft Wrap">orange</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1orange3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">265607</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1pink3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265607</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink1</td>
<td class="DataItemSigDiff AlignLeft Wrap">pink</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1pink3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">265608</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1purple3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265608</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple1</td>
<td class="DataItemSigDiff AlignLeft Wrap">purple</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1purple3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">265609</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">red</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1red3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265609</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">red1</td>
<td class="DataItemSigDiff AlignLeft Wrap">red</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1red3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSigDiff AlignLeft Wrap">265610</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span>,1.<span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1yellow3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">265610</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">L</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1_yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">256</td>
<td class="DataItemSigDiff AlignLeft Wrap">1600</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">airport</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">medium</td>
<td class="DataItemSigDiff AlignLeft Wrap">M</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">yellow</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">circle</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span>,1.<span class="DataSegSigDiff">06</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">15,<span class="DataSegSigDiff">1</span>70,-2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">15,190,2</td>
<td class="DataItemSigDiff AlignLeft Wrap">1.045,1.045,1.045</td>
<td class="DataItemSigDiff AlignLeft Wrap">780</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">25,45</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">mediumcircle</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1yellow3000</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">travelPillow1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
</tr>
</table>
<br>
</body>
</html>
