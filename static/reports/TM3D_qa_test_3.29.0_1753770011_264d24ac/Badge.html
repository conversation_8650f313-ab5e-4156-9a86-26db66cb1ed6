<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
.Wrap { white-space: nowrap; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.DataItemHeader { color: #000000; background-color: #FFFFFF; background-color: #E7E7E7; padding-top: 8px; }
td.DataItemInsigDiff { color: #000000; background-color: #EEEEFF; }
td.DataItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.DataItemNum { color: #696969; background-color: #F0F0F0; }
td.DataItemSame { color: #000000; background-color: #FFFFFF; }
td.DataItemSigDiff { color: #000000; background-color: #FFE3E3; }
td.DataItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.DataSegInsigDiff { color: #0000FF; }
.DataSegSigDiff { color: #FF0000; }
td.DataItemInsigDiffError { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.DataItemInsigOrphanError { color: #000000; background-color: #FAEEFF; text-decoration: line-through; }
td.DataItemSameError { color: #000000; background-color: #FFFFFF; text-decoration: line-through; }
td.DataItemSigDiffError { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
td.DataItemSigOrphanError { color: #000000; background-color: #F1E3FF; text-decoration: line-through; }
</style>
<title>Badge</title>
</head>
<body>
Badge<br>
Produced: 2025/7/29 14:21:26<br>
&nbsp; &nbsp;
<br>
Mode:&nbsp; Differences &nbsp;
<br>
Left base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753770011_264d24ac\Badge\old &nbsp;
<br>
Right base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753770011_264d24ac\Badge\new &nbsp;
<br>
&nbsp; &nbsp;
<br>
File: Badge_Badge.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
<td class="DataItemHeader Wrap">7:</td>
<td class="DataItemHeader Wrap">8:</td>
<td class="DataItemHeader Wrap">9:</td>
<td class="DataItemHeader Wrap">10:</td>
<td class="DataItemHeader Wrap">&nbsp;</td>
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
<td class="DataItemHeader Wrap">7:</td>
<td class="DataItemHeader Wrap">8:</td>
<td class="DataItemHeader Wrap">9:</td>
<td class="DataItemHeader Wrap">10:</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">7</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">common_badge5_1</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">season9_badge1</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">common_badge5_3</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">common_badge5_4</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">MapleLeaf</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">BadgeItem_GoldenMapleLeaf</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">MapleLeaf</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">BadgeModel_GoldenMapleLeaf</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">GoldenFlowerv1_Glow</span></td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">8</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">common_badge5_2</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">season9_badge2</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">common_badge5_3_2</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">common_badge5_4_2</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">MapleLeaf</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">BadgeItem_ColourMapleLeaf</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">MapleLeaf</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">BadgeModel_ColorMapleLeaf</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">GoldenFlowerv2_Glow</span></td>
</tr>
</table>
<br>
</body>
</html>
