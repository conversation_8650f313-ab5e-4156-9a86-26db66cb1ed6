<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
.Wrap { white-space: nowrap; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.DataItemHeader { color: #000000; background-color: #FFFFFF; background-color: #E7E7E7; padding-top: 8px; }
td.DataItemInsigDiff { color: #000000; background-color: #EEEEFF; }
td.DataItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.DataItemNum { color: #696969; background-color: #F0F0F0; }
td.DataItemSame { color: #000000; background-color: #FFFFFF; }
td.DataItemSigDiff { color: #000000; background-color: #FFE3E3; }
td.DataItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.DataSegInsigDiff { color: #0000FF; }
.DataSegSigDiff { color: #FF0000; }
td.DataItemInsigDiffError { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.DataItemInsigOrphanError { color: #000000; background-color: #FAEEFF; text-decoration: line-through; }
td.DataItemSameError { color: #000000; background-color: #FFFFFF; text-decoration: line-through; }
td.DataItemSigDiffError { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
td.DataItemSigOrphanError { color: #000000; background-color: #F1E3FF; text-decoration: line-through; }
</style>
<title>safe_compare</title>
</head>
<body>
safe_compare<br>
Produced: 2025/7/29 14:21:54<br>
&nbsp; &nbsp;
<br>
Mode:&nbsp; Differences &nbsp;
<br>
Left base folder: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old &nbsp;
<br>
Right base folder: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new &nbsp;
<br>
<br>
&nbsp; &nbsp;
<br>
File: Toy_Match_3D_埋点方案_num公共事件属性.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">&nbsp;</td>
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="AlignCenter Wrap">-+</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">g_battle_record</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">字符串</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">战斗状态记录</span></td>
</tr>
</table>
<br>
&nbsp; &nbsp;
<br>
File: Toy_Match_3D_埋点方案_num事件数据.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
<td class="DataItemHeader Wrap">7:</td>
<td class="DataItemHeader Wrap">8:</td>
<td class="DataItemHeader Wrap">&nbsp;</td>
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
<td class="DataItemHeader Wrap">7:</td>
<td class="DataItemHeader Wrap">8:</td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSigDiff AlignLeft Wrap">g_login</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">登陆打点</td>
<td class="DataItemSigDiff AlignLeft Wrap">client</td>
<td class="DataItemSigDiff AlignLeft Wrap">g_login_process</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">字符串</td>
<td class="DataItemSigDiff AlignLeft Wrap">start:发起登录; login_success:登录成功; register_success:注册成功; fail:登录失败,每次尝试失败都需要打点; delay:超时; delayed_success:超时登录成功; account_change:更换账号; remote_start:开始拉取远程存档; remote_success:拉取远程存档成功; remote_fail:拉取远程存档失败;</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">g_login</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">登陆打点</td>
<td class="DataItemSigDiff AlignLeft Wrap">client</td>
<td class="DataItemSigDiff AlignLeft Wrap">g_login_process</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">字符串</td>
<td class="DataItemSigDiff AlignLeft Wrap">start:发起登录; login_success:登录成功; register_success:注册成功; fail:登录失败,每次尝试失败都需要打点; delay:超时; delayed_success:超时登录成功; account_change:更换账号; remote_start:开始拉取远程存档; remote_success:拉取远程存档成功; remote_fail:拉取远程存档失败;<span class="DataSegSigDiff"> </span><span class="DataSegSigDiff">account_enter_healthy:账号系统进入健康状态;</span><span class="DataSegSigDiff"> </span><span class="DataSegSigDiff">account_exit_healthy:账号系统退出健康状态;</span></td>
</tr>
</table>
<br>
</body>
</html>
