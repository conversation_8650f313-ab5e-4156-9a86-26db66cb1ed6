<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.DataItemHeader { color: #000000; background-color: #FFFFFF; background-color: #E7E7E7; padding-top: 8px; }
td.DataItemInsigDiff { color: #000000; background-color: #EEEEFF; }
td.DataItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.DataItemNum { color: #696969; background-color: #F0F0F0; }
td.DataItemSame { color: #000000; background-color: #FFFFFF; }
td.DataItemSigDiff { color: #000000; background-color: #FFE3E3; }
td.DataItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.DataSegInsigDiff { color: #0000FF; }
.DataSegSigDiff { color: #FF0000; }
td.DataItemInsigDiffError { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.DataItemInsigOrphanError { color: #000000; background-color: #FAEEFF; text-decoration: line-through; }
td.DataItemSameError { color: #000000; background-color: #FFFFFF; text-decoration: line-through; }
td.DataItemSigDiffError { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
td.DataItemSigOrphanError { color: #000000; background-color: #F1E3FF; text-decoration: line-through; }
</style>
<title>DartRivalsProgress</title>
</head>
<body>
DartRivalsProgress<br>
Produced: 2025/7/25 14:56:36<br>
&nbsp; &nbsp;
<br>
Mode:&nbsp; Differences &nbsp;
<br>
Left base folder: D:\git\qa_tools\services\config_test\TM3D_hotfix\v3.28.1_1753426594_1485b500\DartRivalsProgress\old &nbsp;
<br>
Right base folder: D:\git\qa_tools\services\config_test\TM3D_hotfix\v3.28.1_1753426594_1485b500\DartRivalsProgress\new &nbsp;
<br>
&nbsp; &nbsp;
<br>
File: DartRivalsProgress_DartRivalsProgress.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader">1:</td>
<td class="DataItemHeader">2:</td>
<td class="DataItemHeader">&nbsp;</td>
<td class="DataItemHeader">1:</td>
<td class="DataItemHeader">2:</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft">0</td>
<td class="DataItemSigDiff AlignLeft">1,2,4,6,10,10,<span class="DataSegSigDiff">1</span>0,<span class="DataSegSigDiff">1</span>0,<span class="DataSegSigDiff">1</span>0,<span class="DataSegSigDiff">1</span>0,<span class="DataSegSigDiff">1</span>0,<span class="DataSegSigDiff">2</span>0,<span class="DataSegSigDiff">2</span>0,<span class="DataSegSigDiff">3</span>0,<span class="DataSegSigDiff">2</span>0,<span class="DataSegSigDiff">2</span>0,40,50,<span class="DataSegSigDiff">20,7</span>0</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft">0</td>
<td class="DataItemSigDiff AlignLeft">1,2,4,6,10,10,<span class="DataSegSigDiff">30,2</span>0,<span class="DataSegSigDiff">3</span>0,<span class="DataSegSigDiff">2</span>0,<span class="DataSegSigDiff">3</span>0,<span class="DataSegSigDiff">4</span>0,<span class="DataSegSigDiff">5</span>0,<span class="DataSegSigDiff">8</span>0,<span class="DataSegSigDiff">5</span>0,<span class="DataSegSigDiff">6</span>0,<span class="DataSegSigDiff">10</span>0,<span class="DataSegSigDiff">1</span>40,50,<span class="DataSegSigDiff">19</span>0</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft">1</td>
<td class="DataItemSigDiff AlignLeft">1,2,4,6,10,10,<span class="DataSegSigDiff">20,1</span>0,20,<span class="DataSegSigDiff">1</span>0,20,<span class="DataSegSigDiff">20,</span>30,40,<span class="DataSegSigDiff">3</span>0,<span class="DataSegSigDiff">3</span>0,50,<span class="DataSegSigDiff">7</span>0,<span class="DataSegSigDiff">3</span>0,1<span class="DataSegSigDiff">0</span>0</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft">1</td>
<td class="DataItemSigDiff AlignLeft">1,2,4,6,10,10,<span class="DataSegSigDiff">3</span>0,20,<span class="DataSegSigDiff">3</span>0,20,30,40,<span class="DataSegSigDiff">5</span>0,<span class="DataSegSigDiff">8</span>0,50<span class="DataSegSigDiff">,60</span>,<span class="DataSegSigDiff">100,14</span>0,<span class="DataSegSigDiff">5</span>0,1<span class="DataSegSigDiff">9</span>0</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft">2</td>
<td class="DataItemSigDiff AlignLeft">1,2,4,6,10,10,<span class="DataSegSigDiff">2</span>0,20,<span class="DataSegSigDiff">2</span>0,20,<span class="DataSegSigDiff">20,</span>30,40,50,<span class="DataSegSigDiff">4</span>0,<span class="DataSegSigDiff">4</span>0,<span class="DataSegSigDiff">7</span>0,100,<span class="DataSegSigDiff">4</span>0,1<span class="DataSegSigDiff">3</span>0</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft">2</td>
<td class="DataItemSigDiff AlignLeft">1,2,4,6,10,10,<span class="DataSegSigDiff">3</span>0,20,<span class="DataSegSigDiff">3</span>0,20,30,40,50,<span class="DataSegSigDiff">8</span>0,<span class="DataSegSigDiff">5</span>0,<span class="DataSegSigDiff">6</span>0,100<span class="DataSegSigDiff">,140</span>,<span class="DataSegSigDiff">5</span>0,1<span class="DataSegSigDiff">9</span>0</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft">4</td>
<td class="DataItemSigDiff AlignLeft">1,6,<span class="DataSegSigDiff">16</span>,20,<span class="DataSegSigDiff">2</span>0,20,<span class="DataSegSigDiff">40,</span>30,40,<span class="DataSegSigDiff">3</span>0,<span class="DataSegSigDiff">4</span>0,50,<span class="DataSegSigDiff">7</span>0,100,<span class="DataSegSigDiff">7</span>0,<span class="DataSegSigDiff">80,13</span>0,190<span class="DataSegSigDiff">,70,250</span></td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft">4</td>
<td class="DataItemSigDiff AlignLeft">1,<span class="DataSegSigDiff">2,4,</span>6<span class="DataSegSigDiff">,10,10</span>,<span class="DataSegSigDiff">30</span>,20,<span class="DataSegSigDiff">3</span>0,20,30,40,<span class="DataSegSigDiff">5</span>0,<span class="DataSegSigDiff">8</span>0,50,<span class="DataSegSigDiff">6</span>0,100,<span class="DataSegSigDiff">14</span>0,<span class="DataSegSigDiff">5</span>0,190</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft">5</td>
<td class="DataItemSigDiff AlignLeft">1,6,1<span class="DataSegSigDiff">6</span>,<span class="DataSegSigDiff">2</span>0,<span class="DataSegSigDiff">2</span>0,20,<span class="DataSegSigDiff">6</span>0,<span class="DataSegSigDiff">4</span>0,<span class="DataSegSigDiff">6</span>0,40,<span class="DataSegSigDiff">6</span>0,80,<span class="DataSegSigDiff">10</span>0,<span class="DataSegSigDiff">15</span>0,100,<span class="DataSegSigDiff">12</span>0,190<span class="DataSegSigDiff">,280,100,380</span></td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft">5</td>
<td class="DataItemSigDiff AlignLeft">1,<span class="DataSegSigDiff">2,4,</span>6,1<span class="DataSegSigDiff">0</span>,<span class="DataSegSigDiff">1</span>0,<span class="DataSegSigDiff">3</span>0,20,<span class="DataSegSigDiff">3</span>0,<span class="DataSegSigDiff">2</span>0,<span class="DataSegSigDiff">3</span>0,40,<span class="DataSegSigDiff">5</span>0,80,<span class="DataSegSigDiff">5</span>0,<span class="DataSegSigDiff">6</span>0,100<span class="DataSegSigDiff">,140</span>,<span class="DataSegSigDiff">5</span>0,190</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft">6</td>
<td class="DataItemSigDiff AlignLeft">1,6,1<span class="DataSegSigDiff">6</span>,30,30,30,<span class="DataSegSigDiff">7</span>0,50,<span class="DataSegSigDiff">7</span>0,50,<span class="DataSegSigDiff">7</span>0,<span class="DataSegSigDiff">9</span>0,1<span class="DataSegSigDiff">1</span>0,<span class="DataSegSigDiff">18</span>0,1<span class="DataSegSigDiff">10,130,220,330,110,44</span>0</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft">6</td>
<td class="DataItemSigDiff AlignLeft">1,<span class="DataSegSigDiff">2,4,</span>6,1<span class="DataSegSigDiff">0,10</span>,30<span class="DataSegSigDiff">,20</span>,30<span class="DataSegSigDiff">,20</span>,30,<span class="DataSegSigDiff">4</span>0,50,<span class="DataSegSigDiff">8</span>0,50,<span class="DataSegSigDiff">6</span>0,<span class="DataSegSigDiff">10</span>0,1<span class="DataSegSigDiff">4</span>0,<span class="DataSegSigDiff">5</span>0,1<span class="DataSegSigDiff">9</span>0</td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSigDiff AlignLeft">7</td>
<td class="DataItemSigDiff AlignLeft">1,6,1<span class="DataSegSigDiff">6</span>,30,30,30,<span class="DataSegSigDiff">8</span>0,50,80,50,<span class="DataSegSigDiff">8</span>0,100,1<span class="DataSegSigDiff">3</span>0,<span class="DataSegSigDiff">20</span>0,1<span class="DataSegSigDiff">30,150,250,380,130,50</span>0</td>
<td class="AlignCenter">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft">7</td>
<td class="DataItemSigDiff AlignLeft">1,<span class="DataSegSigDiff">2,4,</span>6,1<span class="DataSegSigDiff">0,10</span>,30,<span class="DataSegSigDiff">20,</span>30<span class="DataSegSigDiff">,20</span>,30,<span class="DataSegSigDiff">4</span>0,50,80,50,<span class="DataSegSigDiff">6</span>0,100,1<span class="DataSegSigDiff">4</span>0,<span class="DataSegSigDiff">5</span>0,1<span class="DataSegSigDiff">9</span>0</td>
</tr>
</table>
<br>
</body>
</html>
