<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
.Wrap { white-space: nowrap; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.DataItemHeader { color: #000000; background-color: #FFFFFF; background-color: #E7E7E7; padding-top: 8px; }
td.DataItemInsigDiff { color: #000000; background-color: #EEEEFF; }
td.DataItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.DataItemNum { color: #696969; background-color: #F0F0F0; }
td.DataItemSame { color: #000000; background-color: #FFFFFF; }
td.DataItemSigDiff { color: #000000; background-color: #FFE3E3; }
td.DataItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.DataSegInsigDiff { color: #0000FF; }
.DataSegSigDiff { color: #FF0000; }
td.DataItemInsigDiffError { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.DataItemInsigOrphanError { color: #000000; background-color: #FAEEFF; text-decoration: line-through; }
td.DataItemSameError { color: #000000; background-color: #FFFFFF; text-decoration: line-through; }
td.DataItemSigDiffError { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
td.DataItemSigOrphanError { color: #000000; background-color: #F1E3FF; text-decoration: line-through; }
</style>
<title>MainInterfaceShow</title>
</head>
<body>
MainInterfaceShow<br>
Produced: 2025/7/29 11:20:50<br>
&nbsp; &nbsp;
<br>
Mode:&nbsp; Differences &nbsp;
<br>
Left base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753759188_040e6060\MainInterfaceShow\old &nbsp;
<br>
Right base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753759188_040e6060\MainInterfaceShow\new &nbsp;
<br>
&nbsp; &nbsp;
<br>
File: MainInterfaceShow_MainInterfaceShow.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
<td class="DataItemHeader Wrap">&nbsp;</td>
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">13</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">S</span>e<span class="DataSegSigDiff">co</span>nd<span class="DataSegSigDiff">NewUs</span>e<span class="DataSegSigDiff">r</span>Pack</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">二次破冰</span>礼包</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">s</span>e<span class="DataSegSigDiff">co</span>nd<span class="DataSegSigDiff">NewUs</span>e<span class="DataSegSigDiff">r</span>PackHomeIcon_tr</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="DataItemSigDiff AlignLeft Wrap">5</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">13</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Week</span>end<span class="DataSegSigDiff">Sal</span>ePack</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">周末</span>礼包</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">week</span>end<span class="DataSegSigDiff">Sal</span>ePackHomeIcon_tr</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="DataItemSigDiff AlignLeft Wrap">5</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">14</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Weeke</span>nd<span class="DataSegSigDiff">S</span>a<span class="DataSegSigDiff">l</span>e<span class="DataSegSigDiff">Pack</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">周末礼包</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">week</span>end<span class="DataSegSigDiff">Sa</span>le<span class="DataSegSigDiff">Pack</span>HomeIcon_tr</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="DataItemSigDiff AlignLeft Wrap">6</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">14</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">E</span>nd<span class="DataSegSigDiff">lessTre</span>a<span class="DataSegSigDiff">sur</span>e</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">无尽促销</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">endle<span class="DataSegSigDiff">ssGift</span>HomeIcon_tr</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="DataItemSigDiff AlignLeft Wrap">6</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">15</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">En</span>d<span class="DataSegSigDiff">l</span>e<span class="DataSegSigDiff">ssTre</span>a<span class="DataSegSigDiff">sur</span>e</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">无尽</span>促销</td>
<td class="DataItemSigDiff AlignLeft Wrap">e<span class="DataSegSigDiff">nd</span>le<span class="DataSegSigDiff">ssGift</span>HomeIcon_tr</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="DataItemSigDiff AlignLeft Wrap">7</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">15</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Car</span>d<span class="DataSegSigDiff">Packag</span>e<span class="DataSegSigDiff">S</span>a<span class="DataSegSigDiff">l</span>e</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">新卡包</span>促销</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">CardPackag</span>e<span class="DataSegSigDiff">Sa</span>leHomeIcon_tr</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="DataItemSigDiff AlignLeft Wrap">7</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">16</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">CardPackag</span>e<span class="DataSegSigDiff">Sale</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">新卡</span>包<span class="DataSegSigDiff">促销</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Ca</span>r<span class="DataSegSigDiff">dPackag</span>e<span class="DataSegSigDiff">Sale</span>HomeIcon_tr</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="DataItemSigDiff AlignLeft Wrap">8</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">16</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Remov</span>e<span class="DataSegSigDiff">AD</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">去广告礼</span>包</td>
<td class="DataItemSigDiff AlignLeft Wrap">r<span class="DataSegSigDiff">emv</span>e<span class="DataSegSigDiff">Ad</span>HomeIcon_tr</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="DataItemSigDiff AlignLeft Wrap">8</td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">17</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">RemoveAD</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">去广告礼包</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">remveAdHomeIcon_tr</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">2</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">9</span></td>
<td class="AlignCenter Wrap">+-</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
</tr>
</table>
<br>
</body>
</html>
