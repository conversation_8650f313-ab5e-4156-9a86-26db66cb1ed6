<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
.Wrap { white-space: nowrap; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.DataItemHeader { color: #000000; background-color: #FFFFFF; background-color: #E7E7E7; padding-top: 8px; }
td.DataItemInsigDiff { color: #000000; background-color: #EEEEFF; }
td.DataItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.DataItemNum { color: #696969; background-color: #F0F0F0; }
td.DataItemSame { color: #000000; background-color: #FFFFFF; }
td.DataItemSigDiff { color: #000000; background-color: #FFE3E3; }
td.DataItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.DataSegInsigDiff { color: #0000FF; }
.DataSegSigDiff { color: #FF0000; }
td.DataItemInsigDiffError { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.DataItemInsigOrphanError { color: #000000; background-color: #FAEEFF; text-decoration: line-through; }
td.DataItemSameError { color: #000000; background-color: #FFFFFF; text-decoration: line-through; }
td.DataItemSigDiffError { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
td.DataItemSigOrphanError { color: #000000; background-color: #F1E3FF; text-decoration: line-through; }
</style>
<title>safe_compare</title>
</head>
<body>
safe_compare<br>
Produced: 2025/7/29 15:06:14<br>
&nbsp; &nbsp;
<br>
Mode:&nbsp; Differences &nbsp;
<br>
Left base folder: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old &nbsp;
<br>
Right base folder: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new &nbsp;
<br>
&nbsp; &nbsp;
<br>
File: 游戏常量_GameConst.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
<td class="DataItemHeader Wrap">&nbsp;</td>
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSigDiff AlignLeft Wrap">SECOND_PACK_IS_OPEN</td>
<td class="DataItemSigDiff AlignLeft Wrap">int</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">3.25</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">二次破冰礼包开启关闭，1开始，0关闭</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">SECOND_PACK_IS_OPEN</td>
<td class="DataItemSigDiff AlignLeft Wrap">int</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">0</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">3.25</td>
<td class="DataItemSigDiff AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSigDiff AlignLeft Wrap">二次破冰礼包开启关闭，1开始，0关闭</td>
</tr>
</table>
<br>
</body>
</html>
