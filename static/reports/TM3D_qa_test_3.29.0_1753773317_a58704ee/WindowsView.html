<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<style>
.AlignLeft { text-align: left; }
.AlignCenter { text-align: center; }
.AlignRight { text-align: right; }
.Wrap { white-space: nowrap; }
body { font-family: sans-serif; font-size: 11pt; }
img.AutoScale { max-width: 100%; max-height: 100%; }
td { vertical-align: top; padding-left: 4px; padding-right: 4px; }

tr.SectionGap td { font-size: 4px; border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionBegin td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Black; border-right: 1px solid Black; }
tr.SectionMiddle td { border-left: none; border-top: none; border-right: 1px solid Black; }
tr.SubsectionAll td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
tr.SubsectionEnd td { border-left: none; border-top: none; border-bottom: 1px solid Gray; border-right: 1px solid Black; }
table.fc { border-top: 1px solid Black; border-left: 1px solid Black; width: 100%; font-family: monospace; font-size: 10pt; }
td.DataItemHeader { color: #000000; background-color: #FFFFFF; background-color: #E7E7E7; padding-top: 8px; }
td.DataItemInsigDiff { color: #000000; background-color: #EEEEFF; }
td.DataItemInsigOrphan { color: #000000; background-color: #FAEEFF; }
td.DataItemNum { color: #696969; background-color: #F0F0F0; }
td.DataItemSame { color: #000000; background-color: #FFFFFF; }
td.DataItemSigDiff { color: #000000; background-color: #FFE3E3; }
td.DataItemSigOrphan { color: #000000; background-color: #F1E3FF; }
.DataSegInsigDiff { color: #0000FF; }
.DataSegSigDiff { color: #FF0000; }
td.DataItemInsigDiffError { color: #000000; background-color: #EEEEFF; text-decoration: line-through; }
td.DataItemInsigOrphanError { color: #000000; background-color: #FAEEFF; text-decoration: line-through; }
td.DataItemSameError { color: #000000; background-color: #FFFFFF; text-decoration: line-through; }
td.DataItemSigDiffError { color: #000000; background-color: #FFE3E3; text-decoration: line-through; }
td.DataItemSigOrphanError { color: #000000; background-color: #F1E3FF; text-decoration: line-through; }
</style>
<title>WindowsView</title>
</head>
<body>
WindowsView<br>
Produced: 2025/7/29 15:16:19<br>
&nbsp; &nbsp;
<br>
Mode:&nbsp; Differences &nbsp;
<br>
Left base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753773317_a58704ee\WindowsView\old &nbsp;
<br>
Right base folder: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753773317_a58704ee\WindowsView\new &nbsp;
<br>
&nbsp; &nbsp;
<br>
File: WindowsView_WindowsView.csv &nbsp;
<br>
<table class="fc" cellspacing="0" cellpadding="0">
<tr class="SectionAll">
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
<td class="DataItemHeader Wrap">7:</td>
<td class="DataItemHeader Wrap">8:</td>
<td class="DataItemHeader Wrap">9:</td>
<td class="DataItemHeader Wrap">&nbsp;</td>
<td class="DataItemHeader Wrap">1:</td>
<td class="DataItemHeader Wrap">2:</td>
<td class="DataItemHeader Wrap">3:</td>
<td class="DataItemHeader Wrap">4:</td>
<td class="DataItemHeader Wrap">5:</td>
<td class="DataItemHeader Wrap">6:</td>
<td class="DataItemHeader Wrap">7:</td>
<td class="DataItemHeader Wrap">8:</td>
<td class="DataItemHeader Wrap">9:</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">22</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">二次破冰</span>礼包</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">S</span>e<span class="DataSegSigDiff">co</span>nd<span class="DataSegSigDiff">NewUs</span>e<span class="DataSegSigDiff">rPack</span>Window</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">22</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">周末</span>礼包</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Week</span>end<span class="DataSegSigDiff">Sal</span>eWindow</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">23</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">周末礼</span>包</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Wee</span>ke<span class="DataSegSigDiff">nd</span>SaleWindow</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">23</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">新卡</span>包<span class="DataSegSigDiff">促销弹窗</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">CardPac</span>k<span class="DataSegSigDiff">ag</span>eSaleWindow</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">24</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">新卡包促销弹窗</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">C</span>a<span class="DataSegSigDiff">rdP</span>a<span class="DataSegSigDiff">ckage</span>Sa<span class="DataSegSigDiff">l</span>e<span class="DataSegSigDiff">Windo</span>w</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">24</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">翻倍排行榜开始界面</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">D</span>a<span class="DataSegSigDiff">rtRiv</span>a<span class="DataSegSigDiff">ls</span>S<span class="DataSegSigDiff">t</span>a<span class="DataSegSigDiff">rtVi</span>ew</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">25</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">翻倍排行榜</span>开始界面</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">DartRi</span>v<span class="DataSegSigDiff">alsS</span>t<span class="DataSegSigDiff">a</span>r<span class="DataSegSigDiff">tV</span>i<span class="DataSegSigDiff">e</span>w</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">3</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">25</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">大逃杀活动</span>开始界面</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Ad</span>v<span class="DataSegSigDiff">en</span>t<span class="DataSegSigDiff">u</span>r<span class="DataSegSigDiff">eOpenW</span>i<span class="DataSegSigDiff">ndo</span>w</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">26</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">大逃杀活动开始界面</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Adven</span>t<span class="DataSegSigDiff">ur</span>e<span class="DataSegSigDiff">Op</span>e<span class="DataSegSigDiff">nWindow</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">26</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">蜗牛竞速引导弹窗</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">MineDashS</span>t<span class="DataSegSigDiff">ag</span>e<span class="DataSegSigDiff">Pan</span>e<span class="DataSegSigDiff">l</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">4</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">27</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">蜗牛</span>竞速引导弹窗</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Min</span>e<span class="DataSegSigDiff">Dash</span>StagePanel</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">4</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">27</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">星星</span>竞速引导弹窗</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">StarRac</span>eStagePanel</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">4</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">28</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">星星竞速引导</span>弹窗</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">S</span>ta<span class="DataSegSigDiff">rRaceS</span>t<span class="DataSegSigDiff">ag</span>e<span class="DataSegSigDiff">Panel</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">4</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">28</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">连胜活动开始</span>弹窗</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">RiseUpEventS</span>ta<span class="DataSegSigDiff">r</span>t<span class="DataSegSigDiff">Vi</span>e<span class="DataSegSigDiff">w</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">29</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">连胜</span>活动<span class="DataSegSigDiff">开始弹窗</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Ri</span>se<span class="DataSegSigDiff">U</span>p<span class="DataSegSigDiff">Eve</span>n<span class="DataSegSigDiff">tStartV</span>i<span class="DataSegSigDiff">e</span>w</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">2</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">29</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">挖沙</span>活动</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Trea</span>s<span class="DataSegSigDiff">ur</span>e<span class="DataSegSigDiff">DiggingO</span>p<span class="DataSegSigDiff">e</span>n<span class="DataSegSigDiff">W</span>i<span class="DataSegSigDiff">ndo</span>w</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">30</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">挖沙</span>活动</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Tr</span>ea<span class="DataSegSigDiff">su</span>r<span class="DataSegSigDiff">eDiggingOpen</span>Window</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">30</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">糖果收集</span>活动<span class="DataSegSigDiff">开始弹窗</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">SugarCrushR</span>e<span class="DataSegSigDiff">w</span>ar<span class="DataSegSigDiff">d</span>Window</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">31</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">糖果收集</span>活动<span class="DataSegSigDiff">开始</span>弹窗</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Sug</span>a<span class="DataSegSigDiff">rCrushR</span>e<span class="DataSegSigDiff">ward</span>Window</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">31</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">公会合作</span>活动<span class="DataSegSigDiff">引导</span>弹窗</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">TeamGo</span>a<span class="DataSegSigDiff">lOp</span>e<span class="DataSegSigDiff">n</span>Window</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">32</td>
<td class="DataItemSigDiff AlignLeft Wrap">公会<span class="DataSegSigDiff">合作</span>活动引导弹窗</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">TeamG</span>oalOpenWindow</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">32</td>
<td class="DataItemSigDiff AlignLeft Wrap">公会<span class="DataSegSigDiff">对战</span>活动引导弹窗</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Uni</span>o<span class="DataSegSigDiff">nB</span>a<span class="DataSegSigDiff">tt</span>l<span class="DataSegSigDiff">e</span>OpenWindow</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">33</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">公会对战活动引导弹窗</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Un</span>i<span class="DataSegSigDiff">onB</span>at<span class="DataSegSigDiff">tleOpe</span>nWindow</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">33</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">系统通知</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Notif</span>i<span class="DataSegSigDiff">c</span>at<span class="DataSegSigDiff">ionI</span>n<span class="DataSegSigDiff">fo</span>Window</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">0</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">0</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionMiddle">
<td class="DataItemSigDiff AlignLeft Wrap">34</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">系统通知</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">N</span>o<span class="DataSegSigDiff">tificationInfo</span>Window</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">0</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
<td class="AlignCenter Wrap">&lt;&gt;</td>
<td class="DataItemSigDiff AlignLeft Wrap">34</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">去广告礼包</span></td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">Rem</span>o<span class="DataSegSigDiff">veAds</span>Window</td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigDiff AlignLeft Wrap">0</td>
<td class="DataItemSigDiff AlignLeft Wrap">1</td>
<td class="DataItemSigDiff AlignLeft Wrap">60</td>
<td class="DataItemSigDiff AlignLeft Wrap">2</td>
</tr>
<tr class="SectionEnd">
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">35</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">去广告礼包</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">RemoveAdsWindow</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">0</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">0</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">1</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">60</span></td>
<td class="DataItemSigOrphan AlignLeft Wrap"><span class="DataSegSigDiff">2</span></td>
<td class="AlignCenter Wrap">+-</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
<td class="DataItemSame AlignLeft Wrap">&nbsp;</td>
</tr>
</table>
<br>
</body>
</html>
