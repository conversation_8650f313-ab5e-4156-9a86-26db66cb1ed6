# 配置表测试系统使用文档

## 系统概述

配置表测试系统是一个基于Web的游戏配置表对比和测试平台，主要用于检测和分析游戏项目中Excel配置文件的变更情况。系统包含两个核心功能模块：

1. **配置对比模块** - 对比指定时间段内的配置表变更
2. **配置表功能测试模块** - 执行配置表的功能性测试

## 系统架构

### 前端界面 (`config_test.html`)
- 基于HTML5和现代CSS的响应式Web界面
- 支持标签页切换的双模块设计
- 实时进度显示和状态反馈
- 历史记录查看和数据库管理功能

### 后端服务 (`config_compare_service.py`)
- Python后端服务，基于GitLab API和PostgreSQL数据库
- 支持多项目配置管理
- 异步任务处理和进度跟踪
- 自动化的数据清理和存储优化

## 功能详解

### 1. 配置对比功能

#### 功能描述
对比指定时间段内Git仓库中Excel配置文件的变更，生成详细的对比报告。

#### 使用步骤

1. **选择项目和分支**
   - 在"分支选择"下拉框中选择目标Git分支
   - 系统会自动加载当前项目的所有可用分支

2. **设置时间范围**
   - 开始时间：对比的起始时间点
   - 结束时间：对比的结束时间点
   - 支持精确到分钟的时间选择

3. **执行对比**
   - 点击"开始对比"按钮启动对比任务
   - 系统显示实时进度条和状态信息
   - 对比完成后显示结果统计

#### 对比结果说明

对比结果包含以下信息：
- **新增文件** - 在时间段内新增的Excel文件
- **删除文件** - 在时间段内被删除的Excel文件  
- **变更文件** - 在时间段内被修改的Excel文件

每个文件提供以下操作：
- **查看对比报告** - 查看详细的文件内容对比
- **查看提交历史** - 跳转到GitLab查看文件的提交记录
- **下载报告** - 下载HTML格式的对比报告

### 2. 配置表功能测试

#### 功能描述
执行预定义的测试用例，验证配置表的完整性和正确性。

#### 测试用例类型
- **基础配置表验证** - 验证配置表的基本结构和字段完整性
- **数据类型检查** - 检查配置表中各字段的数据类型是否正确
- **关联性验证** - 验证不同配置表之间的关联关系

#### 使用步骤
1. 选择测试分支
2. 点击"开始测试"按钮
3. 查看测试进度和结果
4. 下载测试报告

### 3. 历史记录管理

#### 查看历史对比
- 点击"历史对比"按钮查看过往的对比记录
- 支持按时间排序和筛选
- 可以重新查看历史对比的详细结果

#### 数据库管理
系统提供完整的数据库管理功能：

**存储统计信息**
- 总记录数
- 包含HTML报告的记录数
- 最近7天/30天的记录数
- 数据库表大小
- 预估可节省空间

**清理配置**
- HTML报告保留天数（默认30天）
- 完整记录保留天数（默认90天）
- 支持手动执行清理
- 支持定期自动清理

## 技术特性

### 前端技术特性
- **响应式设计** - 支持桌面和移动设备
- **实时进度显示** - WebSocket或轮询方式获取任务进度
- **自定义下拉选择器** - 支持键盘导航和搜索
- **模态弹窗** - 历史记录和数据库管理界面
- **主题适配** - 支持明暗主题切换

### 后端技术特性
- **GitLab集成** - 基于GitLab API获取代码变更
- **PostgreSQL存储** - 使用项目独立的数据库Schema
- **异步任务处理** - 支持长时间运行的对比任务
- **自动清理机制** - 定期清理过期的HTML报告和数据记录
- **错误恢复** - 完善的异常处理和重试机制

### 安全特性
- **项目隔离** - 每个项目使用独立的数据库Schema
- **权限控制** - 基于GitLab Token的访问控制
- **数据加密** - 敏感配置信息加密存储

## 配置要求

### 系统依赖
- Python 3.8+
- PostgreSQL 12+
- BCompare (用于文件对比)
- GitLab实例访问权限

### Python依赖包
```
pandas>=1.3.0
psycopg2-binary>=2.9.0
python-gitlab>=3.0.0
PyYAML>=6.0
```

### 配置文件结构
```yaml
projects:
  项目名称:
    git:
      url: "GitLab实例URL"
      project_ID: "GitLab项目ID"
      project_name: "项目名称"
      token: "访问Token"
      excel_dir: "Excel文件目录"
      excel_dir_ignore: ["忽略的目录"]
    pgsql:
      url: "数据库地址"
      port: 5432
      database: "数据库名"
      user: "用户名"
      password: "密码"
```

## 使用注意事项

### 性能优化建议
1. **时间范围控制** - 避免选择过长的时间范围，建议单次对比不超过30天
2. **并发限制** - 同一项目同时只能运行一个对比任务
3. **存储管理** - 定期清理历史数据，避免数据库过度膨胀

### 常见问题解决

**分支加载失败**
- 检查GitLab Token权限
- 确认项目ID配置正确
- 检查网络连接状态

**对比任务失败**
- 检查BCompare是否正确安装
- 确认Excel文件格式正确
- 查看系统日志获取详细错误信息

**数据库连接问题**
- 检查数据库配置信息
- 确认数据库服务运行状态
- 验证用户权限设置

### 最佳实践
1. **定期备份** - 定期备份重要的对比结果和配置数据
2. **监控存储** - 监控数据库存储使用情况，及时清理过期数据
3. **版本管理** - 保持系统和依赖包的版本更新
4. **日志管理** - 定期查看和清理系统日志文件

## 扩展功能

### 自定义测试用例
系统支持添加自定义的配置表测试用例，可以根据项目需求扩展测试逻辑。

### 报告定制
支持自定义对比报告的格式和内容，可以集成到CI/CD流程中。

### API接口
提供RESTful API接口，支持与其他系统集成和自动化调用。

---

*本文档版本：v1.0*  
*最后更新：2025-01-28*
