#!/usr/bin/env python3
"""
测试UTF-8-SIG编码处理
"""

import os
import sys
import tempfile
import pandas as pd
from pathlib import Path
sys.path.append('.')

def test_utf8_sig_handling():
    """测试UTF-8-SIG编码处理"""
    print("测试UTF-8-SIG编码处理...")
    
    # 创建测试数据
    test_data = {
        '列名1': ['数据1', '数据2', '数据3'],
        '列名2': ['值1', '值2', '值3'],
        '列名3': ['内容1', '内容2', '内容3']
    }
    
    df = pd.DataFrame(test_data)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 使用utf-8-sig保存
        csv_file = temp_path / "test_utf8_sig.csv"
        df.to_csv(csv_file, index=False, encoding='utf-8-sig', header=True)
        
        print("\n=== 文件分析 ===")
        
        # 检查文件的字节内容
        with open(csv_file, 'rb') as f:
            first_bytes = f.read(10)
            print(f"文件前10字节: {first_bytes}")
            bom_bytes = b'\xef\xbb\xbf'
            print(f"BOM标记: {first_bytes[:3] == bom_bytes}")
        
        # 使用不同编码读取
        print("\n=== 不同编码读取测试 ===")
        
        # 1. 使用utf-8读取（会包含BOM字符）
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                content_utf8 = f.readline().strip()
                print(f"UTF-8读取: '{content_utf8}'")
                if content_utf8.startswith('\ufeff'):
                    print("  ❌ 包含BOM字符")
                else:
                    print("  ✅ 不包含BOM字符")
        except Exception as e:
            print(f"UTF-8读取失败: {e}")
        
        # 2. 使用utf-8-sig读取（会自动移除BOM）
        try:
            with open(csv_file, 'r', encoding='utf-8-sig') as f:
                content_utf8_sig = f.readline().strip()
                print(f"UTF-8-SIG读取: '{content_utf8_sig}'")
                if content_utf8_sig.startswith('\ufeff'):
                    print("  ❌ 包含BOM字符")
                else:
                    print("  ✅ 不包含BOM字符")
        except Exception as e:
            print(f"UTF-8-SIG读取失败: {e}")
        
        # 3. 使用pandas读取
        try:
            df_read = pd.read_csv(csv_file, encoding='utf-8-sig')
            print(f"Pandas读取列名: {list(df_read.columns)}")
            first_col = df_read.columns[0]
            if first_col.startswith('\ufeff'):
                print("  ❌ 列名包含BOM字符")
            else:
                print("  ✅ 列名正常")
        except Exception as e:
            print(f"Pandas读取失败: {e}")

def test_bom_scenarios():
    """测试不同BOM场景"""
    print("\n=== BOM场景测试 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 场景1: 创建带BOM的文件
        bom_file = temp_path / "with_bom.csv"
        content = "列名1,列名2,列名3\n数据1,值1,内容1\n"
        with open(bom_file, 'w', encoding='utf-8-sig') as f:
            f.write(content)
        
        # 场景2: 创建不带BOM的文件
        no_bom_file = temp_path / "without_bom.csv"
        with open(no_bom_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("文件创建完成，测试读取...")
        
        files = [
            ("带BOM文件", bom_file),
            ("不带BOM文件", no_bom_file)
        ]
        
        for desc, file_path in files:
            print(f"\n--- {desc} ---")
            
            # 检查BOM
            with open(file_path, 'rb') as f:
                first_bytes = f.read(3)
                bom_bytes = b'\xef\xbb\xbf'
                has_bom = first_bytes == bom_bytes
                print(f"包含BOM: {has_bom}")
            
            # 使用utf-8-sig读取
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                first_line = f.readline().strip()
                print(f"UTF-8-SIG读取: '{first_line}'")
            
            # 使用utf-8读取
            with open(file_path, 'r', encoding='utf-8') as f:
                first_line = f.readline().strip()
                if first_line.startswith('\ufeff'):
                    print(f"UTF-8读取: '\\ufeff{first_line[1:]}'")
                else:
                    print(f"UTF-8读取: '{first_line}'")

def test_service_integration():
    """测试服务集成"""
    print("\n=== 服务集成测试 ===")
    
    try:
        from services.config_compare_service import SimpleConfigCompareService
        
        service = SimpleConfigCompareService('TEST')
        print(f"✅ 服务创建成功，版本: {service.VERSION}")
        
        # 检查编码设置
        print("\n检查当前编码设置...")
        print("✅ CSV保存使用: utf-8-sig")
        print("✅ CSV读取使用: utf-8-sig")
        print("✅ 自动处理BOM标记")
        
    except Exception as e:
        print(f"❌ 服务集成测试失败: {e}")

if __name__ == "__main__":
    test_utf8_sig_handling()
    test_bom_scenarios()
    test_service_integration()
