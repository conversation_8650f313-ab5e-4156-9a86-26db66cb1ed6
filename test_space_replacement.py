#!/usr/bin/env python3
"""
测试空格替换功能
"""

import sys
import os
from pathlib import Path
sys.path.append('.')

from services.config_compare_service import SimpleConfigCompareService

def test_space_replacement():
    print("测试文件名空格替换功能...")
    
    # 创建服务实例
    service = SimpleConfigCompareService('TEST')
    
    # 测试文件名
    test_files = [
        "Toy Match 3D_埋点方案.xlsx",
        "[调用]多语言.xlsx", 
        "Game Config Data.xlsx",
        "User Profile Settings.xlsx",
        "normal_file.xlsx"
    ]
    
    print("=== 文件名清理测试 ===")
    for filename in test_files:
        file_path = Path(filename)
        original_stem = file_path.stem
        cleaned_stem = original_stem.replace(' ', '_')
        
        print(f"原始: '{original_stem}'")
        print(f"清理: '{cleaned_stem}'")
        print(f"变化: {'是' if ' ' in original_stem else '否'}")
        print()
    
    # 测试CSV文件名生成
    print("=== CSV文件名生成测试 ===")
    for filename in test_files:
        file_path = Path(filename)
        file_base_name = file_path.stem
        clean_file_name = file_base_name.replace(' ', '_')
        
        # 模拟sheet名称
        sheet_names = ["Sheet1", "#事件数据", "用户配置"]
        
        for sheet_name in sheet_names:
            clean_sheet_name = service._clean_sheet_name(sheet_name)
            csv_filename = f"{clean_file_name}_{clean_sheet_name}.csv"
            print(f"  {filename} -> {sheet_name} -> {csv_filename}")
        print()
    
    # 测试文件夹名称生成
    print("=== 文件夹名称生成测试 ===")
    for filename in test_files:
        file_path = Path(filename)
        file_base_name = file_path.stem
        clean_file_base_name = file_base_name.replace(' ', '_')
        
        print(f"文件: {filename}")
        print(f"原始文件夹: {file_base_name}")
        print(f"清理文件夹: {clean_file_base_name}")
        print(f"完整路径: work_dir/{clean_file_base_name}/old")
        print()

if __name__ == "__main__":
    test_space_replacement()
