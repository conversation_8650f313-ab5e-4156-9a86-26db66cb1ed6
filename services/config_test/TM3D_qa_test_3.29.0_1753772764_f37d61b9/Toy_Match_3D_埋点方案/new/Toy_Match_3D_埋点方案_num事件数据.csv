事件名（必填）,事件显示名,事件说明,事件标签,属性名（必填）,属性显示名,属性类型（必填）,属性说明
act_bug,,,client,g_err_debug,,字符串,"debug:debug版本;
release:release版本;"
,,,,g_err_file,,字符串,
,,,,g_err_line,,数值,
,,,,g_err_stack,,字符串,
active,,,,gdpr_consent,,字符串,
activeOnce,,,,gdpr_consent,,字符串,
adword_loop_levelStart_25,,,,,,,
,,,,,,,
adword_loop_levelStart_30,,,,,,,
,,,,,,,
adword_loop_levelStart_50,,,,,,,
,,,,,,,
adword_loop_level_20,,,,,,,
,,,,,,,
adword_loop_level_30,,,,,,,
,,,,,,,
adword_loop_level_35,,,,,,,
,,,,,,,
adword_loop_level_50,,,,,,,
,,,,,,,
adword_loop_online_5,,,,,,,
,,,,,,,
adword_loop_power_1,,,,,,,
,,,,,,,
adword_once_0h_level_15,,,,,,,
,,,,,,,
adword_once_0h_level_20,,,,,,,
,,,,,,,
adword_once_0h_level_30,,,,,,,
,,,,,,,
adword_once_0h_level_50,,,,,,,
,,,,,,,
adword_once_0h_level_70,,,,,,,
,,,,,,,
adword_once_0h_level_100,,,,,,,
,,,,,,,
adword_once_0h_rv_1,,,,,,,
,,,,,,,
adword_once_0h_interAd_3,,,,,,,
,,,,,,,
adword_once_168h_level_15,,,,,,,
,,,,,,,
adword_once_168h_level_20,,,,,,,
,,,,,,,
adword_once_168h_level_25,,,,,,,
,,,,,,,
adword_once_168h_level_30,,,,,,,
,,,,,,,
adword_once_168h_level_35,,,,,,,
,,,,,,,
adword_once_168h_level_50,,,,,,,
,,,,,,,
adword_once_168h_level_55,,,,,,,
,,,,,,,
adword_once_168h_level_90,,,,,,,
,,,,,,,
adword_once_168h_online_2,,,,,,,
,,,,,,,
adword_once_168h_online_400,,,,,,,
,,,,,,,
adword_once_168h_power_5,,,,,,,
,,,,,,,
adword_once_168h_rv_1,,,,,,,
,,,,,,,
adword_once_24h_levelStart_25,,,,,,,
,,,,,,,
adword_once_24h_levelStart_30,,,,,,,
,,,,,,,
adword_once_24h_levelStart_50,,,,,,,
,,,,,,,
adword_once_24h_levelStart_70,,,,,,,
,,,,,,,
adword_once_24h_level_120,,,,,,,
,,,,,,,
adword_once_24h_level_15,,,,,,,
,,,,,,,
adword_once_24h_level_20,,,,,,,
,,,,,,,
adword_once_24h_level_25,,,,,,,
,,,,,,,
adword_once_24h_level_30,,,,,,,
,,,,,,,
adword_once_24h_level_35,,,,,,,
,,,,,,,
adword_once_24h_level_50,,,,,,,
,,,,,,,
adword_once_24h_level_55,,,,,,,
,,,,,,,
adword_once_24h_level_70,,,,,,,
,,,,,,,
adword_once_24h_level_90,,,,,,,
,,,,,,,
adword_once_24h_online_2,,,,,,,
,,,,,,,
adword_once_24h_online_400,,,,,,,
,,,,,,,
click_ad,,SDK打点,,channel,,字符串,
,,,,id,,字符串,
,,,,p,,数值,
,,,,sdk_ad_price,,数值,
,,,,sdk_ad_scene,,字符串,
,,,,sdk_ad_source,,字符串,
,,,,sdk_ad_subchannel,,字符串,
,,,,sdk_ad_type,,字符串,
,,,,tf_adid,,字符串,
,,,,type,,字符串,
g_close,,关闭UI,client,g_ui_scene,,字符串,"privacy:隐私政策;
home:主界面;
levelPreview:关卡预览;
battle:战斗界面;
star_chest:星星宝箱;
level_chest:等级宝箱;
revive:复活界面;
lives:体力界面;
loading:loading页面;
shop_main:商城首页;
shop_moreoffer:商城moreoffer页;
battlepass:通行证主页;
remove_ads_pack:去广告礼包弹窗;
endless:无尽礼包主页;
newuser_pack:新手礼包弹窗;
singlecard:单卡促销页面;
multiplecardpack:卡包促销;
combo_packsale:三合一促销;
weekend_pack:周末礼包弹窗;"
g_collect_event,,收集活动,client,g_batch_time,,数值,"活动批次, 例如********"
,,,,g_candy_num,,数值,当前关卡获得的糖果数量，在胜利结算页打，每收集1个糖果+1
,,,,g_candy_total,,数值,当前批次累计获得的糖果数量，在胜利结算页打，每收集1个糖果+1
,,,,g_event_progress,,数值,领取奖励时的等级，
,,,,g_act,,字符串,"show:活动曝光;
get_candy:获得糖果;
claim_reward:领取奖励;"
,,,,g_packtype,,字符串,"TRUE:投放卡包;
FALSE:没有投放卡包;"
,,,,g_o_item_list,,对象组,资源对象组，活动期间所有消耗的累计
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_revive_time,,数值,金币复活累计次数
g_change_device_authority,,获取写入权限打点,client,g_act,,字符串,"start:发起获取写入权限;
success:获取写入权限成功;
fail:获取写入权限失败;"
,,,,g_way,,字符串,"new_device:拉取原因为新设备;
reloading:拉取原因为reloading;"
,,,,get_permission_info,,字符串,获取写入权限返回信息(成功/失败信息）
g_guide,,新手引导相关事件,client,g_act,,字符串,"show:展示;
click: 点击;"
,,,,g_guide_step,,数值,步骤
,,,,g_guide_id,,数值,对应的引导id(详细说明)
g_iap,,内购相关打点,client,g_act,,字符串,"iap_show:展示（每个商品被展示在屏幕范围内1次打1次点，商品页上下滑动只计1次）;
iap_showfirst:用户优先展示（对应的商品id为根据rfm变化后优先展示的礼包档位);
iap_try:发起内购;
iap_launch:拉起原生支付窗口;
iap_complete:内购完成;
iap_cancel:内购取消;
iap_fail:内购失败;
iap_order_err:内购订单异常;
iap_complete_cache:启动的时候，收到了内购完成的回调（上次内购完成没返回回调，重启会去查询未消耗的订单），然后这个时候比如还在loading界面，就不能弹出获得界面，就打了个cache点，然后等回到主界面才弹出获得界面;
iap_get_cache:获取未消耗的订单;
iap_consume_cache:开始消耗缓存订单;
iap_pay_suc:支付成功;
iap_consume_suc:消耗成功;"
,,,,g_fail_detail,,字符串,记录payFail回传的失败原因详情，在iap_fail的情况下打
,,,,g_currency,,字符串,货币
,,,,g_order_id,,字符串,内购完成订单号
,,,,g_price,,数值,价格
,,,,g_price_local,,数值,本地化价格
,,,,g_product_id,,字符串,商品id
,,,,g_discount_state,,布尔,判定倍率券是否开启
,,,,g_shopitemid,,字符串,上报给服务器的shopitemid，和server_iap的yiap__shopitemid对应
,,,,g_round_diff,,数值,"离上次购买的轮次间隔,（促销和复活礼包分开计算，复活礼包仅计入有展示的轮次）"
,,,,g_scene,,字符串,"home_shop_main:主界面进入商城首页;
revive_shop_main:复活进入商城首页,4个付费项的页面;
revive_more_offer:点moreoffer后的商店页面;
battle_shop_main:战斗界面进入商城首页;
prebattle_shop_main:关前准备界面进入商城首页;
activity_window:从主界面&活动弹窗（去广告礼包、BP、无尽促销、卡包促销）;
revive_window:从复活弹窗下方购买;
discount_coupons:从商品倍率券引导弹窗进入商城首页;
props_supply:从道具礼包购买;
morelife:从购买体力banner购买;"
g_item_exhaust,,触发道具用尽,client,g_o_item_status,,对象组,某个道具用尽后，剩余的道具情况。
,,,,g_o_item_status._g_item_count,,数值,道具信息
,,,,g_o_item_status._g_item_id,,数值,道具信息
,,,,g_item_id,,字符串,"10002:体力;
30001:灯泡;
30002:撤回;
30003:风扇;
30004:冰冻;
20001:闪电;
20002:闹钟;"
g_item_get,,资源获取,client,g_get_scene,,字符串,"initial:初始发放;
collectEvent:收集活动;
gem:钻石兑换;
revive_shop:复活商城购买;
main_shop:主页面进入的商城购买;
battle_shop:战斗时商城购买（不包括复活商城）;
reward_video:看广告获得;
purchaseCache:缓存订单;
signin:签到活动;
star_chest:星星宝箱;
level_chest:关卡宝箱;
battle_royale:大逃杀活动;
remove_ad_pack:商店去广告加钻石礼包;
remove_ad_only_pack:商店仅去广告礼包;
remove_ad_window:去广告礼包弹窗;
battlepass_free:通行证免费区域;
battlepass_premium:通行证付费区域;
startournament:星星排行榜;
revive_pack:复活礼包;
newuser_pack:新手礼包;
endless_free:无尽礼包免费奖励;
endless_pay:无尽礼包付费奖励;
dart_rivals:翻倍排行榜;
dart_rivals_progress:翻倍排行榜进度;
singlecard:单卡促销;
card_sprint_map1:抽奖机map1活动页面;
card_sprint_map2:抽奖机map2活动页面;
card_sprint_map3:抽奖机map3活动页面;
card_sprint_map4:抽奖机map4活动页面;
card_sprint_map5:抽奖机map5活动页面;
daily_bonus:每日免费礼包领取;
combo_packsale:三合一促销;
multiplecardpack:卡包促销;
cardsystem_map:集卡系统map奖励;
cardsystem_set:集卡系统set奖励;
card_allcomplete:card全集齐奖励;
level:关卡获得;
free:引导获得卡包;
wildcard:万能卡兑换;
customized_sale:自选组合促销;
seasonal_packsale:夏日促销;
second_newuser_pack:二次破冰礼包弹窗;
props_supply:关内补充道具页面;
card_star_convert:赛季结算星星转化;
card_pack_convert:赛季结算卡包转化;
weekend_pack_sale:周末礼包促销;
snail_race:蜗牛竞速活动;
riseup_event:连胜活动;
card_package_sale:卡包促销;
free_more_lives:主场景和重试体力不够时限时体力领取;
star_race:星星竞速;
life_help:赠送体力奖励;
bp_gift:公会bp奖励;
team_goal:公会合作;
return_gift:回归奖励;
team_battle_person:公会争霸个人进度;
team_battle_team:公会争霸公会排名和个人排名;
support_GM:GM工具发送奖励;
sand_event:铲子活动;
crash:闪退补发;"
,,,,g_o_item_list,,对象组,资源对象组
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_o_item_list._g_time,,数值,无限资源对应的时间
g_inf_life_get,,获取无限体力,client,g_inf_life_times,,数值,"用户第几次获得非付费无限体力,只有获得非付费无限体力时发。
g_get_scene必须为signin,level_chest,star_chest,collectEvent,battlepass_free,battle_royale,startournament,endless_free,dart_rivals其中之一"
g_item_use,,资源使用,client,g_o_item_list,,对象组,资源对象组
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_o_item_list._g_time,,数值,
,,,,g_use_scene,,字符串,"life:购买体力;
buy_prop:游戏内购买道具;
use_prop:游戏内使用道具;
lose_life:失去体力;
revive_first:第1次复活;
revive_second:第2次复活;
revive_third:第3次复活;
revive_morethan_3:所有超过三次的复活;
union_create:创建公会;
change_name:改名消耗金币;"
g_level_fail,,关卡失败,client,g_seed_id,,数值,关卡随机种子
,,,,g_theme_id,,数值,主题场景ID
,,,,g_theme_progress,,数值,主题场景下投放关卡的关卡编号（-1，1~~11）；不属于投放关卡时标记为-1
,,,,g_difficulty_id,,数值,读取实际关卡ID；1 普通关；2 难关；3 超难关； 4 ultra hard（读取关卡数据中“difficulty”字段）
,,,,g_initial_time,,数值,关卡初始时间
,,,,g_real_time,,数值,关卡标签、爽关修正后的初始时间（不计算关前加时）
,,,,g_time,,数值,消耗时长（秒）
,,,,g_resttime,,数值,剩余时长（秒）
,,,,g_fail_times,,数值,关卡失败次数（每触发满足单关修正的g_level_fail计数+1；V1正常失败都计数+1）
,,,,g_diff_times,,数值,单关修正次数（新版本关卡对应为fix的值-1）
,,,,g_revive_time,,数值,触发时当关共复活几次
,,,,g_goal_progress,,数值,关卡目标物进度，剩余目标物件/全部目标物件*100，向下取整
,,,,g_progress,,数值,关卡进度，剩余物件/全部物件*100，向下取整
,,,,g_cause,,数值,"1:收集栏满了;
2:倒计时结束;"
,,,,g_model_detail,,对象组,失败时场内剩余物件数据
,,,,g_model_detail._model_id,,数值,"上报格式为物品id,物品层数,物品数量"
,,,,g_model_detail._model_quantity,,数值,
,,,,g_model_detail._model_initial,,数值,物品id的关卡初始数量
,,,,g_model_detail._goal,,布尔,物品是否是目标物
,,,,g_bar_detail,,对象组,失败时收集栏物件信息
,,,,g_bar_detail._model_id,,数值,失败时收集栏内物件id
,,,,g_bar_detail._model_quantity,,数值,失败时收集栏内物件id对应的物件数量
,,,,g_o_used_item,,对象组,消耗的关前道具（不包括限时道具），关内道具
,,,,g_o_used_item._item_id,,字符串,消耗的关前道具（不包括限时道具），关内道具
,,,,g_o_used_item._item_quantity,,数值,消耗的关前道具（不包括限时道具），关内道具 对应的数量
,,,,g_click_bar_status,,列表,本关点击每个模型距离关卡开始的时间序列（单位秒）
,,,,g_click_duration,,列表,本关点击每个模型的id序列
,,,,g_click_model_id,,列表,本关点击每个模型时收集栏的物品数量序列（数据层面而不是表现层面）
,,,,g_o_collect_detail,,对象组,本关收集物序列
,,,,g_o_collect_detail._g_model_id,,数值,本关投放的收集物模型id
,,,,g_o_collect_detail._g_start_num,,数值,本关投放的收集物数量
,,,,g_o_collect_detail._g_get_num,,数值,本关收集的收集物数量
,,,,g_o_star_times,,对象组,1~3星对应的剩余时间
,,,,g_o_star_times._g_one_star,,数值,1星剩余时间
,,,,g_o_star_times._g_two_star,,数值,2星剩余时间
,,,,g_o_star_times._g_three_star,,数值,3星剩余时间
,,,,g_o_return_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_return_level._g_stage,,数值,记录回归奖励档位，配置表中id对应的值即为档位值
,,,,g_o_return_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_return_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_return_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
,,,,g_o_purchase_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_purchase_level._g_stage,,数值,记录付费爽关档位，配置表中id对应的值即为档位值
,,,,g_o_purchase_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_purchase_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_purchase_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
g_level_quit,,关卡退出,client,g_seed_id,,数值,关卡随机种子
,,,,g_theme_id,,数值,主题场景ID
,,,,g_theme_progress,,数值,主题场景下投放关卡的关卡编号（-1，1~~11）；不属于投放关卡时标记为-1
,,,,g_difficulty_id,,数值,读取实际关卡ID；1 普通关；2 难关；3 超难关； 4 ultra hard（读取关卡数据中“difficulty”字段）
,,,,g_initial_time,,数值,关卡初始时间
,,,,g_real_time,,数值,关卡标签、爽关修正后的初始时间（不计算关前加时）
,,,,g_time,,数值,消耗时长（秒）
,,,,g_resttime,,数值,剩余时长（秒）
,,,,g_fail_times,,数值,关卡失败次数（每触发满足单关修正的g_level_fail计数+1；V1正常失败都计数+1）
,,,,g_diff_times,,数值,单关修正次数（新版本关卡对应为fix的值-1）
,,,,g_goal_progress,,数值,关卡目标物进度，剩余目标物件/全部目标物件*100，向下取整
,,,,g_progress,,数值,关卡进度，剩余物件/全部物件*100，向下取整
,,,,g_bar_detail,,对象组,退出时收集栏物件信息
,,,,g_bar_detail._model_id,,数值,退出时收集栏内物件id
,,,,g_bar_detail._model_quantity,,数值,退出时收集栏内物件id对应的物件数量
,,,,g_o_used_item,,对象组,消耗的关前道具（不包括限时道具），关内道具
,,,,g_o_used_item._item_id,,字符串,消耗的关前道具（不包括限时道具），关内道具
,,,,g_o_used_item._item_quantity,,数值,消耗的关前道具（不包括限时道具），关内道具 对应的数量
,,,,g_model_detail,,对象组,"上报格式为物品id,物品层数,物品数量"
,,,,g_model_detail._model_id,,数值,
,,,,g_model_detail._model_quantity,,数值,
,,,,g_model_detail._model_initial,,数值,物品id的关卡初始数量
,,,,g_model_detail._goal,,布尔,物品是否是目标物
,,,,g_o_star_times,,对象组,1~3星对应的剩余时间
,,,,g_o_star_times._g_one_star,,数值,1星剩余时间
,,,,g_o_star_times._g_two_star,,数值,2星剩余时间
,,,,g_o_star_times._g_three_star,,数值,3星剩余时间
,,,,g_o_return_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_return_level._g_stage,,数值,记录回归奖励档位，配置表中id对应的值即为档位值
,,,,g_o_return_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_return_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_return_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
,,,,g_o_purchase_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_purchase_level._g_stage,,数值,记录付费爽关档位，配置表中id对应的值即为档位值
,,,,g_o_purchase_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_purchase_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_purchase_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
g_level_start,,进入关卡,client,g_level_start_item,,列表,"10002:体力;
10003:限时体力;
20001:关前闪电;
20002:关前闹钟;
20003:限时闪电;
20004:限时闹钟;"
,,,,g_seed_id,,数值,关卡随机种子
,,,,g_theme_id,,数值,主题场景ID
,,,,g_theme_progress,,数值,主题场景下投放关卡的关卡编号（-1，1~~11）；不属于投放关卡时标记为-1
,,,,g_difficulty_id,,数值,读取实际关卡ID；1 普通关；2 难关；3 超难关； 4 ultra hard（读取关卡数据中“difficulty”字段）
,,,,g_initial_time,,数值,关卡初始时间
,,,,g_fail_times,,数值,关卡失败次数（每触发满足单关修正的g_level_fail计数+1；V1正常失败都计数+1）
,,,,g_real_time,,数值,关卡标签、爽关修正后的初始时间（不计算关前加时）
,,,,g_diff_times,,数值,单关修正次数（新版本关卡对应为fix的值-1）
,,,,g_o_star_times,,对象组,1~3星对应的剩余时间
,,,,g_o_star_times._g_one_star,,数值,1星剩余时间
,,,,g_o_star_times._g_two_star,,数值,2星剩余时间
,,,,g_o_star_times._g_three_star,,数值,3星剩余时间
,,,,g_model_detail,,对象组,关卡内物品数量
,,,,g_model_detail._model_id,,数值,"上报格式为物品id,物品层数,物品数量"
,,,,g_model_detail._model_quantity,,数值,物品id在关卡内数量（实际）
,,,,g_model_detail._model_initial,,数值,物品id的关卡初始数量（配置）
,,,,g_model_detail._goal,,布尔,物品是否是目标物
,,,,g_o_return_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_return_level._g_stage,,数值,记录回归奖励档位，配置表中id对应的值即为档位值
,,,,g_o_return_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_return_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_return_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
,,,,g_o_purchase_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_purchase_level._g_stage,,数值,记录付费爽关档位，配置表中id对应的值即为档位值
,,,,g_o_purchase_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_purchase_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_purchase_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
g_level_win,,关卡胜利,client,g_seed_id,,数值,关卡随机种子
,,,,g_theme_id,,数值,主题场景ID
,,,,g_theme_progress,,数值,主题场景下投放关卡的关卡编号（-1，1~~11）；不属于投放关卡时标记为-1
,,,,g_difficulty_id,,数值,读取实际关卡ID；1 普通关；2 难关；3 超难关； 4 ultra hard（读取关卡数据中“difficulty”字段）
,,,,g_initial_time,,数值,"关卡初始时间,按照秒进行计算"
,,,,g_real_time,,数值,关卡标签、爽关修正后的初始时间（不计算关前加时）
,,,,g_time,,数值,通关花费时长（秒）
,,,,g_resttime,,数值,关卡剩余时间
,,,,g_fail_times,,数值,关卡失败次数（每触发满足单关修正的g_level_fail计数+1；V1正常失败都计数+1）
,,,,g_diff_times,,数值,单关修正次数（新版本关卡对应为fix的值-1）
,,,,g_revive_time,,数值,触发时当关共复活几次
,,,,g_star,,数值,通关星级评价
,,,,g_o_used_item,,对象组,消耗的关前道具（不包括限时道具），关内道具，复活信息
,,,,g_o_used_item._item_id,,字符串,消耗的关前道具，关内道具
,,,,g_o_used_item._item_quantity,,数值,消耗的关前道具，关内道具 对应的数量
,,,,g_o_item_list,,对象组,资源对象组，连胜期间所有消耗的累计，当触发关卡失败和退出（断连胜）时清零重计
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_click_bar_status,,列表,本关点击每个模型距离关卡开始的时间序列（单位秒）
,,,,g_click_duration,,列表,本关点击每个模型的id序列
,,,,g_click_model_id,,列表,本关点击每个模型时收集栏的物品数量序列（数据层面而不是表现层面）
,,,,g_o_collect_detail,,对象组,本关收集物序列
,,,,g_o_collect_detail._g_model_id,,数值,本关投放的收集物模型id
,,,,g_o_collect_detail._g_start_num,,数值,本关投放的收集物数量
,,,,g_o_collect_detail._g_get_num,,数值,本关收集的收集物数量
,,,,g_o_star_times,,对象组,1~3星对应的剩余时间
,,,,g_o_star_times._g_one_star,,数值,1星剩余时间
,,,,g_o_star_times._g_two_star,,数值,2星剩余时间
,,,,g_o_star_times._g_three_star,,数值,3星剩余时间
,,,,g_model_detail,,对象组,胜利时关卡内物品数量
,,,,g_model_detail._model_id,,数值,"上报格式为物品id,物品层数,物品数量"
,,,,g_model_detail._model_quantity,,数值,胜利时物品id在关卡内数量
,,,,g_model_detail._model_initial,,数值,物品id的关卡初始数量
,,,,g_model_detail._goal,,布尔,物品是否是目标物
,,,,g_o_return_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_return_level._g_stage,,数值,记录回归奖励档位，配置表中id对应的值即为档位值
,,,,g_o_return_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_return_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_return_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
,,,,g_o_purchase_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_purchase_level._g_stage,,数值,记录付费爽关档位，配置表中id对应的值即为档位值
,,,,g_o_purchase_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_purchase_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_purchase_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
g_open,,打开UI,client,g_ui_scene,,字符串,"privacy:隐私政策;
home:主界面;
levelPreview:关卡预览;
battle:战斗界面;
star_chest:星星宝箱;
level_chest:等级宝箱;
revive:复活界面;
lives:体力界面;
loading:loading页面;
shop_main:商城首页;
shop_moreoffer:商城moreoffer页;
battlepass:通行证主页;
remove_ads_pack:去广告礼包弹窗;
endless:无尽礼包主页;
newuser_pack:新手礼包弹窗;
discount_coupons:商品倍率券引导弹窗;
singlecard:单卡促销页面;
multiplecardpack:卡包促销;
combo_packsale:三合一促销;
customized_sale:自选组合促销;
second_newuser_pack:二次破冰礼包弹窗;
level_props:关内补充道具页面;
force_update_window:强制更新弹窗;
tips_update_window:提示更新弹窗;
wild_card_use_window:万能卡兑换弹窗;
seasonal_packsale:夏日促销;
weekend_pack_sale:周末礼包促销;
pack:场景导航栏页;
card_package_sale:卡包促销主界面;"
,,,,g_open_from,,字符串,"home:从主界面进入;
revive:从复活进入;
battle:从战斗界面进入;
prebattle:从关前准备界面进入;
discount_coupons:从商品倍率券引导弹窗进入;"
g_revive,,关卡复活,client,g_seed_id,,数值,关卡随机种子
,,,,g_theme_id,,数值,主题场景ID
,,,,g_theme_progress,,数值,主题场景下投放关卡的关卡编号（-1，1~~11）；不属于投放关卡时标记为-1
,,,,g_difficulty_id,,数值,读取实际关卡ID；1 普通关；2 难关；3 超难关； 4 ultra hard（读取关卡数据中“difficulty”字段）
,,,,g_initial_time,,数值,关卡初始时间
,,,,g_real_time,,数值,关卡标签、爽关修正后的初始时间（不计算关前加时）
,,,,g_time,,数值,消耗时长（秒）
,,,,g_resttime,,数值,剩余时长（秒）
,,,,g_goal_progress,,数值,关卡目标物进度，剩余目标物件/全部目标物件*100，向下取整
,,,,g_progress,,数值,关卡进度，剩余物件/全部物件*100，向下取整
,,,,g_fail_times,,数值,关卡失败次数（每触发满足单关修正的g_level_fail计数+1；V1正常失败都计数+1）
,,,,g_diff_times,,数值,单关修正次数（新版本关卡对应为fix的值-1）
,,,,g_revive_time,,数值,触发时当关共复活几次（算上本次）
,,,,g_act,,字符串,"trigger:触发复活;
done:完成复活;"
,,,,g_cause,,数值,"1:收集栏满了;
2:倒计时结束;"
,,,,g_method,,字符串,"coin:钻石复活;
ad:广告复活;
bp:battlepass复活;
guide:新手引导赠送;"
,,,,g_extra_time,,数值,触发复活的额外赠送时长，>0时打点
,,,,g_extra_time_fail_count,,数值,累计失败次数（计算为额外加时的未复活次数）
,,,,g_bar_detail,,对象组,触发复活时收集栏物件信息
,,,,g_bar_detail._model_id,,数值,触发复活时收集栏内物件id
,,,,g_bar_detail._model_quantity,,数值,触发复活时收集栏内物件id对应的物件数量
,,,,g_model_detail,,对象组,"上报格式为物品id,物品层数,物品数量"
,,,,g_model_detail._model_id,,数值,
,,,,g_model_detail._model_quantity,,数值,
,,,,g_model_detail._model_initial,,数值,物品id的关卡初始数量
,,,,g_model_detail._goal,,布尔,物品是否是目标物
,,,,g_o_star_times,,对象组,1~3星对应的剩余时间
,,,,g_o_star_times._g_one_star,,数值,1星剩余时间
,,,,g_o_star_times._g_two_star,,数值,2星剩余时间
,,,,g_o_star_times._g_three_star,,数值,3星剩余时间
,,,,g_o_return_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_return_level._g_stage,,数值,记录回归奖励档位，配置表中id对应的值即为档位值
,,,,g_o_return_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_return_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_return_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
,,,,g_o_purchase_level,,对象组,记录回归奖励档位、进度、调整方式、调整组数
,,,,g_o_purchase_level._g_stage,,数值,记录付费爽关档位，配置表中id对应的值即为档位值
,,,,g_o_purchase_level._g_progress,,数值,"对应此档位此次调整的第几个关卡（1,2,3,4...)"
,,,,g_o_purchase_level._g_adjust_id,,数值,"1:固定时间;
2:比例时间;
3:比例组数;"
,,,,g_o_purchase_level._g_adjust_num,,数值,记录实际调整的时间（S）/组数；记录最终调整的值
g_rv_video,,激励视频,client,g_act,,字符串,"try:尝试激励视频;
appear:按钮展示;
show:播放激励视频;
complete:完成播放激励视频;"
,,,,g_rv_scene,,字符串,"revive:复活;
life:体力;
preview_scene:准备界面道具;
shop:商城换钻石;
rv_chest:关卡宝箱;
sign:七日签到;"
,,,,g_webview_version,,数值,
g_video_chest,,,client,g_type,,数值,"1:普通宝箱;
2:难关宝箱;"
,,,,g_act,,字符串,"chest_show:关卡内宝箱展示;
chest_click:点击关卡宝箱;
chest_get:宝箱收集(收集宝箱且关卡胜利，打在level id+1之前;
chest_window:待领取页面展示，打在level id+1之前;
chest_open:成功领取奖励，打在level id+1之前;"
g_scene_policy,,隐私政策弹窗,client,g_act,,字符串,"show:展示政策弹窗;
click_privacy_policy:点击隐私政策;
click_terms_of_service:点击服务条款;
click_accept:点击同意;
click_european_accept:点击同意;"
g_signin_activity,,7日签到活动,client,g_act,,字符串,"show:展示签到界面;
close:关闭签到界面;
claim:领取;"
,,,,g_signin_round,,数值,对应的轮次
,,,,g_signin_start,,数值,对应的轮次第几次开启，首次开启为1，断签后第2次开启为2，断签后第3次开启为3
,,,,g_stop_day,,数值,"上次断签的最后签到day,g_signin_start>1且g_signin_day=1时才发。"
,,,,g_signin_day,,数值,对应的day
impression_ad,,,,channel,,字符串,
,,,,id,,字符串,
,,,,p,,数值,
,,,,sdk_ad_adreviewcreativeid,,字符串,
,,,,sdk_ad_creativeid,,字符串,
,,,,sdk_ad_price,,数值,
,,,,sdk_ad_scene,,字符串,
,,,,sdk_ad_source,,字符串,
,,,,sdk_ad_subchannel,,字符串,
,,,,sdk_ad_type,,字符串,
,,,,tf_adid,,字符串,
,,,,type,,字符串,
,,,,,,,
new_device,,,,,,,
new_session,,,,,,,
p_event,,,,error,,字符串,
,,,,init_google,,字符串,
,,,,request_google,,字符串,
,,,,request_server,,字符串,
,,,,step,,字符串,
,,,,success,,字符串,
reward_ad,,,,channel,,字符串,
,,,,id,,字符串,
,,,,p,,数值,
,,,,sdk_ad_price,,数值,
,,,,sdk_ad_scene,,字符串,
,,,,sdk_ad_source,,字符串,
,,,,sdk_ad_subchannel,,字符串,
,,,,sdk_ad_type,,字符串,
,,,,tf_adid,,字符串,
,,,,type,,字符串,
sdk_close_session,,,,sdk_session_time,,数值,
sdk_feedback_event,,,,sdk_email,,字符串,
,,,,sdk_message,,字符串,
sdk_purchase_active,,,,sdk_group,,字符串,
sdk_purchase_activeOnce,,,,sdk_group,,字符串,
sdk_purchase_submit,,,,sdk_currency_code,,字符串,
,,,,sdk_group,,字符串,
,,,,sdk_product_id,,字符串,
,,,,sdk_product_price,,数值,
,,,,sdk_product_price_usd,,数值,
,,,,sdk_purchase_count,,数值,
,,,,sdk_purchase_scene,,字符串,
,,,,sdk_purchase_type,,字符串,
sdk_purchase_success,,,,sdk_currency_code,,字符串,
,,,,sdk_group,,字符串,
,,,,sdk_product_id,,字符串,
,,,,sdk_product_price,,数值,
,,,,sdk_product_price_usd,,数值,
,,,,sdk_purchase_count,,数值,
,,,,sdk_shopitem_id,,字符串,
,,,,sdk_order_id,,字符串,
,,,,sdk_purchase_scene,,字符串,
,,,,sdk_purchase_type,,字符串,
sdk_purchase_verify,,,,sdk_action,,字符串,
,,,,sdk_error_message,,字符串,
,,,,sdk_parameters,,字符串,
sdk_test_fileExist,,,,isreadolddata,,布尔,
sdk_view_event,,,,sdk_view_action,,字符串,
,,,,sdk_view_name,,字符串,
server_iap,,,,yiap__afuserid,,字符串,
,,,,yiap__consumable,,数值,
,,,,yiap__currency,,字符串,
,,,,yiap__did,,字符串,
,,,,yiap__group,,字符串,
,,,,yiap__idfa,,字符串,
,,,,yiap__isonlinetest,,数值,
,,,,yiap__itemprice,,数值,
,,,,yiap__itemrevenue,,数值,
,,,,yiap__name,,字符串,
,,,,yiap__newcreateat,,时间,
,,,,yiap__orderid,,字符串,
,,,,yiap__pid,,字符串,
,,,,yiap__pn,,字符串,
,,,,yiap__productid,,字符串,
,,,,yiap__refund,,数值,
,,,,yiap__shopitemid,,字符串,
,,,,yiap__shopname,,字符串,
,,,,yiap__v,,字符串,
,,,,yiap__verify,,字符串,
tf_needShow,,,,sdk_ad_scene,,字符串,
,,,,sdk_ad_type,,字符串,
,,,,type,,字符串,
tf_showAd,,,,channel,,字符串,
,,,,id,,字符串,
,,,,p,,数值,
,,,,sdk_ad_price,,数值,
,,,,sdk_ad_scene,,字符串,
,,,,sdk_ad_source,,字符串,
,,,,sdk_ad_subchannel,,字符串,
,,,,sdk_ad_type,,字符串,
,,,,tf_adid,,字符串,
,,,,type,,字符串,
g_login,,登陆打点,client,g_login_process,,字符串,"start:发起登录;
login_success:登录成功;
register_success:注册成功;
fail:登录失败,每次尝试失败都需要打点;
delay:超时;
delayed_success:超时登录成功;
account_change:更换账号;
remote_start:开始拉取远程存档;
remote_success:拉取远程存档成功;
remote_fail:拉取远程存档失败;
account_enter_healthy:账号系统进入健康状态;
account_exit_healthy:账号系统退出健康状态;"
,,,,g_login_info,,字符串,"返回token失败描述,只有失败时打。"
g_account_bind,,绑定账号,client,g_act,,字符串,"bind_start:发起绑定;
bind_success:绑定成功;
bind_fail:绑定失败;
unbind_start:开始解绑;
unbind_success:解绑成功;
unbind_fail:解绑失败;"
,,,,g_method,,字符串,"facebook:fb登录;
google:谷歌登录;
apple:苹果登录;
guest:游客;"
,,,,g_bind_detail,,字符串,触发账号绑定失败回传的错误详情
g_reloading,,reloading打点,client,g_reloading_way,,字符串,"cloud_cover:云端存档覆盖本地;
device_conflict:设备冲突导致;
change_account_sucess:切换账号成功;
change_account_fail:切换账号失败;"
,,,,g_reloading_progress,,字符串,"start:发起reloading;
restore:使用云端的数据覆盖本地的数据;
complete:完成reloading;"
g_loading,,游戏开始loading打点,client,act,,字符串,"open:开始loading;
close:结束loading;"
g_remote_config,,拉取远程配置,client,g_login_process,,字符串,"start:发起拉取;
complete:拉取完成;
delay:超时;
delayed_cache:超时后收到的配置，缓存下个session使用;"
,,,,g_way,,字符串,"new_session:拉取原因为登录;
reloading:拉取原因为reloading;"
,,,,g_account_id,,字符串,拉取时的accoutn_id
g_remote_tag,,拉取远程标签,client,g_login_process,,字符串,"start:发起拉取;
complete:拉取完成;
delay:超时;
delayed_cache:超时后收到的配置，缓存下个session使用;"
,,,,g_way,,字符串,"new_session:拉取原因为登录;
reloading:拉取原因为reloading;"
,,,,g_account_id,,字符串,拉取时的accoutn_id
g_runtime_init,,初始化进入游戏主界面的数据和资源,client,,,,
g_star_chest,,星星宝箱,client,g_event_progress,,数值,当前领取的第几次
g_level_chest,,等级宝箱,client,g_event_progress,,数值,当前领取的第几次
g_battleroyale,,大逃杀活动,client,g_batch_time,,数值,"活动批次, 例如********"
,,,,g_royale_start,,数值,同批次第几次报名
,,,,g_royale_revive,,数值,g_act=join时开始累计复活次数，触发g act=fal或者achieve时结束累计，此时记录复活次数，再次触发join时清零并重新开始累计
,,,,g_royale_progress,,数值,g_act的行为发生时的连胜进度
,,,,g_packtype,,字符串,none:无卡包投放;
,,,,g_act,行为,字符串,"view:开启报名弹窗;
banner_view:关前准备banner曝光;
join:报名;
fail:失败;
achieve:完成活动;"
,,,,g_round,,数值,活动轮次
,,,,g_o_item_list,,对象组,资源对象组，活动期间所有消耗的累计
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_revive_time,,数值,金币复活累计次数
g_campaign,,用户分层归因事件,client,g_act,来源,字符串,"
init:初始化分组;
country:基于国家地区信息的分组;
adjust:基于adjust SDK归因回调的分组;
tag:基于标签系统下发的标签触发的分组;
reloading:老用户reloading触发的分组变化;"
,,,,g_pre_campaign_group,,字符串,玩家切换前的分组名
,,,,g_campaign_time,,数值,每个用户获得数据回传时间间隔（从进入游戏直到获取分组标签时间）
,,,,g_campaign_level,,数值,获得分组标签时的当前关卡点位
,,,,g_campaign_obtain,,字符串,"campaign_complete:归因信息解析成功;
campaign_fail:归因信息解析失败;



"
,,,,g_campaign_info,,字符串,"campaign_info:获取到的归因信息
"
,,,,g_campaign_group,,字符串,"CPB:针对价值优化中针对内购事件的优化;
CPE:针对非T3地区的非内购的事件优化;
ORG:针对非T3地区的自然量用户;
CPI:针对所有安装优化和T3地区的所有新增;
IAP:针对内购的价值优化系列;
DEFAULT:不属于其他分组的内容;
"
g_user_group_change,,当玩家因为支付行为，价值分组变化时触发（例如CPI变为IAP时）,client,g_pre_campaign_group,,字符串,玩家切换前的分组名
,,,,g_campaign_group,,字符串,玩家当前的分组名
,,,,g_act,,字符串,"remove_ads:因为购买去广告触发;
d15_iap:因为15天内的金额变化触发;"
,,,,g_cause,,数值,如果是因为15天内的金额变化触发，就记录用于判断的金额。如果是去广告触发，该属性=1
g_battlepass,,通行证,client,g_batch_time,,数值,"活动批次, 例如********"
,,,,g_key_num,,数值,获得的钥匙数量
,,,,g_act,,字符串,"view:进入bp界面;
get_key:获取钥匙;
claim_free:领取免费奖励;
claim_activated:领取付费奖励;
activate:激活bp;"
,,,,g_version,,字符串,"old:老BP;
new:新bp;"
,,,,g_activate_way,,字符串,"purchase:购买;
support_GM:GM发奖工具;"
,,,,g_pass_progress,,数值,bp等级
g_in_video,,插屏事件,client,g_act,行为,字符串,"try:尝试;
show:展示;
done:完成;"
g_notification_click,,系统推送,client,g_key,,字符串,Regular_pull:常规拉活;
g_dartrivals,,翻倍排行,client,g_act,,字符串,"show:活动开启引导界面;
banner_view:关前准备banner曝光;
join:报名活动;
get:通关获取代币;
reach_progress:达成节点;
finish:活动结算;"
,,,,g_dart_num,,数值,获得代币的数量
,,,,g_rank,,数值,结算排名
,,,,g_progress,,数值,节点进度（freach_progress达成节点行为时打，1~20）
,,,,g_packtype,,字符串,"TRUE:投放卡包;
FALSE:没有投放卡包;"
,,,,g_batch_time,,数值,"活动批次,例如********"
,,,,g_dart_total,,数值,"累计分数,当轮活动结算后清零"
,,,,g_o_item_list,,对象组,"资源对象组,活动期间所有消耗的累计"
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_revive_time,,数值,金币复活累计次数
,,,,g_robot_detail,,字符串,"上传当前轮次机器人中最高分机器人的增分行为
需要包括,最后总分,加分过程中各stage的增量打印出来"
g_discountcoupons,,商品倍率券,client,g_discount,,数值,倍率数值
,,,,g_act,,字符串,"go:点击go按钮;
iap_try:发起内购;
iap_complete:内购完成;"
,,,,g_price,,数值,在倍率券活动期间购买的礼包价格
,,,,g_product_id,,字符串,商品id
,,,,g_batch_time,,数值,"活动批次, 例如********"
g_ab_testing,,Abtest,client,,,,
g_cardget,,获取卡牌相关事件,client,g_act,,字符串,open:获得卡牌（关卡/活动/内购获得的卡包开启时、万能卡兑换时、抽卡机/单卡促销获得单卡时打）;
,,,,g_o_cardbag_objects,,对象组,卡牌具体信息
,,,,g_o_cardbag_objects._packtype,,字符串,"A:A类卡包;
B:B类卡包;
C:C类卡包;
D:D类卡包;
E:E类卡包:"
,,,,g_o_cardbag_objects._times,,数值,第几次开启该类卡包，在open时打点，新赛季开启时重置
,,,,g_o_cardbag_objects._number,,数值,卡牌编号
,,,,g_o_cardbag_objects._g_map_id,,数值,map编号
,,,,g_o_cardbag_objects._g_set_id,,数值,set编号
,,,,g_o_cardbag_objects._g_card_id,,数值,卡牌在set中的位置
,,,,g_o_cardbag_objects._g_star,,数值,卡牌星数
,,,,g_o_cardbag_objects._g_special,,布尔,是否为特殊卡池卡牌
,,,,g_get_scene,,字符串,"level:关卡;
free:初始免费赠送;
startournament:星星排行榜;
dart_rivals:翻倍排行榜;
battle_royale:大逃杀活动;
endless:无尽礼包付费奖励;
battlepass_free:通行证免费区域;
battlepass_premium:通行证付费区域;
combo_packsale:三合一促销;
customized_sale:自选组合促销;
wild:万能卡兑换宝箱;
star_exchange:星星兑换宝箱;
seasonal_packsale:夏日促销;
collectEvent:糖果收集活动;
RiseUpEvent:连胜活动;
card_package_sale:卡包促销;
star_race:星星竞速;
gm_reward:GM发奖;
team_goal:公会合作;
team_battle_team:公会对战公会奖;
team_battle_person:公会对战个人奖;
sand_event:铲子活动;
dart_rivals_progress:翻倍排行榜进度;"
g_cardprogress,,卡牌完成进度,client,g_act,,字符串,"progress1:单个set收集7张;
progress2:单个set收集9张;
progress3:单个map收集完成;
set_complete:set收集完成;
all_complete:全收集;"
,,,,g_map,,数值,触发事件的map
,,,,g_set,,数值,触发事件的set，如果触发事件的map本身则set为0
,,,,g_o_item_list,,对象组,资源对象组
,,,,g_o_item_list._g_item_id,,数值,获得物品的id
,,,,g_o_item_list._g_item_count,,数值,获得物品的数量
,,,,g_o_item_list._g_time,,数值,无限资源对应的时间
g_cardsystem,,卡牌系统,client,g_batch_time,,数值,活动开启时间
,,,,g_act,,字符串,show:界面展示;
,,,,g_ui_scene,,字符串,"main:主页面;
star_exchange_info:星星兑换说明页;
set1:set1详情页;
set2:set2详情页;
set3:set3详情页;
set4:set4详情页;
set5:set5详情页;
set6:set6详情页;
set7:set7详情页;
set8:set8详情页;
set9:set9详情页;
set10:set10详情页;
set11:set11详情页;
set12:set12详情页;
set13:set13详情页;
set14:set14详情页;
set15:set15详情页;"
g_packget,,获取卡包相关事件,client,g_act,,字符串,"place:投放卡包（大逃杀/排行榜/无尽促销/三合一促销/bp）;
show:展示卡包（大逃杀/排行榜/无尽促销/三合一促销/关卡/卡包促销/单卡促销/抽奖机，展示时打）;
collect: 在关卡中点击卡包（活动中不打，只打关卡）;
get:获得卡包（关卡/活动/内购获得的卡包开启时、万能卡兑换时、抽卡机/单卡促销获得单卡时打）;
fail:损失卡包（大逃杀/排行榜投放卡包但未获得时打-包含活动失败或倒计时结束，关内展示但未收集时打）;"
,,,,g_pack,,字符串,"A:A类卡包;
B:B类卡包;
C:C类卡包;
magic1:1星魔法卡包;
magic2:2星魔法卡包;
magic3:3星魔法卡包;
D:D类卡包;
E:E类卡包:"
,,,,g_get_scene,,字符串,"level:关卡;
free:初始免费赠送;
startournament:星星排行榜;
dart_rivals:翻倍排行榜;
battle_royale:大逃杀活动;
endless:无尽礼包付费奖励;
battlepass_free:通行证免费区域;
battlepass_premium:通行证付费区域;
combo_packsale:三合一促销;
customized_sale:自选组合促销;
wild:万能卡星星兑换宝箱;
star_exchange:星星兑换宝箱;
seasonal_packsale:夏日促销;
collectEvent:糖果收集活动;
riseup_event:连胜活动;
card_package_sale:卡包促销主界面;
star_race:星星竞速;
gm_reward:GM发奖;
team_goal:公会合作;
team_battle_team:公会对战公会奖;
team_battle_person:公会对战个人奖;
sand_event:铲子活动;
dart_rivals_progress:翻倍排行榜进度;"
g_seasonpacksale,,季节促销,client,g_act,,字符串,"show:开启活动时展示活动主界面;
iap_complete:内购完成;"
,,,,g_batch_time,,数值,"活动批次, 例如********，按开始时间"
,,,,g_iaptimes,,数值,当轮活动已购买次数
g_wildcard,,兑换,client,g_act,,字符串,"show:展示兑换宝箱的页面;
exchange:兑换宝箱;"
,,,,g_cardstar_total_num,,数值,累计获得星星数量
,,,,g_cardstar_current_num,,数值,当前剩余星星数量
,,,,g_box,,字符串,"box1:兑换宝箱时打点;
box2:兑换宝箱时打点;
box3:兑换宝箱时打点;"
g_weekendpacksale,,周末促销,client,g_act,,字符串,"show:开启活动时展示活动主界面;
iap_complete:内购完成;"
,,,,g_batch_time,,数值,"活动批次, 例如********，按开始时间"
g_leaderboard_collect,,排行榜获取冲榜积分,client,g_act,,字符串,"click:点击;
get:获取;
lose: 损失;"
g_leaderboard_monthly,,排行榜月榜,client,g_act,,字符串,"start:加入月榜;
end: 月榜赛季结束（完成结算);
claim:月榜领到奖(前200);"
g_update,,热更事件,client,g_act,,字符串,"sever_success:拉取服务器信息成功;
sever_fail:拉取服务器信息失败;
remote_success:远端对比成功;
remote_fail:远端对比失败;
net_notice:本地资源不完整，触发联网提示;
download_list_success:获取下载清单成功;
download_list_fail:获取下载清单失败;
update_begin:有需要下载的热更资源;
update_sucess:完成热更;
update_fail:热更失败;"
,,,,g_reason,,字符串,"network:未联网;
delay:超时;
no_space:空间不足;
notfound:远端文件丢失;
other:触发失败但不属于以上原因;"
,,,,g_detail,,字符串,
,,,,g_update_version,,数值,预期升级到的版本，在远端对比，获得下载清单，进行热更三个阶段携带这个属性
g_date_limit,,用户数据量超过1M,client,g_data_size,,字符串,数据大小
g_cheat_active,,作弊行为事件,client,g_act,,字符串,"monthly:月榜写入积分数量异常，单次写入积分数量大于1时(常量配置)，该玩家行为标记为作弊;
dartrivals:翻倍排行榜单次获得积分大于10时(常量配置)触发;
coin:单次获得金币超过上限时(常量配置)触发;"
,,,,g_num,,数值,触发作弊行为时的增量
g_get_client_activity_config,,用户获取客户端活动,client,g_act,,字符串,"success:获去客户端活动成功;
fail:获取失败;"
g_race,,竞速活动,client,g_round,,数值,各阶段参加的第n轮
,,,,g_rank,,数值,玩家通关数进度
,,,,g_wintimes,,数值,各个阶段的胜利次数
,,,,g_act,,字符串,"show:开启报名弹窗;
banner_view:关前准备报名banner曝光;
join:报名活动;
fail:阶段失败;
achieve:活动结算;"
,,,,g_o_item_list,,对象组,资源对象组
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_o_item_list._g_time,,数值,无限资源对应的时间(不用打了)
,,,,g_revive_time,,数值,金币复活累计次数
g_riseup_event,,连胜活动,client,g_batch_time,,数值,连胜活动批次，开始时间
,,,,g_act,,字符串,"show:开启活动主界面;
join:报名活动;
fail:节点失败;
achieve:节点达成;
end:活动结束(达成全部节点或时间结束打）;"
,,,,g_stage,,数值,当前档位（1~10）
,,,,g_progress,,数值,节点进度（fail、achieve行为时打，1~10）
,,,,g_revive_time,,数值,金币复活次数
,,,,g_o_item_list,,对象组,资源对象组
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_o_item_list._g_time,,数值,无限资源对应的时间
,,,,g_level_count,,数值,触发g_act为end时携带，为本轮活动期间累计通过的关卡数
g_activity_state_change,,活动状态改变,client,g_activity,,字符串,活动名称
,,,,g_batch_time,,数值,活动批次
,,,,g_pre_activity_state,,字符串,切换前的活动状态
,,,,g_activity_state,,字符串,切换后的活动状态
g_star_race,,星星竞速活动,client,g_round,,数值,各阶段参加的第n轮
,,,,g_total,,数值,玩家获得星星数量
,,,,g_wintimes,,数值,各个阶段的胜利次数
,,,,g_act,,字符串,"show:开启报名弹窗;
banner_view:关前准备报名banner曝光;
join:报名活动;
fail:活动失败;
achieve:活动成功;"
,,,,g_o_item_list,,对象组,资源对象组
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_o_item_list._g_time,,数值,无限资源对应的时间(不用打)
,,,,g_join_from,,字符串,"main:活动主界面加入;
banner:banner位置加入活动;"
,,,,g_revive_time,,数值,金币复活累计次数
g_deeplink,,deeplink事件,client,g_address,,字符串,
g_get_server_time,,用户获取服务器时间成功,client,,,,
g_level_adjust,,关卡难度调整事件,client,g_act,,字符串,"adjust_up:关卡难度区间提高;
adjust_down:关卡难度区间降低;
keep:关卡难度区间不变;"
,,,,g_diff_adjust_pre,,数值,调整前难度，难度区间调整时所在难度区间，难度不变时为当前难度
,,,,g_diff_adjust,,数值,调整后难度，难度区间调整时是调整后所在难度区间，难度不变时为当前难度
,,,,g_reason,变化或不变的原因,字符串,"cost_min_fail_min:消耗<下限,失败<下限;
cost_min_fail_mid:消耗<下限,下限<=失败<=上限;
cost_min_fail_max:消耗<下限,失败>上限;
cost_mid_fail_min:下限<=消耗<=上限,失败<=下限;
cost_mid_fail_mid:下限<=消耗<=上限,失败>下限;
cost_max:消耗>上限;"
g_union_join,,用户加入离开公会事件,client,g_act,,字符串,"show:展示公会推荐列表;
create:创建公会;
apply:申请加入公会;
join:加入公会;
leave:离开公会;
kicked:被踢出公会;"
,,,,g_join_from,,字符串,"recommend:推荐页;
search:搜索页;
setting_search:设置搜索页;
leaderboard:排行榜;"
,,,,g_union_info_id,,字符串,所有act对应的公会id
g_union_info,,修改公会信息相关事件,client,g_act,,字符串,"description:修改描述;
open_state:修改开放状态;
limit_level:修改可加入等级;
make_coleader:设置副会长;
demote_coleader:卸任副会长;
make_leader:退出公会导致会长变更;
union_disband:退出公会导致公会解散;
join_allow:审批通过;
join_unallow:审批未通过;
kick_out:踢出成员;"
,,,,g_position,,字符串,公会职位
,,,,g_passive_id,,字符串,被操作的用户id
,,,,g_changed_info_pre,,字符串,修改前信息，修改描述，开放状态，可加入等级时携带
,,,,g_changed_info,,字符串,修改后信息，修改描述，开放状态，可加入等级时携带
g_union_chat,,触发公会聊天页面事件,client,g_act,,字符串,"chat:发送聊天信息;
life_request:请求体力;
life_help:赠送体力;
bp_give:赠送bp礼物;
bp_get:领取bp礼物;"
g_free_lives,,触发体力背包事件,client,g_act,,字符串,"add:补充1个免费体力到资产栏;
refill:补满体力资产栏（关前预览&重试界面触发）;"
g_union_error,,用户公会相关操作失败情况,client,g_reason,,字符串,导致公会相关操作失败的原因
g_team_goal,,公会合作活动,client,g_act,,字符串,"show:活动弹窗弹出;
join:加入活动(获得第1个收集物时触发);
get:获得收集物;
claim_reward_p1:领取第1个宝箱奖励;
claim_reward_p2:领取第2个宝箱奖励;
claim_reward_p3:领取第3个宝箱奖励;
claim_reward_rank:领取个人奖励;
finish:活动结束;"
,,,,g_num,,数值,个人单次获得收集物数量
,,,,g_total,,数值,个人获得收集物总数量
,,,,g_rank,,数值,个人排名
,,,,g_change_team,,布尔,每轮活动默认为false，活动期间退出公会变为true
,,,,g_revive_time,,数值,活动期间复活次数
,,,,g_o_item_list,,对象组,资源对象组，活动期间消耗累计
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_o_item_list._g_time,,数值,
g_feedback,,点击feedback按钮,client,g_act,,字符串,"email:不满足条件，调起email;
helpshift:满足条件，调起helpshift;"
g_team_battle,,公会争霸,client,g_act,,字符串,"show:活动弹窗弹出;
join:加入活动;
get:获得收集物;
claim_reward_union:领取公会排名奖励;
claim_reward_person:领取个人进度奖励;
claim_reward_rank:领取个人排名奖励;
finish:活动结束;"
,,,,g_num,,数值,个人单次获得收集物数量
,,,,g_total,,数值,个人获得收集物总数量
,,,,g_union_total,,数值,公会活动收集物总数
,,,,g_rank,,数值,个人排名
,,,,g_union_rank,,数值,公会排名
,,,,g_revive_time,,数值,活动期间复活次数
,,,,g_o_item_list,,对象组,资源对象组，活动期间消耗累计
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_o_item_list._g_time,,数值,
g_exception,,上传异常信息（非bug),client,g_info,,字符串,
g_battle_state,,战斗状态,client,g_act,,字符串,
,,,,g_info,,字符串,
g_gm_rewards,,GM发送奖励,client,g_act,,字符串,"get_cache:拉取到缓存的奖励数据;
start_progress:启动时判断有缓存，开始执行相关的逻辑;"
,,,,g_gm_reward_id,,列表,服务器发的GM发奖批次ID
g_sand_event,,铲子活动,client,g_act,,字符串,"show:进入活动主界面;
get:获得铲子;
achieve:节点达成;
Finish:活动结束;"
,,,,g_num,,数值,单次获得铲子数量，在g_act值为get时打点
,,,,g_total,,数值,活动期间获得总铲子数量，在g_act值为get时打点
,,,,g_progress,,数值,当前完成的棋盘进度（1～5），在在g_act值为achieve时打点
,,,,g_revive_time,,数值,活动期间复活次数
,,,,g_o_item_list,,对象组,资源对象组，活动期间消耗累计
,,,,g_o_item_list._g_item_count,,数值,
,,,,g_o_item_list._g_item_id,,数值,
,,,,g_o_item_list._g_time,,数值,
