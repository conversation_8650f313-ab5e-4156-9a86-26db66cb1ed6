Column_1,Column_2
,
,TA系统支持多端的数据接入，如客户端、服务端以及第三方数据，并且可以将多端数据接入同个项目中，以解决数据孤岛问题，但过程中有以下注意点，请在接入前予以关注
,"1.用户id匹配问题：
       在多端数据接入过程中（以下以服务端+客户端为例），我们需要保证用户在多端的数据是可以准确关联到一起的，这里就涉及到用户id的关联匹配问题，一般来说客户端数据习惯使用设备id、服务端使用账号id标记用户，当两端数据按照上述逻辑导入系统时，由于两个id的命名规则不同，收到上述数据时，会产生两个新用户，导致同一个用户的多端行为无法关联，基于TA系统的用户识别规则，建议通过以下方式设计埋点时的用户id。
a.只记录用户登录后的行为，因为在用户登录后，即产生了账号id，此时不管是客户端数据还是服务端数据，都将#account_id设置为账号id，即可完成用户多端行为的关联
b.用户在不登录的情况下也会产生行为，并且从业务上希望进行关注，TA是支持访客行为与登录行为的关联的，但是由于多端埋点时，多个埋点端数据到达的先后顺序理论上无法保证，所以需要进行特殊设计将#account_id设置为登录后的业务id，如账号id/角色id等，#distinct_id设置为基于设备的id，如设备id、自定义的访客id等，其中不管是客户端数据还是服务端数据，都需要保证#distinct_id在任何场景下都需要有值，由于服务产生数据时，一定已经和客户端产生了通信，此时即可把该用户的设备id，即客户端数据中的#distcint_id回传给服务端，以便服务端触发埋点数据时进行赋值
"
,"2.时间先后问题：
       由于在多端埋点过程中，各个端的时间可能是不统一的，如客户端埋点取的是本机时间，而服务端取的是服务端时间，此时由于多端存在时间差
可能导致事实上具备先后关系的数据，由于分别在客户端和服务端产生，导致最终落地成数据时，前后关系错乱，如发起充值（客户端）晚于充值完成（服务端）
针对上述情况，除了建议尽量将连续事件埋在一端以外，也建议通过以下方式进行解决
a.不存在多时区问题：那么建议客户端在初始化时，像服务端请求到服务器当前的时间，以该时间作为客户端数据采集的标准时间，避免客户端和服务端的时间差，此外，如果服务端涉及到多台服务器，那么本身服务器之间也会存在时钟不同步的问题，可以通过一些时钟同步组件进行时间同步
b.存在客户端跨时区问题：在这样的场景下，客户端和服务端可能存在不同的时区，而导致两端采集数据时的时间不统一，在这样的情况下，如果对于多时区的分析是敏感的，那么建议服务端数据跟着客户端的时区走，即服务端尝试获取客户端的时区，并且将#time字段设置为当前时间戳在该时区下的时间
"
,"3.事件以及通用事件属性的设计：
在没有特殊需求的情况下，不建议多端同时采集相同含义的事件，避免造成数据的冗余，对于通用事件属性而言，如等级、渠道、服务器等，建议是在多端数据中都包含，如只有一端设置了这些通用事件属性，TA系统是不会自动补齐的，会导致后续分析过程中属性不对齐的问题

"
