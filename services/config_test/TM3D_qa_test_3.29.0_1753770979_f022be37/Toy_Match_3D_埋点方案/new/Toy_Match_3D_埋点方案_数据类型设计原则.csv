Column_1,Column_2,Column_3,Column_4,Column_5,Column_6
,,,,,
,TA系统目前支持以下数据类型，系统内字段类型由首次导入时，该字段的类型决定，因此建议在埋点时重点确认字段相关类型,,,,
,TA数据类型,取值样例,取值说明,JSON数据类型,建议场景
,数值,"123,1.23",数据范围是-9E15至9E15，小数点最多保留3位,Number,"1.属性具备数值型运算场景（总和、中位数、均值等），如充值金额、消耗钻石量、过关时间（秒）、等级.....
2.id相关属性，且id的先后关系和数值大小成正相关，如关卡id、服务器id等，如这类id非纯数值，则作为字符串处理
3.接上，对于用户相关id，如角色id等，虽然可能由全数字组成，但本身无数值含义，且位数可能很大，建议作为字符串处理"
,字符串,"""ABC"",""上海""",字符的默认上限是2KB,String,不适用其它类型的属性，都可以考虑使用字符串类型，包括一些目前TA不支持的复杂类型，如map类型等
,时间,"""2019-01-01 00:00:00"",""2019-01-01 00:00:00.000""","""yyyy-MM-dd HH:mm:ss.SSS""或""yyyy-MM-dd HH:mm:ss""，如需表示日期，可使用""yyyy-MM-dd 00:00:00""",String,如注册时间、首次充值时间、月卡到期时间等
,布尔,"true,false",-,Boolean,如是否首次过关、是否付费用户等
,列表,"[""a"",""1"",""true""]",列表中的元素都会转变为字符串类型,Array,"如参战卡牌id[""关羽""、""张飞""、""赵云“]，用户标签[""高付费用户""、""潜在流失用户""]"
,对象,"{""item_name"":""英雄碎片"",""item_num"":5}",须符合子属性的取值规则，如子属性为字符串，子属性的取值需符合字符的默认上限是2KB,Object,/
,对象组,"[
{""item_name"":""英雄碎片"",""item_num"":5},
{""item_name"":""皮肤碎片"",""item_num"":2},
{""item_name"":""荣耀积分"",""item_num"":10}
]",须符合子属性的取值规则，如子属性为字符串，子属性的取值需符合字符的默认上限是2KB,Array(Object),例如一次战斗中多个角色出征，对象类型的属性可以包含不同类型的多个子属性，用于描述某个英雄的名称、星级、战力等；对象组类型可以将多个对象类型以数组的形式用一个字段表达，用以描述整个出征阵容。
