属性名（必填）,属性显示名,属性类型（必填）,属性说明
g_level_id,,数值,事件发生时的常规关卡最大关卡进度
g_start_level_id,,数值,事件发生时最新进入的关卡id，默认为1
g_gem,,数值,事件发生时剩余钻石数量
g_tests_object,,对象组,事件发生时用户的测试分组
g_tests_object._g_group_name,,字符串,测试分组名称，为g_tests_object对象组的子属性
g_iap_count,,数值,事件发生时的订单总数(谷歌订单号包含GPA，苹果订单号存在)
g_iap_total,,数值,事件发生时的订单总金额，用配置的美金计价(谷歌订单号包含GPA，苹果订单号存在)
g_life_count,,数值,事件发生时的体力数
g_retention_day,,数值,事件发生时距离登录的天数（首日登录为0）
g_hotfix_number,,数值,热更新版本号
g_leaderboard_monthly_score,,数值,每个月结算后更新
g_leaderboard_monthly_rank,,数值,每个月结算后更新
g_batch_time_leaderboard_monthly,,数值,"月榜活动的状态
活动批次号:用户参加了活动时为该轮活动的批次号;"
g_start,,数值,玩家的进关次数，默认为0，每次进关时+1，进入到下一关时重置
g_complete,,数值,玩家完成关卡的次数，默认为0，每次失败或通关后+1，不包含退出，进入到下一关时重置
g_last_try,,数值,记录上一关胜利时的g_complete数值
g_union_id,,字符串,所在公会id
g_free_life_count,,数值,事件发生时体力背包的免费体力数
idfa,设备编号,字符串,
pn,包名,字符串,
groupA,广告分组,字符串,
groupC,常量分组,字符串,
tf_plugin_version,插件版本是指sdk版本,字符串,
lan,设备系统语言,字符串,系统设置的语言，Android为二位字码，IOS为语言_国家码
sdk_play_day,用户活跃天数,数值,每日首次进入游戏时加1
sdk_retention_day,用户留存天数,数值,每日首次进入游戏时，计算与注册日的自然天数差
sdk_code,sdk版本号,数值,
app_code,app buildVersion,数值,
idfv,,字符串,
g_level_difficulty,,数值,事件发生时用户所处的关卡难度。为1-4，分别对应ABCD。注意level_win时，一定是获胜对应的关卡的关卡难度。
g_theme_level,,字符串,事件发生时投放关卡主题，非投放关卡为default，投放关卡为对应的主题名
g_card_session_openday,,数值,当期card活动已开启天数
g_card_session_playday,,数值,当期card活动开启后用户的游玩天数，每期card活动结束后清零
g_card_session_id,,数值,card活动唯一ID，用于区别赛季的为唯一值，使用活动开始时间，如20240501
g_streak,,数值,连胜次数的记录，每次关卡胜利时+1，当触发关卡失败和退出（断连胜）时清零重计
g_o_event_state,,对象组,活动状态对象组
g_o_event_state._dartrivals,,数值,"翻倍排行榜活动的状态
活动批次号:活动开启
0:活动未开启"
g_o_event_state._battleroyal,,数值,"大逃杀活动的状态
活动批次号:活动开启
0:活动未开启"
g_o_event_state._dartrivals_stage,,数值,翻倍排行榜活动的档位，在加入翻倍排行榜时发生变化
g_o_event_state._collect,,数值,"收集活动的状态
活动批次号:活动开启
0:活动未开启"
g_o_event_state._collect_progress,,数值,收集活动的进度，在领取奖励时发生变化
g_coin_iap_tag,,字符串,事件发生时用户的coin_iap_tag标签
g_pack_iap_tag,,字符串,事件发生时用户的pack_iap_tag标签
g_hotfix_num,,数值,热更版本
g_card_session_stage,,数值,当期card活动的轮次
g_iap_group,,字符串,客户端计算的大中小R分组
g_o_event_state._race,,数值,"竞速活动的状态
活动批次号:活动开启
0:活动未开启"
g_o_event_state._race_stage,,数值,竞速活动的机器人档位，在加入各阶段时发生变化
g_o_event_state._race_progress,,数值,竞速活动的阶段进度，在阶段解锁时发生变化
g_o_event_state._star_race,,数值,"星星竞速活动的状态
活动批次号:活动开启
0:活动未开启"
g_o_event_state._star_race_stage,,数值,星星竞速活动的机器人档位，在加入各阶段时发生变化
g_o_event_state._star_race_progress,,数值,星星竞速活动的阶段进度，在阶段解锁时发生变化
g_o_event_state._riseup,,数值,"连胜活动的状态
活动批次号:活动开启
0:活动未开启"
g_o_event_state._sand_event,,数值,"铲子活动的状态
活动批次号:活动开启
0:活动未开启"
g_o_event_state._riseup_stage,,数值,连胜活动档位，在加入各阶段时发生变化
g_o_event_state._riseup_progress,,数值,连胜活动的阶段进度，在阶段解锁时发生变化
g_o_event_state._goal,,数值,公会合作活动批次
g_o_event_state._goal_stage,,数值,公会合作活动档位
g_o_event_state._battle,,数值,公会争霸活动批次
g_o_event_state._battle_stage,,数值,公会争霸活动公会档位
g_o_event_state._battle_collect_stage,,数值,公会争霸活动个人档位
g_o_event_state._battle_progress,,数值,公会争霸活动个人奖励进度
g_account_method,,字符串,当前登录的账号类型
g_level_version ,,字符串,"对应关卡体系
v1：老的关卡体系； v2：新的关卡体系"
g_level_name,,字符串,实际使用的关卡文件名称
g_return_record_time,,字符串,回归奖励比对时间，格式**********（精确到小时）
g_local_time,,字符串,当前本地时间戳，格式**********（精确到小时）
g_item_20001,,数值,当前关前飞机数量（更新时间晚于获得或消耗）
g_item_20002,,数值,当前关前闹钟数量（更新时间晚于获得或消耗）
g_item_30001,,数值,当前磁铁数量（更新时间晚于获得或消耗）
g_item_30002,,数值,当前弹弓数量（更新时间晚于获得或消耗）
g_item_30003,,数值,当前风扇数量（更新时间晚于获得或消耗）
g_item_30004,,数值,当前冰冻数量（更新时间晚于获得或消耗）
