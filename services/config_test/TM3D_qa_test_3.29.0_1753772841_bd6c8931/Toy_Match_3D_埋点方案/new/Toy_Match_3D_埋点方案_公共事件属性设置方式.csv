Column_1,Column_2
,
,公共事件属性是所有(或大部分)事件都具有的属性
,"
使用 服务端sdk 或 数据导入工具 上报数据时，手动设置用户的公共事件属性即可；

使用 客户端sdk 上报数据时：
对于会出现变动的公共属性，如等级，当前最大关卡等，建议使用""动态公共属性""的方式进行设置，保证上报时属性值的正确性

公共事件属性将会被保存到缓存中，无需每次启动App时调用。如果调用 setSuperProperties: 设置了先前已设置过的公共事件属性，则会覆盖之前的属性。如果公共事件属性和 track:properties: 上传的某个属性的Key重复，则该事件的属性会覆盖公共事件属性。

注意，在用户退出登录之后，除了需要调用logout方法外，还可能需要清除目前已经设置的，和账户有依赖关系的公共事件属性，删除某个公共事件属性，可以调用 unsetSuperProperty方法， 如果想要清空所有公共事件属性，可以调用 clearSuperProperties

安卓SDK 设置方式：
https://doc.thinkingdata.cn/tdamanual/installation/android_sdk_installation.html#b%E8%AE%BE%E7%BD%AE%E5%85%AC%E5%85%B1%E4%BA%8B%E4%BB%B6%E5%B1%9E%E6%80%A7

iOS SDK 设置方式：
https://doc.thinkingdata.cn/tdamanual/installation/ios_sdk_installation.html#b-%E8%AE%BE%E7%BD%AE%E5%85%AC%E5%85%B1%E4%BA%8B%E4%BB%B6%E5%B1%9E%E6%80%A7

JS SDK 设置方式：
https://doc.thinkingdata.cn/tdamanual/installation/javascript_sdk_installation.html#c-%E8%AE%BE%E7%BD%AE%E5%85%AC%E5%85%B1%E4%BA%8B%E4%BB%B6%E5%B1%9E%E6%80%A7

Unity SDK 设置方式：
https://doc.thinkingdata.cn/tdamanual/installation/unity_sdk_installation.html#33-%E8%AE%BE%E7%BD%AE%E5%8A%A8%E6%80%81%E5%85%AC%E5%85%B1%E5%B1%9E%E6%80%A7

Flutter SDK 设置方式：
https://doc.thinkingdata.cn/tdamanual/installation/flutter_sdk_installation.html#32-%E8%AE%BE%E7%BD%AE%E5%85%AC%E5%85%B1%E4%BA%8B%E4%BB%B6%E5%B1%9E%E6%80%A7

小程序/小游戏 SDK 设置方式：
https://doc.thinkingdata.cn/tdamanual/installation/mp_sdk_installation.html#332-%E8%AE%BE%E7%BD%AE%E5%8A%A8%E6%80%81%E5%85%AC%E5%85%B1%E5%B1%9E%E6%80%A7"
