Column_1,Column_2,Column_3,Column_4,Column_5,Column_6,Column_7,Column_8,Column_9,Column_10,Column_11,Column_12,Column_13,Column_14,Column_15,Column_16
,,数据采集需求文档,,,,,,,,,,,,,
,,文档中带#的sheet为埋点设计部分，其它sheet为埋点过程中关键问题的设计原理或注意点，可根据实际业务需求进行浏览,,,,,,,,,,,,,
,,,,,,,,,,,,,,,
,,接入须知：接入前建议先阅读以下相关文档,,,,,,,,,,,,,
,,接入必知,,用户的定义,,,用户识别规则：在TA系统内如何定义一个用户,,,,,,,,
,,,,数据规范,,,数据规则：TA系统的数据格式规范,,,,,,,,
,,,,预置字段与系统字段,,,预置字段与系统字段,,,,,,,, 
,,接入工具,,TA 提供了全端的数据接入方案，主要接入方式包括：,,,,,,,,,,,
,,,,客户端SDK,,,可以采集到设备信息，以及不与服务器通信的用户行为数据,,,,,,,,
,,,,服务端SDK,,,采集内容更加精准，适合采集核心业务数据,,,,,,,,
,,,,数据导入工具,,,通常用于历史数据导入；服务端 SDK 配合 LogBus 也是比较通用的服务端数据采集方案,,,,,,,,
,,,,针对一般应用和 Web 开发，我们提供了：,,,,,,,,,,,
,,,,原生 SDK,,,Android SDK,,iOS SDK,,,,,,
,,,,第三方框架,,,Flutter,RN Android,RN iOS,Weex Android,Weex iOS,,,,
,,,,H5 开发,,,JavaScript SDK,,H5 与原生 SDK 打通方案,,,,,,
,,,,主流小程序、快应用平台,,,小程序 SDK,,,,,,,,
,,,,针对小游戏开发，我们提供了：,,,,,,,,,,,
,,,,主流游戏引擎的支持,,,LayaBox,Egret 白鹭引擎,Cocos Creator,,,,,,
,,,,主流小游戏、快游戏平台的支持,,,小游戏 SDK,,,,,,,,
,,,,针对手游开发，我们提供了：,,,Unity SDK,,虚幻 4 SDK,,,,,,
,,,,对于服务端采集方案，我们推荐：,,,服务端 SDK +, Logbus 的方案,,,,,,,
,,,,该方案在数据导入的稳定性、实时性、和效率方面都有比较好的表现。,,,,,,,,,,,
,,,,如果您有一些异构的历史数据需要导入，或者对于部分数据需要补录到 TA 系统中，可考虑使用 ,,,,,,,DataX,,,,
,,,,与 Logbus 方案不同，DataX 不是常驻服务，无法监控新数据的产生并且及时导入，因此不能保证数据的实时性。 DataX 的优势在于支持多种数据源的异构数据导入，操作简单。,,,,,,,,,,,
,,,,,,,,,,,,,,,
,,,,如果您使用了 Filebeat 和 Logstash 收集日志，并且希望将日志数据导入到 TA 系统，可以使用： ,,,,,,,,,,,
,,,,Filebeat +Logstash 方案,,,,,,,,,,,
,,接入必备信息,,"1.项目 APP ID：在 TA 后台创建项目的时候会生成项
   目的 APP ID，也可以在项目管理页面查看
2.确定数据接收端地址
   如果使用云服务, 接收端地址为: https://receiver.ta.thinkingdata.cn
   对于私有化部署的情况，需要在私有集群（或者接入点）绑定域名，并配置 SSL 证书
3.验证接收端地址：浏览器访问 https://YOUR_RECEIVER_URL/health-check，页面返回 ok 表示正确
4.数据采集方案，要包括：
   数据接入的方式：客户端 SDK、服务端 SDK、数据导入工具、或者几种方案结合的方式
   待接入数据的内容和触发时机",,,,,,,,,,,
,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,
,,针对上述接入方式，可针对实际业务情况进行选择，如有问题，可联系数数服务人员配合进行选型,,,,,,,,,,,,,
,,,,,,,,,,,,,,,
,,重点关注点：以下事项会直接影响埋点可用性，请关注,,,,,,,,,,,,,
,,关于数据类型的设计原则：数数分析系统内属性（即字段）的类型由该属性首次入库时的类型决定，因此建议在埋点时重点确认相关属性的类型，避免后续进行修改迁移,,,,,,,数据类型设计原则,,,,,,
,,,,,,,,,,,,,,,
,,如希望同时接入服务端与客户端的数据，请关注右侧文档,,,,,,,多端接入注意点,,,,,,
,,如项目中存在多个需要关注的用户ID体系，如角色id、账号id、设备id等，建议在埋点前优先梳理用户id相关的策略,,,,,,,多用户id体系的埋点建议,,,,,,
,,,,,,,,,,,,,,,
,,公共事件属性是所有(或大部分)事件都具有的属性，如果使用客户端sdk上传数据，需要注意在用户退出登录之后，需要手动清除目前已经设置的公共事件属性,,,,,,,公共事件属性设置方式,,,,,,
