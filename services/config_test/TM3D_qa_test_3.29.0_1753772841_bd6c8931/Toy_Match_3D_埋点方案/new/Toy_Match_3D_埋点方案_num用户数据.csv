属性名（必填）,属性显示名,属性类型（必填）,更新方式,属性说明,属性标签
g_device_id,,字符串,user_setOnce,new时记录,基本信息类
g_retention_day,,数值,user_set,登录流程时记录,进度类
g_user_current_gem,,数值,user_set,每次获取或消耗钻石时上传,进度类
g_user_is_cheat,,布尔,user_set,用户是否为作弊用户,基本信息类
g_user_is_debug,,布尔,user_setOnce,用户是否为debug用户,基本信息类
g_user_is_remove_ad,,布尔,user_set,用户内购和登录流程时设置,基本信息类
g_user_max_level,,数值,user_set,用户关卡胜利后后上传,进度类
g_user_total_gem,,数值,user_add,用户钻石获取量累计,累积类
g_total_rv_num,累计观看激励视频次数,数值,,每次观看激励视频时累加,累积类
g_leaderboard_monthly_score,,数值,,每个月结算后更新,
g_leaderboard_monthly_rank,,数值,,每个月结算后更新,
g_hotfix_version,,数值,,热更版本,
g_iap_group,,字符串,,客户端计算的大中小R分组,
g_user_group,,字符串,,"CPB:针对价值优化中针对内购事件的优化;
CPE:针对非T3地区的非内购的事件优化;
ORG:针对非T3地区的自然量用户;
CPI:针对所有安装优化和T3地区的所有新增;
IAP:针对内购的价值优化系列;
DEFAULT:其他分组:",
g_user_group_dynamic,,字符串,,"CPB:针对价值优化中针对内购事件的优化;
CPE:针对非T3地区的非内购的事件优化;
CPI:针对所有安装优化和T3地区的所有新增;
IAP:针对内购的价值优化系列;",
g_contaminated_account,,布尔,,重装后的reloading前的污染账号,
g_notification,,数值,,"0:应用内通知关闭;
1:应用内通知开启:",
g_user_country,,字符串,,获取用户的国家地区,
g_ecpm,,数值,,在每次进入到主页面后取值一次，如果取到的值大于目前记录的值则进行覆盖,
adjust_external_click_id,,字符串,,,
adjust_reftag,,字符串,,,
af_d_id,,字符串,,SDK打点,
af_u_id,,字符串,,SDK打点,
afrawip__ad,,字符串,,,
afrawip__ad_id,,字符串,,,
afrawip__adset,,字符串,,,
afrawip__adset_id,,字符串,,,
afrawip__af_site_id,,字符串,,,
afrawip__app_id,,字符串,,,
afrawip__app_version,,字符串,,,
afrawip__attributed_touch_time,,字符串,,,
afrawip__attribution_platform,,字符串,,,
afrawip__campaign,归因系列,字符串,,,
afrawip__campaign_id,,字符串,,,
afrawip__country_code,,字符串,,,
afrawip__event_name,,字符串,,,
afrawip__meida_source,归因渠道,字符串,,,
afrawip__platform,,字符串,,,
afrawip__unity_gamer_id,,字符串,,,
aj_u_id,adjust唯一ID,字符串,,SDK打点,
firebase_u_id,,字符串,,SDK打点,
gclid,,字符串,,,
idfa,,字符串,,SDK打点,
idfv,,字符串,,SDK打点,
network,,字符串,,,
pcampaignid,,字符串,,,
sdk_country_code,,字符串,,SDK打点,
sdk_group_a,,字符串,,SDK打点,
sdk_group_c,,字符串,,SDK打点,
sdk_play_day,,数值,,SDK打点,
sdk_purchase_count,内购总次数,数值,,SDK打点,
sdk_purchase_sum,,数值,,SDK打点,
sdk_register_date,,时间,,SDK打点,
sdk_register_version,,字符串,,SDK打点,
sdk_register_version_build,,数值,,SDK打点,
sdk_retention_day,,数值,,SDK打点,
sdk_system_device,,字符串,,SDK打点,
sdk_time_zone,,数值,,SDK打点,
sdk_update_date,,时间,,SDK打点,
sdk_update_version,,字符串,,SDK打点,
sdk_update_version_build,,数值,,SDK打点,
tf_uid,,字符串,,SDK打点,
bytd_iap_predict_7_30,,数值,,中台打点,
bytd_iap_predict_15_30,,数值,,中台打点,
g_union_id,,字符串,,服务器打点-公会id,
g_position,,字符串,,服务器打点-公会职位,
g_user_is_robot ,,布尔,,服务器打点-为机器人,
g_cheating_user,,布尔,,是否是作弊用户，默认false,
