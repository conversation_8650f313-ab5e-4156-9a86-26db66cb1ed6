常量名称,常量类型,常量值（此列一定要设置为文本格式）_数组需要为空的话填null就行了,常量参与远程配置版本,常量参与ab测试版本,备注
const_name,const_type,const_value,remote_version,abtest_version,markdown
string,string,string,string,string,string
export,export,export,,,export
,,,,,
,,,,,
,,,,,
,,,,,
,,,,,
,,,,,
INIT_POWER,int,5,,,初始体力数
INIT_POWER_LIMIT,int,5,,,初始体力上限
POWER_RESUME_TIME_SEC,int,1800,,,1体力恢复时间/秒
INIT_COIN,int,2000,,,初始金币数量
INIT_ITEM,int,4,,,道具默认初始数量
GEM_ID,int,10001,,,金币默认ID
LIFE_ID,int,10002,,,体力的ID
LIFE_ENDLESS_ID,int,10003,,,无限体力的ID
WIN_STEAK_PROP_LEVEL,[int],"17,5,18,5",,,17赠送5个关内飞机，18关赠送5个关内闹钟
WIN_STEAK_UNLOCK_LEVEL,int,19,,,连胜活动解锁关卡
PRELEVEL_BOOSTER_GUIDE_LEVEL,[int],"10,12",,,关前道具引导的关卡（闪电，闹钟）
BOOSTER_GUIDE_LEVEL,[int],"2,4,6,8",,,道具引导的关卡（锤子，行消，列消，多目标消除）
TUTORIAL_HARD_LEVEL_V2,int,14,,,首次难关引导关卡号-v2
ROYAL_PASS_DEPOSIT_CONTAIN,int,5000,,,通行证保险箱最大容量
ROYAL_PASS_DEPOSIT_COIN,int,50,,,通行证保险箱每次存钱数量
ROYAL_PASS_GET_KEY,[int],"1,2,3",,,通行证发放钥匙数量（普通关，难关，超难关）
ROYAL_PASS_GET_COIN,int,3000,,,通行证购买立得金币
TOY_PASS_LIFE_NEW,int,8,,,新通行证购买后体力上限
GOOGLE_PLAY_URL,string,https://play.google.com/store/apps/details?id=com.toy.match.game,,,gp商店url
DEFAULT_LANGUAGE,string,En,,,默认使用语言
COLLECT_EVENT_ITEM_NUM,[int],"1,10,2,35,3,10,4,30,5,15",,,收集活动创建收集物的数量
WINDOW_CONSECUTIVE_VIEW_LIMIT,int,2,,,单词连续弹窗次数上限
DAILYGIFT_UNLOCK_LEVEL,int,21,,,7日签到开启等级
NEWUSER_PACK_UNLOCK_LEVEL,int,15,,,新手礼包开启等级
NEWUSER_PACK_UNLOCK_TIME,int,7,,,新手礼包持续时长（日）
REVIVE_PACK_UNLOCK_LEVEL,int,15,,,复活礼包开启等级
ACTIVITY_INF_LIFE_REWARD,[int],"10003,15",,,活动报名赠送无限体力
BATTLEPASS_EXP_TYPE,int,0,1,,BP_ABtest分组类型
ADVENTURE_PACK_TYPE,int,0,1,,大逃杀Abtest分组类型
NEW_USER_PACK_ID,int,10504,,,新手礼包计费点Id
SECOND_NEW_USER_PACK_ID,int,10506,,,二次破冰礼包计费点id
BATTLE_PASS_ID,int,10501,,,bp计费点id
SELECT_ARTICLE_IDLE_TIME,int,5,,,战斗连续无操作时间时显示消除提示，单位秒
LOADING_TIPS_UPDATE_VERSION,int,307,,,loading提示更新版本号
LOADING_FORCE_UPDATE_VERSION,int,300,,,loading强制更新版本号
DART_RIVALS_RANK_REWARD_TYPE,int,0,,,翻倍排行版Abtest分组类型
DART_RIVALS_RANK_TAG_DOWN,[int],"4,2",,,翻倍排行榜玩家降档排名判断。参数1排名限制，参数2轮次限制
SINGLE_CLOCK_TIME_ADD,int,10,,,单个闹钟的时长(s)
LEADERBOARD_MONTHLY_NUM_LIMIT,int,200,,,月榜榜单上榜人数限制
NORMAL_CARD_PALCE_S3,[float],"80201,0.063,16",,,普通关卡投放A卡包id，概率，最大关数
HARD_CARD_PALCE_S3,[float],"80202,0.5,2",,,困难关卡投放B卡包id，概率，最大关数
VHARD_CARD_PALCE_S3,[float],"80203,0.5,2",,,超难关卡投放C卡包id，概率，最大关数
CARD_SYSTEM_START_PACK_S3,[int],"80203,1",,,集卡系统每次开启时发放卡包
CARD_FUNCTION_PARAMETE_S3,[float],"600,0.213,-0.575,0.0002,0.671",,,card第三期开卡函数参数
SPECIALCARD_GROUP_AMOUNT,int,2,,,card第三期特殊卡池卡牌数量
SET_DIFF_LIMIT,int,3,,,card第三期set之间差值限制
CARD_DIFF_LIMIT,int,3,,,card第三期卡牌之间差值限制
CHRAT_MONTHLY_GET,int,1,,,月榜写入积分数量异常，单次写入积分数量大于该值时，该玩家行为标记为作弊
CHRAT_DARTRIVALS_GET,int,10,,,翻倍排行榜单次获得积分大于该值时标记作弊行为
CHRAT_COINS_GET,int,100000,,,玩家单次获得金币超过该值上限时标记作弊行为
REVIVE_PACK_PURCHACE_LIMIT,int,2,,,复活礼包每轮购买次数上限
REVIVE_PACK_RESET_ROUND,int,2,,,复活礼包档位重置条件，X轮未购买则重置回level1
REVIVE_PACK_LAST_TIME,int,60,,,复活礼包持续时长（秒）
REVIVE_PACK_STAGE_UP,int,1,,,复活礼包升档条件，购买x次就升1档
ENDLESS_STAGE_CHANGE_ROUND,[int],"2,4",,,无尽促销用于判定档位升降的轮次数，两个参数分别代表阶段1-3和阶段4-6的轮次数；
ENDLESS_STAGE_CHANGE,[int],"1,3,4,2",,,无尽礼包档位升降: 1—3超过最大没购买轮次初始化值，4-6超过最大没购买轮次初始化值，4-6没超过最大没购买轮次初始化值，7-9初始化减少历史档位的值
IAP_TOTAL_LIMIT,[int],"50,500",344,,大中小R判断值
RIVIVE_BANNER_PRIORITY,[int],"19,1,8,40,6",,,复活banner展示付费类型优先级
BATTLEPASS_GOLDEN_NAME,string,"50003_0_30,50016_0_30,50020_0_30",,,各个机器人活动金色名字占比
TEAM_UNLOCK_LEVEL,int,21,,,公会解锁等级
REQUEST_LIVES_LIMIT,int,10,,,可请求体力时的体力背包上限
BATTLEPASS_TEAM_GIFT,[int],"10001,50",,,BP公会礼物
HELP_LIVES_REWARD,int,5,,,体力帮助奖励（金币数）
REVIVE_PACK_IS_OPEN,int,1,1,,控制复活礼包是否开启的开关，0为关闭，1为开启
RIVIVE_PACK_IS_NEW,int,1,3.17,3.17,控制复活礼包测试的开关，0为旧版本，1为新版本
RIVIVE_STORE_KEY,int,0,3.17,,控制复活商店tag标签的开关，0为旧版本，1为新版本
RETURN_GIFT_LEVEL_LIMIT,int,21,,,可获得回归奖励的最小等级
HELPSHIFT_IAP_SUM_REQUIREMENT,int,1000,1,,helpshift功能付费金额限制，为内购金额*100
ENDLESS_PACK_IS_OPEN,int,1,1,,无尽礼包ab测试开关，1为开启，0为关闭
LEVEL_MAX_V2,int,4550,,,最大关卡数（真实包可玩关卡数）V2
LOOP_TEMPLATE_BEGIN,int,2001,,,关卡进入循环起始关卡号
SEED_ID,[int],"3497,3689,3105,4347,5634,7176,7433,9896,10614,12571,14664,15993,21026,27901,31859,38560,48020,57525,61317,79220,85719,99709,118174,118027,138680,162753,176300,205901,232801,274930,334358,382385,539364,585270,806029,945902,1024781,1202603,1411268,1528709,1843497,2105266,2470669,2899357,3675806,4684578,5075825,5498576,6452630,8192913",,,关卡随机种子id
STAR_RATIO_DEFAULT,[float],"0,0.2,0.4",,,difficulty=1的关卡的星级剩余时间比例
STAR_RATIO_HARD,[float],"0,0.06,0.12",,,difficulty=2的关卡的星级剩余时间比例
STAR_RATIO_SUPERHARD,[float],"0,0.04,0.08",,,difficulty=3的关卡的星级剩余时间比例
STAR_RATIO_ULTRAHARD,[float],"0,0.03,0.06",,,difficulty=4的关卡的星级剩余时间比例
ITEM_SCALE,float,1.03,,,模型统一缩放比例
TUTORIAL_SUPER_HARD_LEVEL_V2,int,41,,,首次超难关引导关卡号-v2
LEVEL_DIFF_LIMIT,int,10,,,算入关卡失败需要的流逝的时间
THEAM_LEVEL_CHANGE_V2,int,11,,,V2新场景投放关卡数
SAND_EVENT_SHOLVE_NUM,[int],"1,3,5,10",,,不同关卡难度胜利获得的铲子数量
RETURN_ABTEST_V2,string,"{\""data\"":[{\""stage\"":1,\""dayDiff\"":1,\""extraLevelDurations\"":[],\""extraDurationsRatio\"":[0],\""decreaseItemRatio\"":[],\""rewards\"":[]},{\""stage\"":2,\""dayDiff\"":2,\""extraLevelDurations\"":[],\""extraDurationsRatio\"":[0],\""decreaseItemRatio\"":[],\""rewards\"":[]},{\""stage\"":3,\""dayDiff\"":3,\""extraLevelDurations\"":[],\""extraDurationsRatio\"":[0.37],\""decreaseItemRatio\"":[],\""rewards\"":[]},{\""stage\"":4,\""dayDiff\"":7,\""extraLevelDurations\"":[],\""extraDurationsRatio\"":[0.37,0.3,0.22],\""decreaseItemRatio\"":[],\""rewards\"":[]},{\""stage\"":5,\""dayDiff\"":14,\""extraLevelDurations\"":[],\""extraDurationsRatio\"":[0.37,0.32,0.27,0.22,0.15],\""decreaseItemRatio\"":[],\""rewards\"":[{\""count\"":1000,\""reward_id\"":10001},{\""count\"":60,\""reward_id\"":10003},{\""count\"":60,\""reward_id\"":20003},{\""count\"":60,\""reward_id\"":20004},{\""count\"":1,\""reward_id\"":30002},{\""count\"":1,\""reward_id\"":30003},{\""count\"":1,\""reward_id\"":30001},{\""count\"":1,\""reward_id\"":30004}]},{\""stage\"":6,\""dayDiff\"":28,\""extraLevelDurations\"":[],\""extraDurationsRatio\"":[0.37,0.32,0.27,0.25,0.22,0.18,0.15],\""decreaseItemRatio\"":[],\""rewards\"":[{\""count\"":2000,\""reward_id\"":10001},{\""count\"":120,\""reward_id\"":10003},{\""count\"":120,\""reward_id\"":20003},{\""count\"":120,\""reward_id\"":20004},{\""count\"":2,\""reward_id\"":30002},{\""count\"":2,\""reward_id\"":30003},{\""count\"":2,\""reward_id\"":30001},{\""count\"":2,\""reward_id\"":30004}]},{\""stage\"":7,\""dayDiff\"":60,\""extraLevelDurations\"":[],\""extraDurationsRatio\"":[0.37,0.34,0.32,0.3,0.27,0.24,0.22,0.18,0.15,0.12],\""decreaseItemRatio\"":[],\""rewards\"":[{\""count\"":3000,\""reward_id\"":10001},{\""count\"":180,\""reward_id\"":10003},{\""count\"":180,\""reward_id\"":20003},{\""count\"":180,\""reward_id\"":20004},{\""count\"":3,\""reward_id\"":30002},{\""count\"":3,\""reward_id\"":30003},{\""count\"":3,\""reward_id\"":30001},{\""count\"":3,\""reward_id\"":30004}]}]}",3.19,3.19,回归奖励ab测试配置，本地默认配置
LEVEL_PURCHASE_ABTEST_V2,string,"{\""data\"":[{\""stage\"":1,\""purchaseAmount\"":[0,4],\""extraLevelDurations\"":[0,0,0],\""extraDurationsRatio\"":[],\""decreaseItemRatio\"":[]},{\""stage\"":2,\""purchaseAmount\"":[4,15],\""extraLevelDurations\"":[0,0,0,0,0],\""extraDurationsRatio\"":[],\""decreaseItemRatio\"":[]},{\""stage\"":3,\""purchaseAmount\"":[15,50],\""extraLevelDurations\"":[0,0,0,0,0,0,0],\""extraDurationsRatio\"":[],\""decreaseItemRatio\"":[]},{\""stage\"":4,\""purchaseAmount\"":[50,100],\""extraLevelDurations\"":[0,0,0,0,0,0,0,0,0,0],\""extraDurationsRatio\"":[],\""decreaseItemRatio\"":[]}]}",3.19,3.19,付费爽关ab测试配置，本地默认配置
NOTIFICATION_SLEEP_MODE,[int],"22,9",,,系统通知勿扰时间
NOTIFICATION_DAILY_LIMIT,int,4,,,系统通知每日上限
CHANGENAME_COST,[int],"0,1000,2000,5000",,,金币改名费用
FREECHANGE_NAME,int,2,,,免费改名次数
RETURN_IS_OPEN,int,0,,,回归奖励功能开关，1为开启，0为关闭
REVIVE_BANNER_PACK_COUNT,int,2,3.25,,复活界面banner位可同时展示的付费项最多数量
NEWUSER_PACK_UNLOCK_TIME_NEW,int,3,3.25,,新手礼包持续天数测试
NEWUSER_PACK_UNLOCK_LEVEL_NEW,int,22,3.25,,新手礼包开启等级测试
NEWUSER_PACK_IS_NEW,int,0,3.25,,新手礼包功能版本，0原版，1新版
SECOND_PACK_IS_OPEN,int,1,3.25,,二次破冰礼包开启关闭，1开始，0关闭
WEEKEND_PACK_IS_OPEN,int,0,3.25,,周末礼包开启关闭，1开启，0关闭
WEEKEND_ENDLESS_TABLE,[int],"1,1,2,1,3,2,4,2,5,2,6,3,7,3,8,4,9,5",,,无尽礼包和周末礼包档位对照关系
ENDLESS_PACK_IS_OPEN_NEW,int,1,,,新无尽礼包功能开启开关，1开启，0关闭
