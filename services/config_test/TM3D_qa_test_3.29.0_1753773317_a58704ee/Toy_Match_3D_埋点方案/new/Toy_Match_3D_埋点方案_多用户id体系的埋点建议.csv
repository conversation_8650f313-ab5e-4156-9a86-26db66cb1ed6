Column_1,Column_2,Column_3,Column_4,Column_5,Column_6
,,,,,
,在游戏分析场景下，往往需要从多个角度分析用户，如角色、账号、设备或一些第三方id等，因此对于id的设计会直接影响到TA系统后续的使用过程,,,,
,以下以一个基础场景，来解释在TA系统埋点时，如果设计用户id体系，如有更复杂的场景，可以随时找数数的服务人员沟通,,,,
,,,,,
,假设目前项目存在如下id体系，如角色id、账号id、设备相关id（包括设备id或一些第三方基于设备的id，如adjust_id）,,,,
,在TA系统内，是以#account_id作为第一标准去识别单个用户的，即不同的#account_id一定对应不同的#user_id，#user_id即TA系统内的用户的识别依据,,,,
,"一般建议以最小的不可分割的业务id作为#account_id，上述场景下为角色id，以设备相关id作为#distinct_id,如设备id、数数客户端sdk生成的访客id，或自定义的访客id",,,,
,此外的其它id，都建议作为通用事件属性（如其和角色存在多对多的关系，如设备id）或用户属性进行记录，基于上述逻辑，建议埋点如下（下述访客id为数数客户端sdk的默认访客id，实际场景下可自定义设置 ）,,,,
,,,,,
,,,,,
,#account_id,#distinct_id,账号id（自定义属性）,设备id（自定义属性）,其它id（自定义属性）
,角色id,访客id,账号id,设备id,平台id、adjust_id等
,,,,,
,当然在实际埋点中，可能存在部分id不存在的问题，如设备激活时是没有角色id的，此时留空即可，当然访客id不管任何情况，理论上一定会存在的,,,,
,以下从实际的业务场景入手，来说明各个场景下的id设置,,,,
,,,,,
,埋点场景,#account_id,#distinct_id,账号id（自定义属性）,设备id（自定义属性）
,设备激活,-,访客id1,-,设备id1
,注册账号,-,访客id1,账号id1,设备id1
,创角角色,角色id1,访客id1,账号id1,设备id1
,开始战斗,角色id1,访客id1,账号id1,设备id1
,,,,,
,因此，总体来说，遵循有则填充，无则留空的逻辑，基于上述埋点，可以将用户登陆前后的数据进行关联，并且后续TA系统也会提供对应的功能,,,,
,从各个角度针对用户进行分析，如设备留存、账号漏斗转化等,,,,
