import requests
import yaml
import os
import json
from typing import Dict, List, Any, Optional, Union
from .log_common import LogService


class RequestService:
    """HTTP请求服务类
    
    根据项目名称从config.yaml读取post_gm配置并发送POST请求
    """

    def __init__(self, project_name: str):
        """
        初始化Request服务
        
        Args:
            project_name: 项目名称，用于从config.yaml中读取对应的post_gm配置
        """
        self.project_name = project_name
        self.logger = LogService(f'request_service_{project_name}')
        self.config = self._load_request_config()
        self.base_url = self.config['url']

        # 默认请求头
        self.default_headers = {
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh-TW;q=0.9,zh;q=0.8,en-US;q=0.7,en;q=0.6",
            "content-type": "application/json",
            "origin": "https://gm-pre-front.playdayy.cn",
            "priority": "u=1, i",
            "referer": "https://gm-pre-front.playdayy.cn/",
            "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"macOS\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "sessiontoken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NDgsImVtYWlsIjoieWlucGVuZ0BwbGF5ZGF5eS5jb20iLCJ1c2VyTmFtZSI6IuWwuem5jyIsImlzQWN0aXZlIjp0cnVlLCJpc0FkbWluIjowLCJpc1BtIjoxLCJpc1Jld2FyZCI6MSwiYXBwcyI6WyJ0bTNkIl0sImNyZWF0ZWRfYXQiOiIyMDI1LTA1LTE1IDE3OjMxOjM5IiwidXBkYXRlZF9hdCI6IjIwMjUtMDUtMTUgMTc6MzY6NTgiLCJpYXQiOjE3NTM3NzUzMDIsImV4cCI6MTc1NDM4MDEwMn0.6MXLbwgPuJRXUv3PVmS1s5b2eznXu_Fd7BrBUCYlWeQ"
        }

        # 默认超时时间
        self.timeout = 30

    def _load_request_config(self) -> Dict[str, Any]:
        """从config.yaml加载post_gm配置"""
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'config.yaml')

            with open(config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)

            if 'projects' not in config:
                raise ValueError("配置文件中未找到projects节点")

            if self.project_name not in config['projects']:
                raise ValueError(f"项目 '{self.project_name}' 未在配置文件中找到")

            project_config = config['projects'][self.project_name]

            if 'post_gm' not in project_config:
                raise ValueError(f"项目 '{self.project_name}' 中未找到post_gm配置")

            post_gm_config = project_config['post_gm']

            # 验证必要的配置项
            if 'url' not in post_gm_config:
                raise ValueError("post_gm配置缺少必要参数: url")

            self.logger.info(f"成功加载项目 '{self.project_name}' 的post_gm配置")
            return post_gm_config

        except FileNotFoundError:
            raise FileNotFoundError("配置文件 config.yaml 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        except Exception as e:
            self.logger.error(f"加载post_gm配置失败: {e}")
            raise

    def _build_url(self, route: str) -> str:
        """
        构建完整的请求URL
        
        Args:
            route: 路由路径（如 "/reset"）
            
        Returns:
            str: 完整的URL
        """
        # 确保base_url不以/结尾，route以/开头
        base_url = self.base_url.rstrip('/')
        route = route if route.startswith('/') else f'/{route}'

        full_url = f"{base_url}{route}"
        self.logger.debug(f"构建URL: {base_url} + {route} = {full_url}")
        return full_url

    def post(self, route: str, data: Dict[str, Any], headers: Dict[str, str] = None,
             timeout: int = None, verify: bool = True) -> requests.Response:
        """
        发送POST请求
        
        Args:
            route: 路由路径（如 "/reset"）
            data: 请求体数据（字典格式，会自动转换为JSON）
            headers: 额外的请求头
            timeout: 超时时间（秒）
            verify: 是否验证SSL证书
            
        Returns:
            requests.Response: 响应对象
        """
        try:
            # 构建完整URL
            url = self._build_url(route)

            # 合并请求头
            request_headers = self.default_headers.copy()
            if headers:
                request_headers.update(headers)

            # 设置超时时间
            request_timeout = timeout or self.timeout

            # 记录请求信息
            self.logger.info(f"发送POST请求到: {url}")
            self.logger.debug(f"请求头: {request_headers}")
            self.logger.debug(f"请求体: {json.dumps(data, ensure_ascii=False, indent=2)}")

            # 发送请求
            response = requests.post(
                url=url,
                json=data,
                headers=request_headers,
                timeout=request_timeout,
                verify=verify
            )

            # 记录响应信息
            self.logger.info(f"响应状态码: {response.status_code}")
            self.logger.debug(f"响应头: {dict(response.headers)}")

            try:
                response_data = response.json()
                self.logger.info(f"响应体: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
            except (json.JSONDecodeError, ValueError):
                self.logger.info(f"响应体(非JSON): {response.text[:500]}...")

            return response

        except requests.exceptions.Timeout:
            self.logger.error(f"请求超时: {url}")
            raise
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"连接错误: {e}")
            raise
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求异常: {e}")
            raise
        except Exception as e:
            self.logger.error(f"发送POST请求失败: {e}")
            raise

    def post_and_parse(self, route: str, data: Dict[str, Any], headers: Dict[str, str] = None,
                       timeout: int = None, verify: bool = True) -> Dict[str, Any]:
        """
        发送POST请求并解析JSON响应
        
        Args:
            route: 路由路径
            data: 请求体数据
            headers: 额外的请求头
            timeout: 超时时间
            verify: 是否验证SSL证书
            
        Returns:
            Dict[str, Any]: 解析后的JSON响应
        """
        try:
            response = self.post(route, data, headers, timeout, verify)

            # 检查响应状态
            if not response.ok:
                self.logger.warning(f"请求返回非成功状态码: {response.status_code}")

            # 解析JSON响应
            try:
                return response.json()
            except (json.JSONDecodeError, ValueError) as e:
                self.logger.error(f"响应不是有效的JSON格式: {e}")
                return {
                    "error": "invalid_json_response",
                    "status_code": response.status_code,
                    "raw_response": response.text
                }

        except Exception as e:
            self.logger.error(f"POST请求并解析失败: {e}")
            raise

    def post_user_reset(self, firebase_id: str, remark: str = "qa_tools", app_name: str = None) -> Dict[str, Any]:
        """
        发送用户重置请求（便捷方法）
        
        Args:
            firebase_id: Firebase用户ID
            remark: 备注信息
            app_name: 应用名称（如果为None，会根据项目名称自动设置）
            
        Returns:
            Dict[str, Any]: 响应结果
        """
        try:
            # 自动设置app_name
            if app_name is None:
                app_name_mapping = {
                    'TM3D': 'tm3d',
                    'Merge': 'merge'
                }
                app_name = app_name_mapping.get(self.project_name, self.project_name.lower())

            # 构建请求数据
            request_data = {
                "fid": firebase_id,
                "remark": remark,
                "appName": app_name
            }

            self.logger.info(f"发送用户重置请求: Firebase ID={firebase_id}, App={app_name}")

            # 发送请求
            return self.post_and_parse("/reset", request_data)

        except Exception as e:
            self.logger.error(f"用户重置请求失败: {e}")
            raise

    def test_connection(self) -> bool:
        """
        测试连接到GM服务器
        
        Returns:
            bool: 是否连接成功
        """
        try:
            # 尝试发送一个简单的请求来测试连接
            test_data = {"test": "connection"}
            response = self.post("/health", test_data, timeout=10)

            self.logger.info("GM服务器连接测试成功")
            return True

        except requests.exceptions.Timeout:
            self.logger.warning("GM服务器连接超时")
            return False
        except requests.exceptions.ConnectionError:
            self.logger.warning("GM服务器连接失败")
            return False
        except Exception as e:
            self.logger.warning(f"GM服务器连接测试异常: {e}")
            return False

    def get_config(self) -> Dict[str, Any]:
        """
        获取当前项目的post_gm配置信息
        
        Returns:
            Dict[str, Any]: post_gm配置信息
        """
        config_copy = self.config.copy()
        config_copy['project_name'] = self.project_name
        config_copy['timeout'] = self.timeout
        return config_copy

    def set_timeout(self, timeout: int):
        """
        设置请求超时时间
        
        Args:
            timeout: 超时时间（秒）
        """
        self.timeout = timeout
        self.logger.debug(f"设置请求超时时间: {timeout}秒")

    def set_headers(self, headers: Dict[str, str]):
        """
        设置默认请求头
        
        Args:
            headers: 请求头字典
        """
        self.default_headers.update(headers)
        self.logger.debug(f"更新默认请求头: {headers}")

    def add_auth_header(self, token: str, auth_type: str = "Bearer"):
        """
        添加认证头
        
        Args:
            token: 认证token
            auth_type: 认证类型（Bearer, Basic等）
        """
        auth_header = f"{auth_type} {token}"
        self.set_headers({"Authorization": auth_header})
        self.logger.debug(f"添加认证头: {auth_type} ***")

    # 便捷方法 - 常用的GM操作
    def post_user_ban(self, firebase_id: str, reason: str = "qa_test", duration: int = 3600) -> Dict[str, Any]:
        """
        封禁用户（便捷方法）
        
        Args:
            firebase_id: Firebase用户ID
            reason: 封禁原因
            duration: 封禁时长（秒）
            
        Returns:
            Dict[str, Any]: 响应结果
        """
        request_data = {
            "fid": firebase_id,
            "reason": reason,
            "duration": duration
        }

        self.logger.info(f"发送用户封禁请求: Firebase ID={firebase_id}, 时长={duration}秒")
        return self.post_and_parse("/ban", request_data)

    def post_user_unban(self, firebase_id: str, reason: str = "qa_test") -> Dict[str, Any]:
        """
        解封用户（便捷方法）
        
        Args:
            firebase_id: Firebase用户ID
            reason: 解封原因
            
        Returns:
            Dict[str, Any]: 响应结果
        """
        request_data = {
            "fid": firebase_id,
            "reason": reason
        }

        self.logger.info(f"发送用户解封请求: Firebase ID={firebase_id}")
        return self.post_and_parse("/unban", request_data)

    def post_add_currency(self, firebase_id: str, currency_type: str, amount: int,
                          reason: str = "qa_test") -> Dict[str, Any]:
        """
        添加游戏货币（便捷方法）
        
        Args:
            firebase_id: Firebase用户ID
            currency_type: 货币类型（如 "gold", "gem"）
            amount: 数量
            reason: 添加原因
            
        Returns:
            Dict[str, Any]: 响应结果
        """
        request_data = {
            "fid": firebase_id,
            "type": currency_type,
            "amount": amount,
            "reason": reason
        }

        self.logger.info(f"发送添加货币请求: Firebase ID={firebase_id}, 类型={currency_type}, 数量={amount}")
        return self.post_and_parse("/add_currency", request_data)
