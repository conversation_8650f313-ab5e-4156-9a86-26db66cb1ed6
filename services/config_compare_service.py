#!/usr/bin/env python3
"""
简化的配置表对比服务
功能：
1. 获取指定时间段内的commit变更
2. 提取Excel文件变更
3. 转换Excel为CSV并进行对比
4. 生成对比报告
"""

import os
import sys
import yaml
import pandas as pd
import shutil
import gitlab
import uuid
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List
import threading
import time
import base64
import json
import psycopg2
from psycopg2.extras import RealDictCursor

# 添加services目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from log_common import LogService


class SimpleConfigCompareService:
    # 版本标识 - 用于确认新功能已加载
    VERSION = "2025-01-28-v2-with-chinese-translation"

    def __init__(self, project_name: str, task_id: str = None):
        self.project_name = project_name
        self.task_id = task_id or f"{project_name}_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        self.logger = LogService(f"SimpleConfigCompare-{project_name}")

        # 记录版本信息
        self.logger.info(f"SimpleConfigCompareService 版本: {self.VERSION}")
        
        # 基础路径
        self.base_dir = Path(__file__).parent.parent
        self.work_dir = self.base_dir / "services" / "config_test" / self.task_id
        self.result_dir = self.base_dir / "static" / "reports" / self.task_id  # 改为static目录下，便于Web访问
        
        # 确保目录存在
        self.work_dir.mkdir(parents=True, exist_ok=True)
        self.result_dir.mkdir(parents=True, exist_ok=True)
        
        # 进度跟踪
        self.progress = 0
        self.status_message = ""
        self.is_running = False
        
        self._load_config()
        
        self.gitlab_client = None
        self.project = None
        
    def _load_config(self):
        """加载配置文件"""
        try:
            config_path = self.base_dir / "config" / "config.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 获取项目配置
            project_config = config.get('projects', {}).get(self.project_name, {})
            git_config = project_config.get('git', {})
            pgsql_config = project_config.get('pgsql', {})
            
            self.git_config = {
                'base_url': git_config.get('url', '').rstrip('/'),
                'project_id': git_config.get('project_ID'),
                'project_name': git_config.get('project_name'),  # 添加项目名称
                'token': git_config.get('token'),
                'excel_dir': git_config.get('excel_dir', 'excel'),
                'excel_dir_ignore': git_config.get('excel_dir_ignore', [])
            }
            
            self.pgsql_config = {
                'host': pgsql_config.get('url'),
                'port': pgsql_config.get('port', 5432),
                'database': pgsql_config.get('database'),
                'user': pgsql_config.get('user'),
                'password': pgsql_config.get('password')
            }
            
            # 对比脚本路径
            self.compare_script = self.base_dir / "config" / "compare_folder.txt"

            # 测试BCompare安装
            # self._test_bcompare_installation()

            self.logger.info(f"配置加载成功: {self.project_name}")
            
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            raise

    # def _test_bcompare_installation(self):
    #     """测试BCompare安装和配置"""
    #     try:
    #         # 测试BCompare是否可执行
    #         bcompare_cmd = self._get_bcompare_command()
    #         redirect_cmd = " >nul 2>&1" if os.name == 'nt' else " >/dev/null 2>&1"
    #         test_cmd = f"{bcompare_cmd} /?"
    #         result = os.system(test_cmd + redirect_cmd)  # 重定向输出避免显示
    #
    #         if result == 0:
    #             self.logger.info("BCompare安装检测成功")
    #         else:
    #             self.logger.warning(f"BCompare可能未正确安装或不在PATH中，测试返回码: {result}")
    #
    #         # 检查脚本文件是否存在
    #         if self.compare_script.exists():
    #             self.logger.info(f"BCompare脚本文件存在: {self.compare_script}")
    #         else:
    #             self.logger.error(f"BCompare脚本文件不存在: {self.compare_script}")
    #
    #     except Exception as e:
    #         self.logger.warning(f"BCompare安装测试失败: {e}")

    def _get_bcompare_command(self):
        """获取跨平台的BCompare命令"""
        import platform

        system = platform.system().lower()

        if system == 'windows':
            # Windows系统使用BComp.exe
            return 'BComp.exe'
        elif system == 'darwin':
            # macOS系统使用bcomp
            # 检查是否安装在Applications目录
            mac_paths = [
                '/Applications/Beyond Compare.app/Contents/MacOS/bcomp',
                '/usr/local/bin/bcomp',
                'bcomp'  # 如果在PATH中
            ]

            for path in mac_paths:
                if os.path.exists(path) or path == 'bcomp':
                    self.logger.info(f"使用macOS BCompare命令: {path}")
                    return path

            self.logger.warning("未找到macOS版本的BCompare，使用默认命令")
            return 'bcomp'
        elif system == 'linux':
            # Linux系统使用bcompare
            return 'bcompare'
        else:
            # 其他系统使用默认命令
            self.logger.warning(f"未知操作系统: {system}，使用默认BCompare命令")
            return 'bcomp'

    def get_db_connection(self):
        """获取数据库连接"""
        try:
            conn = psycopg2.connect(
                host=self.pgsql_config['host'],
                port=self.pgsql_config['port'],
                database=self.pgsql_config['database'],
                user=self.pgsql_config['user'],
                password=self.pgsql_config['password']
            )
            return conn
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise

    def init_db_tables(self):
        """初始化数据库表"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # 创建项目特定的schema（如果不存在）
            schema_name = self.project_name.lower()
            cursor.execute(f"CREATE SCHEMA IF NOT EXISTS {schema_name}")

            # 创建历史记录表 - 在项目schema中
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {schema_name}.config_compare_history (
                id SERIAL PRIMARY KEY,
                task_id VARCHAR(255) UNIQUE NOT NULL,
                project_name VARCHAR(100) NOT NULL,
                branch VARCHAR(100) NOT NULL,
                start_time TIMESTAMP WITH TIME ZONE NOT NULL,
                end_time TIMESTAMP WITH TIME ZONE NOT NULL,
                total_files INTEGER DEFAULT 0,
                changed_files INTEGER DEFAULT 0,
                added_files INTEGER DEFAULT 0,
                deleted_files INTEGER DEFAULT 0,
                status VARCHAR(20) DEFAULT 'completed',
                result_data JSONB,
                html_reports JSONB,
                completed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );

            CREATE INDEX IF NOT EXISTS idx_config_compare_project_time
            ON {schema_name}.config_compare_history (project_name, completed_at DESC);
            """

            cursor.execute(create_table_sql)

            # 检查并添加缺失的字段（数据库迁移）
            self._migrate_database_schema(cursor, schema_name)

            conn.commit()
            cursor.close()
            conn.close()

            self.logger.info(f"数据库表初始化完成 - Schema: {schema_name}")

        except Exception as e:
            self.logger.error(f"数据库表初始化失败: {e}")
            raise

    def _migrate_database_schema(self, cursor, schema_name):
        """数据库schema迁移"""
        try:
            # 检查html_reports字段是否存在
            cursor.execute(f"""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = '{schema_name}'
                AND table_name = 'config_compare_history'
                AND column_name = 'html_reports'
            """)

            if not cursor.fetchone():
                # 添加html_reports字段
                cursor.execute(f"""
                    ALTER TABLE {schema_name}.config_compare_history
                    ADD COLUMN html_reports JSONB
                """)
                self.logger.info(f"添加html_reports字段到表 {schema_name}.config_compare_history")

        except Exception as e:
            self.logger.warning(f"数据库迁移失败: {e}")
            # 迁移失败不应该阻止系统运行

    def init_gitlab(self):
        """初始化GitLab客户端"""
        try:
            self.gitlab_client = gitlab.Gitlab(
                self.git_config['base_url'],
                private_token=self.git_config['token']
            )
            self.project = self.gitlab_client.projects.get(self.git_config['project_id'])
            self.logger.info("GitLab客户端初始化成功")
        except Exception as e:
            self.logger.error(f"GitLab客户端初始化失败: {e}")
            raise

    def get_commit_before_time(self, branch: str, target_time: datetime) -> str:
        """获取指定时间之前的最近提交"""
        try:
            until_time = target_time.isoformat()
            commits = self.project.commits.list(
                per_page=1,
                query_parameters={
                    'until': until_time,
                    'ref_name': branch
                }
            )
            
            if not commits:
                raise ValueError(f"未找到时间 {target_time} 之前的提交")
            
            commit = commits[0]
            self.logger.info(f"找到提交: {commit.short_id} - {commit.title}")
            return commit.id
            
        except Exception as e:
            self.logger.error(f"获取提交失败: {e}")
            raise

    def get_changed_files(self, branch: str, start_time: str, end_time: str) -> List[str]:
        """获取时间段内变更的Excel文件"""
        try:
            self.logger.info("获取变更文件列表...")
            
            # 使用新的查询方式获取所有commit
            excel_dir = self.git_config['excel_dir']
            path_commits = self.project.commits.list(
                get_all=True,
                query_parameters={
                    'since': start_time,
                    'until': end_time,
                    'path': excel_dir,
                    'ref_name': branch
                }
            )
            
            self.logger.info(f"找到 {len(path_commits)} 个相关提交")
            
            # 收集所有变更的文件（去重）
            changed_files = set()
            
            for commit in path_commits:
                try:
                    # 获取提交的差异
                    diffs = commit.diff(get_all=True)
                    
                    for diff in diffs:
                        file_path = diff.get('new_path') or diff.get('old_path')
                        
                        # 只处理Excel文件
                        if file_path and file_path.endswith('.xlsx'):
                            # 检查是否在Excel目录下
                            if self._is_in_excel_dir(file_path, excel_dir):
                                # 检查是否被忽略
                                if not self._is_ignored(file_path):
                                    changed_files.add(file_path)
                                    self.logger.debug(f"发现Excel文件变更: {file_path}")
                
                except Exception as e:
                    self.logger.warning(f"处理提交 {commit.short_id} 失败: {e}")
                    continue
            
            result = list(changed_files)
            self.logger.info(f"最终找到 {len(result)} 个Excel文件变更")
            return result
            
        except Exception as e:
            self.logger.error(f"获取变更文件失败: {e}")
            return []

    def _is_in_excel_dir(self, file_path: str, excel_dir: str) -> bool:
        """检查文件是否在Excel目录下"""
        if not excel_dir or not file_path:
            return False
        return file_path.startswith(excel_dir) or excel_dir in file_path

    def _is_ignored(self, file_path: str) -> bool:
        """检查文件是否被忽略"""
        ignore_patterns = self.git_config.get('excel_dir_ignore', [])
        if isinstance(ignore_patterns, str):
            ignore_patterns = [ignore_patterns]
        
        for pattern in ignore_patterns:
            if pattern and pattern in file_path:
                return True
        return False

    def download_file_content(self, file_path: str, commit_sha: str) -> bytes:
        """下载指定提交的文件内容"""
        try:
            file_content = self.project.files.get(
                file_path=file_path,
                ref=commit_sha
            )
            return base64.b64decode(file_content.content)
        except Exception as e:
            self.logger.error(f"下载文件失败 {file_path}@{commit_sha}: {e}")
            raise

    def get_file_latest_commit(self, file_path: str, branch: str) -> str:
        """获取文件在指定分支上的最新commit ID"""
        try:
            # 获取文件的最新提交记录
            commits = self.project.commits.list(
                per_page=1,
                query_parameters={
                    'path': file_path,
                    'ref_name': branch
                }
            )
            
            if commits:
                return commits[0].id
            else:
                self.logger.warning(f"未找到文件 {file_path} 在分支 {branch} 上的提交")
                return None
                
        except Exception as e:
            self.logger.error(f"获取文件最新提交失败 {file_path}: {e}")
            return None

    def excel_to_csv(self, excel_path: Path, output_dir: Path):
        """将Excel文件转换为CSV文件"""
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 读取Excel文件
            excel_file = pd.ExcelFile(excel_path)
            
            for sheet_name in excel_file.sheet_names:
                try:
                    # 完全强制的CSV标题行处理 - 无条件将第一行作为标题
                    df_raw = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)

                    if df_raw.empty:
                        self.logger.warning(f"工作表 {sheet_name} 为空，跳过")
                        continue

                    # 无条件强制处理：始终将第一行作为标题
                    first_row = df_raw.iloc[0]
                    column_names = []

                    for i, val in enumerate(first_row):
                        if pd.notna(val) and str(val).strip():
                            # 清理列名，移除特殊字符，确保是有效的列名
                            col_name = str(val).strip()
                            col_name = col_name.replace('\n', '_').replace('\r', '_').replace('\t', '_')
                            col_name = col_name.replace('"', '').replace("'", "")
                            # 如果列名为空或只有特殊字符，使用默认名称
                            if not col_name or col_name.isspace():
                                col_name = f"Column_{i+1}"
                            column_names.append(col_name)
                        else:
                            column_names.append(f"Column_{i+1}")

                    # 确保列名唯一性
                    seen = set()
                    unique_columns = []
                    for col in column_names:
                        if col in seen:
                            counter = 1
                            new_col = f"{col}_{counter}"
                            while new_col in seen:
                                counter += 1
                                new_col = f"{col}_{counter}"
                            unique_columns.append(new_col)
                            seen.add(new_col)
                        else:
                            unique_columns.append(col)
                            seen.add(col)

                    # 创建DataFrame：从第二行开始作为数据，第一行作为列名
                    if len(df_raw) > 1:
                        df = df_raw.iloc[1:].copy()
                        df.columns = unique_columns
                        df.reset_index(drop=True, inplace=True)
                    else:
                        # 如果只有一行，创建空的DataFrame但保留列名
                        df = pd.DataFrame(columns=unique_columns)

                    # 生成CSV文件名：文件名+sheet名.csv
                    file_base_name = excel_path.stem
                    # 清理文件名中的空格，替换为下划线
                    clean_file_name = file_base_name.replace(' ', '_')
                    # 清理sheet名中的特殊字符
                    clean_sheet_name = self._clean_sheet_name(sheet_name)
                    csv_filename = f"{clean_file_name}_{clean_sheet_name}.csv"
                    csv_path = output_dir / csv_filename

                    # 强制保存为CSV，确保标题行存在，使用utf-8-sig处理BOM
                    df.to_csv(csv_path, index=False, encoding='utf-8-sig', header=True)

                    # 验证CSV文件是否正确生成
                    if csv_path.exists():
                        # 读取生成的CSV验证标题行，使用utf-8-sig自动处理BOM
                        with open(csv_path, 'r', encoding='utf-8-sig') as f:
                            first_line = f.readline().strip()
                            self.logger.debug(f"CSV标题行: {first_line}")

                    self.logger.debug(f"强制转换CSV: {csv_filename} (列数: {len(unique_columns)})")

                except Exception as e:
                    self.logger.warning(f"转换工作表 {sheet_name} 失败: {e}")
            
            excel_file.close()
            
            # 删除原始Excel文件
            try:
                excel_path.unlink()
                self.logger.debug(f"删除原始Excel文件: {excel_path}")
            except Exception as e:
                self.logger.warning(f"删除Excel文件失败: {e}")
            
        except Exception as e:
            self.logger.error(f"Excel转CSV失败: {e}")
            raise

    def _clean_sheet_name(self, sheet_name: str) -> str:
        """清理工作表名称中的特殊字符"""
        # 移除或替换可能导致文件系统和命令行问题的字符
        clean_name = sheet_name

        # 替换特殊字符
        replacements = {
            '#': 'num',      # 井号替换为num
            '*': 'star',     # 星号替换为star
            '?': 'q',        # 问号替换为q
            '<': 'lt',       # 小于号
            '>': 'gt',       # 大于号
            '|': 'pipe',     # 管道符
            '"': 'quote',    # 双引号
            ':': 'colon',    # 冒号
            '/': 'slash',    # 斜杠
            '\\': 'bslash',  # 反斜杠
            ' ': '_',        # 空格替换为下划线
            '\t': '_',       # 制表符
            '\n': '_',       # 换行符
            '\r': '_'        # 回车符
        }

        for old_char, new_char in replacements.items():
            clean_name = clean_name.replace(old_char, new_char)

        # 移除其他可能有问题的字符，只保留安全字符
        clean_name = re.sub(r'[^\w\-_.]', '_', clean_name)

        # 移除连续的下划线
        clean_name = re.sub(r'_+', '_', clean_name)

        # 移除开头和结尾的下划线
        clean_name = clean_name.strip('_')

        # 确保不为空
        if not clean_name:
            clean_name = 'sheet'

        return clean_name

    def _detect_csv_header(self, df):
        """检测DataFrame是否有标题行"""
        try:
            if df.empty or len(df) < 2:
                return False

            # 获取第一行和第二行
            first_row = df.iloc[0]
            second_row = df.iloc[1] if len(df) > 1 else None

            # 检测标题行的特征
            header_indicators = 0
            total_columns = len(first_row)

            for i, (first_val, second_val) in enumerate(zip(first_row, second_row if second_row is not None else [None] * len(first_row))):
                # 转换为字符串进行比较
                first_str = str(first_val).strip() if pd.notna(first_val) else ""
                second_str = str(second_val).strip() if pd.notna(second_val) and second_val is not None else ""

                # 标题行特征检测
                if first_str:
                    # 1. 第一行包含文字，第二行包含数字
                    if first_str.isalpha() or any(char.isalpha() for char in first_str):
                        if second_str.replace('.', '').replace('-', '').isdigit():
                            header_indicators += 1

                    # 2. 第一行是常见的标题词汇
                    common_headers = ['id', 'name', 'type', 'value', 'level', 'count', 'time', 'desc', 'description']
                    if any(header.lower() in first_str.lower() for header in common_headers):
                        header_indicators += 1

                    # 3. 第一行包含中文且看起来像标题
                    if any('\u4e00' <= char <= '\u9fff' for char in first_str):
                        if len(first_str) < 20:  # 标题通常较短
                            header_indicators += 1

            # 如果超过30%的列看起来像标题，则认为有标题行
            threshold = max(1, total_columns * 0.3)
            has_header = header_indicators >= threshold

            self.logger.debug(f"标题检测: {header_indicators}/{total_columns} 列像标题，阈值: {threshold}, 结果: {has_header}")
            return has_header

        except Exception as e:
            self.logger.warning(f"标题行检测失败: {e}")
            return True  # 默认认为有标题行

    def process_file(self, file_path: str, start_commit: str, end_commit: str, branch: str) -> Dict:
        """处理单个文件的对比"""
        try:
            # 创建文件专用目录
            file_name = Path(file_path).name
            file_base_name = Path(file_path).stem
            # 清理文件名中的空格，替换为下划线
            clean_file_base_name = file_base_name.replace(' ', '_')
            file_dir = self.work_dir / clean_file_base_name
            old_dir = file_dir / "old"
            new_dir = file_dir / "new"
            
            # 创建目录
            old_dir.mkdir(parents=True, exist_ok=True)
            new_dir.mkdir(parents=True, exist_ok=True)
            
            base_url = self.git_config['base_url']
            project_id = self.git_config['project_id']
            project_name = self.git_config['project_name']  # 获取项目名称
            
            # 获取文件的最新commit ID用于文件历史链接
            file_commit_id = self.get_file_latest_commit(file_path, branch)
            
            result = {
                'file_path': file_path,
                'file_name': file_name,
                'status': 'success',
                'has_old': False,
                'has_new': False,
                'report_url': None,
                'commit_url': None,
                'file_history_url': f"{self.project.web_url}/-/commit/{file_commit_id}" if file_commit_id else None
            }
            
            # 下载并处理旧版本文件
            try:
                old_content = self.download_file_content(file_path, start_commit)
                old_excel_path = old_dir / file_name
                
                with open(old_excel_path, 'wb') as f:
                    f.write(old_content)
                
                # 转换为CSV
                self.excel_to_csv(old_excel_path, old_dir)
                result['has_old'] = True
                self.logger.info(f"处理旧版本完成: {file_path}")
                
            except Exception as e:
                self.logger.warning(f"处理旧版本失败: {file_path} - {e}")
            
            # 下载并处理新版本文件
            try:
                new_content = self.download_file_content(file_path, end_commit)
                new_excel_path = new_dir / file_name
                
                with open(new_excel_path, 'wb') as f:
                    f.write(new_content)
                
                # 转换为CSV
                self.excel_to_csv(new_excel_path, new_dir)
                result['has_new'] = True
                self.logger.info(f"处理新版本完成: {file_path}")
                
            except Exception as e:
                self.logger.warning(f"处理新版本失败: {file_path} - {e}")
            
            # 根据文件状态设置相应信息
            if result['has_old'] and result['has_new']:
                # 两个版本都存在，进行对比
                report_path = self.result_dir / f"{clean_file_base_name}.html"
                success = self.compare_folders(old_dir, new_dir, report_path, clean_file_base_name)

                if success:
                    result['report_url'] = f"/static/reports/{self.task_id}/{clean_file_base_name}.html"
                    self.logger.info(f"对比完成: {file_path}")
                else:
                    result['status'] = 'compare_failed'
            elif not result['has_old'] and result['has_new']:
                # 新增文件
                result['status'] = 'added'
                result['commit_url'] = f"{self.project.web_url}/-/commit/{end_commit}"
            elif result['has_old'] and not result['has_new']:
                # 删除文件
                result['status'] = 'deleted'
                result['commit_url'] = f"{self.project.web_url}/-/commit/{end_commit}"
            else:
                # 异常情况
                result['status'] = 'error'
                result['error'] = '无法获取文件的任何版本'
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理文件失败 {file_path}: {e}")
            return {
                'file_path': file_path,
                'file_name': Path(file_path).name,
                'status': 'error',
                'error': str(e)
            }

    def compare_folders(self, old_dir: Path, new_dir: Path, report_path: Path, title: str) -> bool:
        """使用BCompare对比文件夹"""
        try:
            # 确保报告目录存在
            report_path.parent.mkdir(parents=True, exist_ok=True)

            # 清理标题中的特殊字符，避免命令行问题
            clean_title = self._clean_title(title)

            # 检查路径是否包含特殊字符，如果是则使用安全路径方案
            if self._has_unsafe_path_chars(str(old_dir)) or self._has_unsafe_path_chars(str(new_dir)):
                self.logger.info(f"检测到路径包含特殊字符，使用安全路径方案: {title}")
                return self._compare_with_safe_paths(old_dir, new_dir, report_path, clean_title)

            # 构建BCompare命令字符串 - 跨平台兼容
            bcompare_cmd = self._get_bcompare_command()
            cmd = f'{bcompare_cmd} /silent @"{self.compare_script}" "{old_dir}" "{new_dir}" "{report_path}" "{clean_title}"'

            self.logger.info(f"执行对比: {title}")
            self.logger.debug(f"执行命令: {cmd}")
            self.logger.debug(f"脚本路径: {self.compare_script}")
            self.logger.debug(f"旧目录: {old_dir}")
            self.logger.debug(f"新目录: {new_dir}")
            self.logger.debug(f"报告路径: {report_path}")

            # 检查脚本文件是否存在
            if not self.compare_script.exists():
                self.logger.error(f"BCompare脚本文件不存在: {self.compare_script}")
                return False

            # 使用os.system执行命令
            result_code = os.system(cmd)

            # 详细的返回码分析
            self._analyze_bcompare_result(result_code, report_path, title)

            # 如果返回码是106或107，尝试使用安全路径方案
            if result_code == 106 or result_code == 107:
                self.logger.warning(f"主脚本失败（返回码{result_code}），尝试安全路径对比: {title}")
                success = self._compare_with_safe_paths(old_dir, new_dir, report_path, clean_title)
                if success:
                    return True
                else:
                    self.logger.error(f"安全路径对比也失败: {title}")
                    return False

            if result_code == 0 or report_path.exists():
                self.logger.info(f"对比完成: {report_path}")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"对比执行失败: {e}")
            return False

    def _clean_title(self, title: str) -> str:
        """清理标题中的特殊字符"""
        import re
        # 移除或替换可能导致命令行问题的字符
        clean_title = re.sub(r'[<>:"/\\|?*]', '_', title)
        clean_title = re.sub(r'\s+', '_', clean_title)  # 替换空格
        # 处理中文字符和其他特殊字符
        clean_title = re.sub(r'[^\w\-_.]', '_', clean_title)  # 只保留字母数字下划线横线和点
        # 移除连续的下划线
        clean_title = re.sub(r'_+', '_', clean_title)
        # 移除开头和结尾的下划线
        clean_title = clean_title.strip('_')
        return clean_title[:50]  # 限制长度

    def _analyze_bcompare_result(self, result_code: int, report_path: Path, title: str):
        """分析BCompare返回码"""
        code_meanings = {
            0: "成功",
            1: "二进制相同",
            2: "基于规则相同",
            11: "二进制差异",
            12: "相似",
            13: "基于规则差异",
            14: "检测到冲突",
            100: "未知错误",
            101: "检测到冲突，合并输出未写入",
            102: "BComp.exe无法等待BCompare.exe完成",
            103: "BComp.exe找不到BCompare.exe",
            104: "试用期已过期",
            105: "加载脚本文件错误",
            106: "脚本语法错误",
            107: "脚本加载文件夹或文件失败"
        }

        meaning = code_meanings.get(result_code, f"未知返回码: {result_code}")

        if result_code == 0:
            self.logger.info(f"BCompare执行成功: {meaning}")
        elif result_code in [1, 2, 11, 12, 13]:
            self.logger.info(f"BCompare完成对比: {meaning}")
        else:
            self.logger.warning(f"BCompare返回码 {result_code}: {meaning}")

        # 检查报告文件是否生成
        if report_path.exists():
            file_size = report_path.stat().st_size
            self.logger.info(f"报告文件已生成: {report_path} (大小: {file_size} bytes)")
        else:
            self.logger.warning(f"报告文件未生成: {report_path}")



    def _has_unsafe_path_chars(self, path: str) -> bool:
        """检查路径是否包含不安全的字符"""
        # 检查空格、中文字符、特殊符号
        import unicodedata
        for char in path:
            # 检查是否为中文字符
            if '\u4e00' <= char <= '\u9fff':
                return True
            # 检查特殊字符
            if char in [' ', '#', '&', '(', ')', '[', ']', '@', '%', '!', '$']:
                return True
        return False

    def _compare_with_safe_paths(self, old_dir: Path, new_dir: Path, report_path: Path, title: str) -> bool:
        """使用安全路径进行对比"""
        try:
            # 创建临时安全目录
            import tempfile
            temp_base = Path(tempfile.gettempdir()) / "bcompare_safe"
            temp_base.mkdir(exist_ok=True)

            # 生成完全安全的目录名（只保留ASCII字母数字）
            safe_name = self._generate_safe_name(title)
            temp_old = temp_base / f"{safe_name}_old"
            temp_new = temp_base / f"{safe_name}_new"

            # 清理旧的临时目录
            if temp_old.exists():
                shutil.rmtree(temp_old)
            if temp_new.exists():
                shutil.rmtree(temp_new)

            # 复制文件到安全路径
            self.logger.info(f"复制文件到安全路径: {temp_old}, {temp_new}")
            shutil.copytree(old_dir, temp_old)
            shutil.copytree(new_dir, temp_new)

            # 使用安全路径进行对比
            bcompare_cmd = self._get_bcompare_command()
            safe_title = "safe_compare"
            cmd = f'{bcompare_cmd} /silent @"{self.compare_script}" "{temp_old}" "{temp_new}" "{report_path}" "{safe_title}"'

            self.logger.info(f"使用安全路径执行对比: {title}")
            self.logger.debug(f"安全路径命令: {cmd}")

            result_code = os.system(cmd)
            self._analyze_bcompare_result(result_code, report_path, f"{title}_safe")

            # 清理临时文件
            try:
                shutil.rmtree(temp_old)
                shutil.rmtree(temp_new)
            except Exception as cleanup_error:
                self.logger.warning(f"清理临时文件失败: {cleanup_error}")

            if result_code == 0 or report_path.exists():
                self.logger.info(f"安全路径对比成功: {report_path}")
                return True
            else:
                self.logger.error(f"安全路径对比失败，返回码: {result_code}")
                return False

        except Exception as e:
            self.logger.error(f"安全路径对比失败: {e}")
            return False

    def _generate_safe_name(self, title: str) -> str:
        """生成完全安全的文件名（翻译中文为英文）"""
        import hashlib

        # 中文到英文的翻译映射
        chinese_to_english = {
            '埋点': 'tracking',
            '方案': 'plan',
            '配置': 'config',
            '多语言': 'multilang',
            '调用': 'call',
            '数据': 'data',
            '事件': 'event',
            '用户': 'user',
            '属性': 'property',
            '公共': 'common',
            '系统': 'system',
            '管理': 'manage',
            '设置': 'setting',
            '界面': 'interface',
            '显示': 'display',
            '奖励': 'reward',
            '进度': 'progress',
            '等级': 'level',
            '经验': 'exp',
            '金币': 'coin',
            '钻石': 'diamond',
            '道具': 'item',
            '卡片': 'card',
            '技能': 'skill',
            '任务': 'task',
            '成就': 'achievement',
            '排行榜': 'leaderboard',
            '商店': 'shop',
            '背包': 'inventory',
            '装备': 'equipment',
            '武器': 'weapon',
            '防具': 'armor',
            '宝石': 'gem',
            '材料': 'material',
            '合成': 'craft',
            '强化': 'enhance',
            '升级': 'upgrade',
            '战斗': 'battle',
            '副本': 'dungeon',
            '关卡': 'stage',
            '章节': 'chapter',
            '模式': 'mode',
            '难度': 'difficulty',
            '时间': 'time',
            '次数': 'count',
            '限制': 'limit',
            '条件': 'condition',
            '要求': 'requirement',
            '效果': 'effect',
            '描述': 'description',
            '名称': 'name',
            '类型': 'type',
            '品质': 'quality',
            '稀有度': 'rarity',
            '价格': 'price',
            '数量': 'amount',
            '概率': 'probability',
            '权重': 'weight',
            '比例': 'ratio',
            '百分比': 'percent',
            '倍数': 'multiplier',
            '加成': 'bonus',
            '减少': 'reduce',
            '增加': 'increase',
            '最大': 'max',
            '最小': 'min',
            '默认': 'default',
            '初始': 'initial',
            '当前': 'current',
            '目标': 'target',
            '结果': 'result',
            '状态': 'status',
            '标记': 'flag',
            '开关': 'switch',
            '启用': 'enable',
            '禁用': 'disable',
            '激活': 'active',
            '锁定': 'lock',
            '解锁': 'unlock',
            '开放': 'open',
            '关闭': 'close',
            '开始': 'start',
            '结束': 'end',
            '暂停': 'pause',
            '继续': 'continue',
            '重置': 'reset',
            '刷新': 'refresh',
            '更新': 'update',
            '保存': 'save',
            '加载': 'load',
            '删除': 'delete',
            '创建': 'create',
            '修改': 'modify',
            '编辑': 'edit',
            '查看': 'view',
            '搜索': 'search',
            '过滤': 'filter',
            '排序': 'sort',
            '分组': 'group',
            '分类': 'category',
            '标签': 'tag',
            '版本': 'version',
            '更新': 'update',
            '日志': 'log',
            '记录': 'record',
            '历史': 'history',
            '统计': 'statistics',
            '报告': 'report',
            '分析': 'analysis',
            '监控': 'monitor',
            '警告': 'warning',
            '错误': 'error',
            '异常': 'exception',
            '调试': 'debug',
            '测试': 'test',
            '验证': 'verify',
            '检查': 'check',
            '确认': 'confirm',
            '取消': 'cancel',
            '提交': 'submit',
            '发送': 'send',
            '接收': 'receive',
            '响应': 'response',
            '请求': 'request',
            '连接': 'connect',
            '断开': 'disconnect',
            '网络': 'network',
            '服务器': 'server',
            '客户端': 'client',
            '数据库': 'database',
            '缓存': 'cache',
            '队列': 'queue',
            '线程': 'thread',
            '进程': 'process',
            '服务': 'service',
            '接口': 'interface',
            '协议': 'protocol',
            '格式': 'format',
            '编码': 'encoding',
            '解码': 'decoding',
            '加密': 'encrypt',
            '解密': 'decrypt',
            '签名': 'signature',
            '验证': 'verify',
            '授权': 'authorize',
            '认证': 'authenticate',
            '权限': 'permission',
            '角色': 'role',
            '用户组': 'usergroup',
            '账号': 'account',
            '密码': 'password',
            '令牌': 'token',
            '会话': 'session',
            '登录': 'login',
            '登出': 'logout',
            '注册': 'register',
            '注销': 'unregister'
        }

        # 先进行中文翻译
        translated_title = title
        for chinese, english in chinese_to_english.items():
            translated_title = translated_title.replace(chinese, english)

        # 移除所有非ASCII字符，只保留字母数字
        safe_chars = []
        for char in translated_title:
            if char.isalnum() and ord(char) < 128:  # 只保留ASCII字母数字
                safe_chars.append(char)
            elif char in [' ', '_', '-']:
                safe_chars.append('_')

        safe_name = ''.join(safe_chars)

        # 移除连续的下划线
        safe_name = re.sub(r'_+', '_', safe_name)
        safe_name = safe_name.strip('_')

        # 如果名称为空或太短，使用hash
        if len(safe_name) < 3:
            hash_obj = hashlib.md5(title.encode('utf-8'))
            safe_name = f"file_{hash_obj.hexdigest()[:8]}"

        # 限制长度
        safe_name = safe_name[:30]  # 增加长度限制以容纳翻译后的英文

        self.logger.debug(f"安全名称生成: '{title}' -> '{translated_title}' -> '{safe_name}'")
        return safe_name

    def update_progress(self, progress: int, message: str):
        """更新进度"""
        self.progress = progress
        self.status_message = message
        self.logger.info(f"进度: {progress}% - {message}")

    def start_compare(self, branch: str, start_time: str, end_time: str) -> Dict:
        """开始配置对比"""
        try:
            self.is_running = True
            self.update_progress(0, "开始配置对比...")
            
            # 解析时间
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            
            self.update_progress(10, "初始化GitLab客户端...")
            self.init_gitlab()
            
            self.update_progress(20, "获取开始时间点提交...")
            start_commit = self.get_commit_before_time(branch, start_dt)
            
            self.update_progress(30, "获取结束时间点提交...")
            end_commit = self.get_commit_before_time(branch, end_dt)
            
            self.update_progress(40, "获取变更文件列表...")
            changed_files = self.get_changed_files(branch, start_time, end_time)
            
            if not changed_files:
                self.update_progress(100, "未发现Excel文件变化")
                return {
                    'task_id': self.task_id,
                    'project': self.project_name,
                    'branch': branch,
                    'start_time': start_time,
                    'end_time': end_time,
                    'total_files': 0,
                    'results': [],
                    'completed_at': datetime.now().isoformat()
                }
            
            # 处理文件变更
            results = []
            total_files = len(changed_files)
            
            for i, file_path in enumerate(changed_files):
                progress = 40 + int((i / total_files) * 50)
                self.update_progress(progress, f"处理文件: {Path(file_path).name}")
                
                result = self.process_file(file_path, start_commit, end_commit, branch)
                results.append(result)
            
            self.update_progress(100, "对比完成")
            self.is_running = False
            
            return {
                'task_id': self.task_id,
                'project': self.project_name,
                'branch': branch,
                'start_time': start_time,
                'end_time': end_time,
                'total_files': len(results),
                'changed_files': len([r for r in results if r['status'] == 'success']),
                'added_files': len([r for r in results if r['status'] == 'added']),
                'deleted_files': len([r for r in results if r['status'] == 'deleted']),
                'results': results,
                'completed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"配置对比失败: {e}")
            self.is_running = False
            raise

    def get_progress(self) -> Dict:
        """获取进度信息"""
        return {
            'progress': self.progress,
            'message': self.status_message,
            'is_running': self.is_running,
            'task_id': self.task_id
        }

    def cleanup(self):
        """清理临时文件"""
        try:
            if self.work_dir.exists():
                shutil.rmtree(self.work_dir, ignore_errors=True)
                self.logger.info(f"清理工作目录: {self.work_dir}")
        except Exception as e:
            self.logger.error(f"清理失败: {e}")

    def save_history_record(self, result: Dict):
        """保存历史记录到数据库"""
        try:
            # 初始化数据库表
            self.init_db_tables()
            
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # 获取项目schema名称
            schema_name = self.project_name.lower()
            
            # 读取并压缩HTML报告内容（如果存在）
            html_reports = self._collect_html_reports(result)
            
            # 插入历史记录 - 使用项目schema
            insert_sql = f"""
            INSERT INTO {schema_name}.config_compare_history (
                task_id, project_name, branch, start_time, end_time,
                total_files, changed_files, added_files, deleted_files,
                status, result_data, html_reports, completed_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            ) ON CONFLICT (task_id) DO UPDATE SET
                result_data = EXCLUDED.result_data,
                html_reports = EXCLUDED.html_reports,
                completed_at = EXCLUDED.completed_at
            """
            
            cursor.execute(insert_sql, (
                result['task_id'],
                result['project'],
                result['branch'],
                result['start_time'],
                result['end_time'],
                result['total_files'],
                result.get('changed_files', 0),
                result.get('added_files', 0),
                result.get('deleted_files', 0),
                'completed',
                json.dumps(result),
                json.dumps(html_reports) if html_reports else None,
                result['completed_at']
            ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            self.logger.info(f"历史记录已保存到数据库 - Schema: {schema_name}, Task: {result['task_id']}")
            
            # 安排HTML文件清理
            self._schedule_html_cleanup()
            
        except Exception as e:
            self.logger.error(f"保存历史记录失败: {e}")

    def _collect_html_reports(self, result: Dict) -> Dict:
        """收集HTML报告内容用于数据库存储"""
        html_reports = {}
        try:
            if result.get('results'):
                for file_result in result['results']:
                    if file_result.get('report_url'):
                        # 构建实际文件路径
                        report_path = self.base_dir / "static" / "reports" / self.task_id / f"{Path(file_result['file_path']).stem}.html"
                        
                        if report_path.exists():
                            # 读取HTML内容并压缩
                            with open(report_path, 'r', encoding='utf-8') as f:
                                html_content = f.read()
                            
                            # 简单压缩：移除多余空白
                            import re
                            compressed_html = re.sub(r'\s+', ' ', html_content).strip()
                            
                            html_reports[file_result['file_path']] = {
                                'html_content': compressed_html,
                                'file_size': len(html_content),
                                'compressed_size': len(compressed_html)
                            }
                            
                            self.logger.debug(f"收集HTML报告: {file_result['file_path']} ({len(html_content)} -> {len(compressed_html)} bytes)")
        
        except Exception as e:
            self.logger.warning(f"收集HTML报告失败: {e}")
        
        return html_reports

    def _schedule_html_cleanup(self):
        """安排HTML文件清理"""
        def cleanup_old_files():
            try:
                import time
                # 等待一段时间后清理（允许用户短期内查看报告）
                time.sleep(3600)  # 1小时后清理
                
                # 清理超过7天的HTML报告文件
                self._cleanup_old_html_reports(days_to_keep=7)
                
            except Exception as e:
                self.logger.error(f"清理HTML文件失败: {e}")
        
        # 在后台线程中执行清理
        cleanup_thread = threading.Thread(target=cleanup_old_files, daemon=True)
        cleanup_thread.start()

    def _cleanup_old_html_reports(self, days_to_keep: int = 7):
        """清理旧的HTML报告文件"""
        try:
            reports_dir = self.base_dir / "static" / "reports"
            if not reports_dir.exists():
                return
                
            import time
            cutoff_time = time.time() - (days_to_keep * 24 * 3600)
            
            cleaned_count = 0
            total_size_freed = 0
            
            for task_dir in reports_dir.iterdir():
                if task_dir.is_dir():
                    # 检查目录的创建时间
                    dir_mtime = task_dir.stat().st_mtime
                    
                    if dir_mtime < cutoff_time:
                        # 计算目录大小
                        dir_size = sum(f.stat().st_size for f in task_dir.rglob('*') if f.is_file())
                        
                        # 删除整个任务目录
                        shutil.rmtree(task_dir, ignore_errors=True)
                        
                        cleaned_count += 1
                        total_size_freed += dir_size
                        
                        self.logger.info(f"清理过期HTML报告目录: {task_dir.name} ({dir_size / 1024 / 1024:.2f} MB)")
            
            if cleaned_count > 0:
                self.logger.info(f"HTML清理完成: 删除 {cleaned_count} 个目录, 释放 {total_size_freed / 1024 / 1024:.2f} MB")
                
        except Exception as e:
            self.logger.error(f"清理HTML报告失败: {e}")

    @staticmethod
    def get_history_records(project_name: str, limit: int = 50) -> List[Dict]:
        """从数据库获取项目的历史记录"""
        try:
            # 需要获取数据库配置
            base_dir = Path(__file__).parent.parent
            config_path = base_dir / "config" / "config.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            project_config = config.get('projects', {}).get(project_name, {})
            pgsql_config = project_config.get('pgsql', {})
            
            conn = psycopg2.connect(
                host=pgsql_config.get('url'),
                port=pgsql_config.get('port', 5432),
                database=pgsql_config.get('database'),
                user=pgsql_config.get('user'),
                password=pgsql_config.get('password')
            )
            
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # 获取项目schema名称
            schema_name = project_name.lower()
            
            # 查询历史记录 - 使用项目schema
            select_sql = f"""
            SELECT task_id, project_name, branch, start_time, end_time,
                   total_files, changed_files, added_files, deleted_files,
                   status, completed_at
            FROM {schema_name}.config_compare_history 
            WHERE project_name = %s
            ORDER BY completed_at DESC
            LIMIT %s
            """
            
            cursor.execute(select_sql, (project_name, limit))
            records = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            # 转换为字典列表
            return [dict(record) for record in records]
            
        except Exception as e:
            print(f"获取历史记录失败: {e}")
            return []

    @staticmethod
    def get_history_record(project_name: str, task_id: str) -> Dict:
        """从数据库获取特定的历史记录"""
        try:
            # 需要获取数据库配置
            base_dir = Path(__file__).parent.parent
            config_path = base_dir / "config" / "config.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            project_config = config.get('projects', {}).get(project_name, {})
            pgsql_config = project_config.get('pgsql', {})
            
            conn = psycopg2.connect(
                host=pgsql_config.get('url'),
                port=pgsql_config.get('port', 5432),
                database=pgsql_config.get('database'),
                user=pgsql_config.get('user'),
                password=pgsql_config.get('password')
            )
            
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # 获取项目schema名称
            schema_name = project_name.lower()
            
            # 查询特定历史记录 - 使用项目schema
            select_sql = f"""
            SELECT result_data
            FROM {schema_name}.config_compare_history 
            WHERE project_name = %s AND task_id = %s
            """
            
            cursor.execute(select_sql, (project_name, task_id))
            record = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            if record and record['result_data']:
                return record['result_data']
            else:
                return None
                
        except Exception as e:
            print(f"获取历史记录失败: {e}")
            return None

    @staticmethod
    def get_html_report_from_db(project_name: str, task_id: str, file_path: str) -> str:
        """从数据库获取HTML报告内容"""
        try:
            base_dir = Path(__file__).parent.parent
            config_path = base_dir / "config" / "config.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            project_config = config.get('projects', {}).get(project_name, {})
            pgsql_config = project_config.get('pgsql', {})
            
            conn = psycopg2.connect(
                host=pgsql_config.get('url'),
                port=pgsql_config.get('port', 5432),
                database=pgsql_config.get('database'),
                user=pgsql_config.get('user'),
                password=pgsql_config.get('password')
            )
            
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            schema_name = project_name.lower()
            
            select_sql = f"""
            SELECT html_reports
            FROM {schema_name}.config_compare_history 
            WHERE project_name = %s AND task_id = %s
            """
            
            cursor.execute(select_sql, (project_name, task_id))
            record = cursor.fetchone()
            
            cursor.close()
            conn.close()
            
            if record and record['html_reports']:
                html_reports = json.loads(record['html_reports'])
                if file_path in html_reports:
                    return html_reports[file_path]['html_content']
            
            return None
            
        except Exception as e:
            print(f"从数据库获取HTML报告失败: {e}")
            return None

    @staticmethod
    def cleanup_old_database_records(project_name: str, retention_config: Dict = None) -> Dict:
        """清理旧的数据库记录"""
        if retention_config is None:
            retention_config = {
                'html_reports_retention_days': 30,  # HTML报告保留30天
                'metadata_retention_days': 90,      # 元数据保留90天
                'enable_cleanup': True
            }
        
        if not retention_config.get('enable_cleanup', True):
            return {'status': 'disabled', 'message': '数据库清理已禁用'}
        
        try:
            base_dir = Path(__file__).parent.parent
            config_path = base_dir / "config" / "config.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            project_config = config.get('projects', {}).get(project_name, {})
            pgsql_config = project_config.get('pgsql', {})
            
            conn = psycopg2.connect(
                host=pgsql_config.get('url'),
                port=pgsql_config.get('port', 5432),
                database=pgsql_config.get('database'),
                user=pgsql_config.get('user'),
                password=pgsql_config.get('password')
            )
            
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            schema_name = project_name.lower()
            
            # 第一阶段：清理HTML报告数据（保留元数据）
            html_retention_days = retention_config['html_reports_retention_days']
            html_cleanup_sql = f"""
            UPDATE {schema_name}.config_compare_history 
            SET html_reports = NULL
            WHERE completed_at < NOW() - INTERVAL '{html_retention_days} days'
              AND html_reports IS NOT NULL
            """
            
            cursor.execute(html_cleanup_sql)
            html_cleaned_count = cursor.rowcount
            
            # 第二阶段：清理完整记录（包括元数据）
            metadata_retention_days = retention_config['metadata_retention_days']
            metadata_cleanup_sql = f"""
            DELETE FROM {schema_name}.config_compare_history 
            WHERE completed_at < NOW() - INTERVAL '{metadata_retention_days} days'
            """
            
            cursor.execute(metadata_cleanup_sql)
            records_deleted_count = cursor.rowcount
            
            # 获取清理统计信息
            stats_sql = f"""
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN html_reports IS NOT NULL THEN 1 END) as records_with_html,
                EXTRACT(EPOCH FROM (MAX(completed_at) - MIN(completed_at)))/86400 as date_range_days
            FROM {schema_name}.config_compare_history
            """
            
            cursor.execute(stats_sql)
            stats = cursor.fetchone()
            
            # 计算数据库空间使用情况
            size_sql = f"""
            SELECT 
                pg_size_pretty(pg_total_relation_size('{schema_name}.config_compare_history')) as table_size,
                pg_size_pretty(pg_column_size(html_reports)) as avg_html_size
            FROM {schema_name}.config_compare_history 
            WHERE html_reports IS NOT NULL
            LIMIT 1
            """
            
            cursor.execute(size_sql)
            size_info = cursor.fetchone()
            
            conn.commit()
            cursor.close()
            conn.close()
            
            cleanup_result = {
                'status': 'completed',
                'html_reports_cleared': html_cleaned_count,
                'records_deleted': records_deleted_count,
                'remaining_records': stats['total_records'] if stats else 0,
                'records_with_html': stats['records_with_html'] if stats else 0,
                'date_range_days': float(stats['date_range_days']) if stats and stats['date_range_days'] else 0,
                'table_size': size_info['table_size'] if size_info else 'Unknown',
                'retention_config': retention_config,
                'project': project_name,
                'cleaned_at': datetime.now().isoformat()
            }
            
            print(f"数据库清理完成 - 项目: {project_name}")
            print(f"  HTML报告清理: {html_cleaned_count} 条记录")
            print(f"  完整记录删除: {records_deleted_count} 条记录")
            print(f"  剩余记录: {stats['total_records'] if stats else 0} 条")
            print(f"  当前表大小: {size_info['table_size'] if size_info else 'Unknown'}")
            
            return cleanup_result
            
        except Exception as e:
            print(f"数据库清理失败: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'project': project_name
            }

    @staticmethod
    def get_database_usage_stats(project_name: str) -> Dict:
        """获取数据库使用统计信息"""
        try:
            base_dir = Path(__file__).parent.parent
            config_path = base_dir / "config" / "config.yaml"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            project_config = config.get('projects', {}).get(project_name, {})
            pgsql_config = project_config.get('pgsql', {})
            
            conn = psycopg2.connect(
                host=pgsql_config.get('url'),
                port=pgsql_config.get('port', 5432),
                database=pgsql_config.get('database'),
                user=pgsql_config.get('user'),
                password=pgsql_config.get('password')
            )
            
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            schema_name = project_name.lower()
            
            # 基本统计信息
            basic_stats_sql = f"""
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN html_reports IS NOT NULL THEN 1 END) as records_with_html,
                COUNT(CASE WHEN completed_at > NOW() - INTERVAL '7 days' THEN 1 END) as records_last_7_days,
                COUNT(CASE WHEN completed_at > NOW() - INTERVAL '30 days' THEN 1 END) as records_last_30_days,
                MIN(completed_at) as oldest_record,
                MAX(completed_at) as newest_record
            FROM {schema_name}.config_compare_history
            """
            
            cursor.execute(basic_stats_sql)
            basic_stats = cursor.fetchone()
            
            # 空间使用统计
            space_stats_sql = f"""
            SELECT 
                pg_size_pretty(pg_total_relation_size('{schema_name}.config_compare_history')) as total_table_size,
                pg_total_relation_size('{schema_name}.config_compare_history') as total_table_size_bytes,
                pg_size_pretty(pg_column_size(result_data)) as avg_metadata_size,
                pg_size_pretty(pg_column_size(html_reports)) as avg_html_size
            FROM {schema_name}.config_compare_history 
            WHERE html_reports IS NOT NULL
            LIMIT 1
            """
            
            cursor.execute(space_stats_sql)
            space_stats = cursor.fetchone()
            
            # 按月份统计
            monthly_stats_sql = f"""
            SELECT 
                DATE_TRUNC('month', completed_at) as month,
                COUNT(*) as record_count,
                COUNT(CASE WHEN html_reports IS NOT NULL THEN 1 END) as records_with_html
            FROM {schema_name}.config_compare_history
            WHERE completed_at > NOW() - INTERVAL '12 months'
            GROUP BY DATE_TRUNC('month', completed_at)
            ORDER BY month DESC
            """
            
            cursor.execute(monthly_stats_sql)
            monthly_stats = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            # 计算预估的清理效果
            total_size_bytes = space_stats['total_table_size_bytes'] if space_stats else 0
            total_records = basic_stats['total_records'] if basic_stats else 0
            records_with_html = basic_stats['records_with_html'] if basic_stats else 0
            
            # 预估清理30天HTML后的空间节省
            estimated_html_cleanup_savings = 0
            if total_records > 0 and records_with_html > 0:
                # 假设HTML占用70%的空间
                html_space_ratio = 0.7
                estimated_html_cleanup_savings = total_size_bytes * html_space_ratio * 0.8  # 80%的HTML会被清理
            
            return {
                'project': project_name,
                'basic_stats': dict(basic_stats) if basic_stats else {},
                'space_stats': dict(space_stats) if space_stats else {},
                'monthly_breakdown': [dict(row) for row in monthly_stats],
                'cleanup_recommendations': {
                    'should_cleanup_html': records_with_html > 100,  # 超过100条带HTML的记录建议清理
                    'should_cleanup_records': total_records > 1000,  # 超过1000条记录建议清理旧记录
                    'estimated_space_savings_mb': round(estimated_html_cleanup_savings / 1024 / 1024, 2),
                    'recommended_html_retention_days': 30,
                    'recommended_metadata_retention_days': 90
                },
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"获取数据库统计失败: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'project': project_name
            }

    @staticmethod  
    def schedule_database_cleanup(project_name: str, retention_config: Dict = None):
        """定期数据库清理任务"""
        def cleanup_task():
            try:
                import time
                # 每周执行一次清理
                while True:
                    time.sleep(7 * 24 * 3600)  # 7天
                    
                    print(f"开始定期数据库清理 - 项目: {project_name}")
                    result = SimpleConfigCompareService.cleanup_old_database_records(
                        project_name, retention_config
                    )
                    
                    if result['status'] == 'completed':
                        print(f"定期清理完成: HTML清理 {result['html_reports_cleared']} 条, 删除记录 {result['records_deleted']} 条")
                    else:
                        print(f"定期清理失败: {result.get('error', 'Unknown error')}")
                        
            except Exception as e:
                print(f"定期清理任务失败: {e}")
        
        # 启动后台清理任务
        cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
        cleanup_thread.start()
        print(f"已启动定期数据库清理任务 - 项目: {project_name}")


# 全局任务管理
_tasks = {}
_tasks_lock = threading.Lock()


def start_simple_compare_task(project: str, branch: str, start_time: str, end_time: str) -> str:
    """启动简化的配置对比任务"""
    task_id = f"{project}_{branch}_{int(time.time())}_{uuid.uuid4().hex[:8]}"
    service = SimpleConfigCompareService(project, task_id)
    
    def run_task():
        try:
            result = service.start_compare(branch, start_time, end_time)
            with _tasks_lock:
                if task_id in _tasks:
                    _tasks[task_id]['result'] = result
                    _tasks[task_id]['status'] = 'completed'
                    
            # 保存历史记录（只有成功的任务才保存）
            if result:
                service.save_history_record(result)
                
        except Exception as e:
            with _tasks_lock:
                if task_id in _tasks:
                    _tasks[task_id]['result'] = None
                    _tasks[task_id]['status'] = 'failed'
                    _tasks[task_id]['error'] = str(e)
        finally:
            # 延迟清理
            def delayed_cleanup():
                time.sleep(300)  # 5分钟后清理
                service.cleanup()
            
            cleanup_thread = threading.Thread(target=delayed_cleanup, daemon=True)
            cleanup_thread.start()
    
    # 记录任务
    with _tasks_lock:
        _tasks[task_id] = {
            'service': service,
            'result': None,
            'status': 'running',
            'error': None,
            'created_at': datetime.now().isoformat()
        }
    
    # 启动后台任务
    thread = threading.Thread(target=run_task, daemon=True)
    thread.start()
    
    return task_id


def get_simple_task_progress(task_id: str) -> Dict:
    """获取任务进度"""
    with _tasks_lock:
        if task_id not in _tasks:
            return {'error': '任务不存在'}
        
        task_info = _tasks[task_id]
        service = task_info['service']
        
        progress_info = service.get_progress()
        
        return {
            'progress': progress_info.get('progress', 0),
            'message': progress_info.get('message', ''),
            'is_running': progress_info.get('is_running', False),
            'task_id': task_id,
            'status': task_info.get('status', 'unknown'),
            'result': task_info.get('result'),
            'error': task_info.get('error'),
            'created_at': task_info.get('created_at')
        }


def get_simple_task_result(task_id: str) -> Dict:
    """获取任务结果"""
    with _tasks_lock:
        if task_id not in _tasks:
            return {'error': '任务不存在'}
        
        task_info = _tasks[task_id]
        return {
            'status': task_info['status'],
            'result': task_info.get('result'),
            'error': task_info.get('error'),
            'created_at': task_info.get('created_at')
        }


if __name__ == "__main__":
    # 测试代码
    service = SimpleConfigCompareService("TM3D")
    try:
        result = service.start_compare(
            "develop",
            "2025-01-01T10:00:00Z",
            "2025-01-02T10:00:00Z"
        )
        print("对比结果:", result)
    except Exception as e:
        print("错误:", e) 