import time
from poco.drivers.unity3d import UnityPoco
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from poco.exceptions import PocoNoSuchNodeException, PocoTargetTimeout
from ..log_common import LogService
from poco.utils.simplerpc.utils import sync_wrapper


class PocoUnity:
    """Poco通用操作类 - Unity3D"""
    
    def __init__(self, device_id=None, timeout=10, screenshot_each_action=True, game_startup_wait=20, fallback_to_android=False, max_unity_retries=5, unity_retry_interval=5):
        """
        初始化Poco通用操作类
        :param device_id: 设备ID
        :param timeout: 操作超时时间
        :param screenshot_each_action: 每次操作后是否截图
        :param game_startup_wait: Unity游戏启动等待时间（秒），默认20秒适应Unity Poco服务启动
        :param fallback_to_android: 如果Unity Poco失败，是否回退到Android UI自动化，默认False不允许降级
        :param max_unity_retries: Unity Poco连接最大重试次数，默认5次
        :param unity_retry_interval: Unity Poco重试间隔时间（秒），默认5秒
        """
        self.device_id = device_id
        self.timeout = timeout
        self.screenshot_each_action = screenshot_each_action
        self.game_startup_wait = game_startup_wait
        self.fallback_to_android = fallback_to_android
        self.max_unity_retries = max_unity_retries
        self.unity_retry_interval = unity_retry_interval
        self.logger = LogService('poco_universal')
        self.poco = None
        self.poco_type = None  # 'unity' 或 'android'
        self._is_closed = False  # 添加关闭状态标志
        
        # 特殊元素处理配置
        self.special_elements = {
            # 格式: '元素路径': {'action': '处理方式', 'params': {}}
            'BtnClose_btn': {'action': 'click', 'params': {'wait_time': 2}},
            # 'HomePanel_com/BtnSet_btn': {'action': 'click', 'params': {'wait_time': 5}},
            # 'MainMenuPanel/BtnSet_btn': {'action': 'click', 'params': {'timeout': 10}},
            'text_continue': {'action': 'click', 'params': {'wait_time': 2}},
            'singleBtn_btn': {'action': 'click', 'params': {'wait_time': 2}},
            'UIMgr/green_btn': {'action': 'click', 'params': {'wait_time': 2}},
            'UIMgr/BtnSingleContinue_btn': {'action': 'click', 'params': {'wait_time': 2}},
            'commonBtnGreenBig': {'action': 'click', 'params': {'timeout': 2}},
            'Close_btn': {'action': 'click', 'params': {'timeout': 2}},
            'Btn_continue_an/Btn_continue_btn': {'action': 'click', 'params': {'timeout': 2}},
            'close_btn': {'action': 'click', 'params': {'timeout': 2}},
            'Btn_continue_btn': {'action': 'click', 'params': {'timeout': 2}}
        }
        
        # 等待游戏启动后再连接
        self._wait_for_game_startup()
        self._connect()
    
    def _wait_for_game_startup(self):
        """等待Unity游戏启动和服务初始化"""
        if self.game_startup_wait > 0:
            self.logger.info(f"等待Unity游戏启动和Poco服务初始化({self.game_startup_wait}秒)...")
            self.logger.info("Unity Poco服务通常需要15-20秒完全启动，请耐心等待...")
            time.sleep(self.game_startup_wait)
    
    def _connect(self):
        """连接到Unity3D应用，根据配置决定是否允许降级"""
        # 首先尝试Unity Poco
        if self._try_unity_poco():
            return
        
        # 如果Unity Poco失败且允许回退，尝试Android UI自动化
        if self.fallback_to_android:
            self.logger.warning("Unity Poco连接失败，尝试回退到Android UI自动化...")
            if self._try_android_poco():
                return
        
                # 如果都失败了或不允许降级
        if self.fallback_to_android:
            raise ConnectionError("无法连接到Unity Poco服务，Android UI自动化也失败")
        else:
            raise ConnectionError("无法连接到Unity Poco服务，且已禁用Android降级模式")
    
    def _try_unity_poco(self):
        """尝试连接Unity Poco，增强容错机制"""
        self.logger.info(f"开始连接Unity Poco服务 (最大重试{self.max_unity_retries}次，间隔{self.unity_retry_interval}秒)")
        
        for attempt in range(self.max_unity_retries):
            try:
                if attempt > 0:
                    wait_time = self.unity_retry_interval * attempt
                    self.logger.info(f"等待{wait_time}秒后重试Unity Poco连接...")
                    time.sleep(wait_time)
                
                self.logger.info(f"尝试连接Unity Poco (第{attempt + 1}/{self.max_unity_retries}次)")
                
                # 创建Unity Poco连接
                # Unity Poco通常不需要device_id参数，它通过ADB自动连接到当前活跃设备
                if self.device_id:
                    self.logger.info(f"目标设备: {self.device_id}")
                    # 首先确保ADB连接到指定设备
                    try:
                        from airtest.core.api import connect_device
                        device = connect_device(f"android:///{self.device_id}")
                        self.logger.info(f"ADB设备连接成功: {self.device_id}")
                    except Exception as adb_error:
                        self.logger.warning(f"ADB设备连接失败: {adb_error}")
                        # 继续尝试，可能Unity Poco可以直接连接
                
                # 创建Unity Poco实例，增加连接参数优化
                self.logger.info("创建Unity Poco实例...")
                # 设置Unity Poco的连接参数，增强稳定性
                unity_options = {
                    'timeout': 10,  # 操作超时时间
                    'reconnect_on_broken_pipe': True  # 在broken pipe时自动重连
                }
                
                try:
                    # 尝试使用优化参数创建Unity Poco
                    self.poco = UnityPoco(**unity_options)
                except TypeError:
                    # 如果参数不支持，使用默认参数
                    self.logger.debug("Unity Poco不支持优化参数，使用默认创建方式")
                    self.poco = UnityPoco()
                
                # 增强的连接测试
                self._test_unity_connection_enhanced()
                
                # 额外的连接稳定性初始化
                self._initialize_unity_connection_stability()
                
                self.logger.info(f"Unity Poco连接成功! 设备: {self.device_id or 'default'}")
                self.poco_type = 'unity'
                return True
                
            except Exception as e:
                error_msg = str(e)
                self.logger.warning(f"Unity Poco连接失败 (第{attempt + 1}次): {error_msg}")
                
                # 如果是端口或服务未启动的错误，给出具体建议
                if "refused" in error_msg.lower() or "timeout" in error_msg.lower():
                    self.logger.warning("可能原因: Unity Poco服务未完全启动，建议:")
                    self.logger.warning("1. 确保游戏已完全启动")
                    self.logger.warning("2. 检查Unity游戏是否集成了Poco SDK")
                    self.logger.warning("3. 确认游戏UI已完全加载")
                elif "no devices" in error_msg.lower():
                    self.logger.warning("设备连接问题，建议:")
                    self.logger.warning("1. 检查ADB设备连接: adb devices")
                    self.logger.warning("2. 确保设备已启用USB调试")
                    self.logger.warning("3. 尝试重新连接设备")
                    if self.device_id:
                        self.logger.warning(f"4. 确认设备ID正确: {self.device_id}")
                
                if attempt == self.max_unity_retries - 1:
                    self.logger.error(f"Unity Poco连接最终失败，已重试{self.max_unity_retries}次")
                    self.logger.error("请检查:")
                    self.logger.error("1. Unity游戏是否正确集成Poco SDK")
                    self.logger.error("2. 游戏是否完全启动（通常需要15-20秒）")
                    self.logger.error("3. 设备ADB连接是否正常")
                    self.logger.error("4. Unity Poco服务端口是否被占用")
        
        return False
    
    def _try_android_poco(self):
        """尝试连接Android UI自动化"""
        try:
            self.logger.info("尝试连接Android UI自动化...")
            
            if self.device_id:
                from airtest.core.api import connect_device
                device = connect_device(f"android:///{self.device_id}")
                self.poco = AndroidUiautomationPoco(
                    device=device,
                    use_airtest_input=True, 
                    screenshot_each_action=self.screenshot_each_action
                )
            else:
                self.poco = AndroidUiautomationPoco(
                    use_airtest_input=True, 
                    screenshot_each_action=self.screenshot_each_action
                )
            
            # 测试连接
            self._test_android_connection()
            
            self.logger.info(f"Android UI自动化连接成功: {self.device_id}")
            self.poco_type = 'android'
            return True
            
        except Exception as e:
            self.logger.error(f"Android UI自动化连接失败: {str(e)}")
            return False
    
    def _test_unity_connection(self):
        """测试Unity Poco连接是否正常"""
        try:
            if self.poco:
                # 简单的连接测试 - 尝试获取根元素
                root = self.poco.agent
                self.logger.debug("Unity Poco连接测试通过")
        except Exception as e:
            raise ConnectionError(f"Unity Poco连接测试失败: {str(e)}")
    
    def _test_unity_connection_enhanced(self):
        """增强的Unity Poco连接测试"""
        try:
            if not self.poco:
                raise ConnectionError("Unity Poco对象未创建")
            
            self.logger.debug("开始Unity Poco连接测试...")
            
            # 测试1: 检查agent连接
            try:
                agent = self.poco.agent
                self.logger.debug("✓ Unity Poco agent连接正常")
            except Exception as e:
                raise ConnectionError(f"Unity Poco agent连接失败: {str(e)}")
            
            # 测试2: 尝试获取屏幕尺寸
            try:
                screen_size = self.poco.get_screen_size()
                self.logger.debug(f"✓ 获取屏幕尺寸成功: {screen_size}")
            except Exception as e:
                self.logger.warning(f"获取屏幕尺寸失败: {str(e)}")
                # 这个不是致命错误，继续测试
            
            # 测试3: 尝试获取UI层次结构
            try:
                # 尝试获取根节点的层次结构
                hierarchy = self.poco.agent.hierarchy.dump()
                if hierarchy:
                    self.logger.debug("✓ Unity UI层次结构获取成功")
                else:
                    self.logger.warning("Unity UI层次结构为空，可能游戏UI未完全加载")
            except Exception as e:
                # 如果hierarchy获取失败，可能是Unity UI还没准备好
                self.logger.warning(f"Unity UI层次结构获取失败: {str(e)}")
                # 这可能不是致命错误，因为游戏可能还在加载UI
            
            # 测试4: 尝试简单的元素查找
            try:
                # 尝试查找任意元素，这会触发Unity Poco的完整通信
                test_element = self.poco("*")  # 查找任意元素
                self.logger.debug("✓ Unity Poco元素查找功能正常")
            except Exception as e:
                self.logger.warning(f"Unity Poco元素查找测试失败: {str(e)}")
                # 这也可能不是致命错误
            
            self.logger.info("Unity Poco连接测试完成，服务可用")
            
        except Exception as e:
            raise ConnectionError(f"Unity Poco增强连接测试失败: {str(e)}")
    
    def _initialize_unity_connection_stability(self):
        """初始化Unity连接稳定性设置"""
        try:
            if not self.poco or not hasattr(self.poco, 'agent'):
                return
                
            self.logger.debug("初始化Unity连接稳定性设置...")
            
            # 设置Unity Poco的连接参数以提升稳定性
            try:
                # 尝试设置更长的超时时间
                if hasattr(self.poco.agent, 'rpc'):
                    if hasattr(self.poco.agent.rpc, 'timeout'):
                        self.poco.agent.rpc.timeout = 20  # 增加RPC超时时间到20秒
                        self.logger.debug("设置Unity RPC超时时间为20秒")
                    
                    # 设置重连参数
                    if hasattr(self.poco.agent.rpc, 'max_retries'):
                        self.poco.agent.rpc.max_retries = 5  # 增加重试次数
                        self.logger.debug("设置Unity RPC最大重试次数为5次")
                        
                # 设置连接保活机制
                if hasattr(self.poco.agent, 'keep_alive'):
                    self.poco.agent.keep_alive = True
                    self.logger.debug("启用Unity连接保活机制")
                
                # 预热连接 - 执行更多轻量级操作来稳定连接
                self.logger.debug("执行扩展连接预热操作...")
                warmup_operations = [
                    lambda: self.poco.get_screen_size(),
                    lambda: self.poco.agent.hierarchy.dump(),
                    lambda: self.poco("*").exists() if hasattr(self.poco("*"), 'exists') else True
                ]
                
                for i, operation in enumerate(warmup_operations):
                    try:
                        result = operation()
                        self.logger.debug(f"预热操作{i+1}成功: {type(result).__name__}")
                        time.sleep(0.8)  # 增加间隔时间
                    except Exception as warmup_error:
                        self.logger.debug(f"预热操作{i+1}失败: {str(warmup_error)}")
                        if "Broken pipe" in str(warmup_error):
                            raise warmup_error  # 如果是连接问题，立即抛出
                
                self.logger.info("Unity连接稳定性初始化完成")
                
            except Exception as stability_error:
                self.logger.warning(f"连接稳定性初始化失败: {str(stability_error)}")
                # 这不是致命错误，继续执行
                
        except Exception as e:
            self.logger.warning(f"Unity连接稳定性初始化异常: {str(e)}")
    
    def _pre_operation_check(self):
        """操作前的连接预检查"""
        try:
            if not self.poco or self._is_closed:
                return False
            
            # 快速连接测试
            self.poco.get_screen_size()
            return True
            
        except Exception as e:
            error_msg = str(e)
            if "Broken pipe" in error_msg:
                self.logger.debug(f"预检查发现连接断开: {error_msg}")
                return False
            else:
                self.logger.debug(f"预检查遇到非致命错误: {error_msg}")
                return True  # 非连接问题，继续执行
    
    def _smart_reconnect(self, operation_type="unknown"):
        """智能重连，根据操作类型优化重连策略"""
        try:
            self.logger.info(f"开始智能重连流程 (操作类型: {operation_type})...")
            
            # 根据操作类型调整重连策略
            if operation_type == "click":
                # 点击操作需要更快的重连
                return self._fast_reconnect()
            elif operation_type == "wait":
                # 等待操作可以容忍更长的重连时间
                return self._stable_reconnect()
            else:
                # 默认使用平衡策略
                return self._balanced_reconnect()
                
        except Exception as e:
            self.logger.error(f"智能重连异常: {str(e)}")
            return False
    
    def _fast_reconnect(self):
        """快速重连策略，适用于点击等操作"""
        try:
            self.logger.debug("执行快速重连...")
            
            # 简单清理
            self.poco = None
            time.sleep(1)  # 短暂等待
            
            # 快速重连
            return self._try_unity_poco()
            
        except Exception as e:
            self.logger.debug(f"快速重连失败: {str(e)}")
            return False
    
    def _stable_reconnect(self):
        """稳定重连策略，适用于等待等操作"""
        try:
            self.logger.debug("执行稳定重连...")
            
            # 完整清理和重连
            return self._force_reconnect()
            
        except Exception as e:
            self.logger.debug(f"稳定重连失败: {str(e)}")
            return False
    
    def _balanced_reconnect(self):
        """平衡重连策略，通用场景"""
        try:
            self.logger.debug("执行平衡重连...")
            
            # 先尝试标准重连
            if self.reconnect_if_needed():
                return True
            
            # 如果失败，使用强制重连
            return self._force_reconnect()
            
        except Exception as e:
            self.logger.debug(f"平衡重连失败: {str(e)}")
            return False
    
    def _test_android_connection(self):
        """测试Android UI自动化连接是否正常"""
        try:
            if self.poco:
                # 简单的连接测试
                self.poco.get_screen_size()
                self.logger.debug("Android UI自动化连接测试通过")
        except Exception as e:
            raise ConnectionError(f"Android UI自动化连接测试失败: {str(e)}")
    
    def get_poco_type(self):
        """获取当前使用的poco类型"""
        return self.poco_type
    
    def reconnect_if_needed(self):
        """如果连接断开则重新连接"""
        try:
            if self.poco and not self._is_closed:
                # 根据poco类型测试连接
                if self.poco_type == 'unity':
                    self._test_unity_connection()
                elif self.poco_type == 'android':
                    self._test_android_connection()
                return True
        except Exception as e:
            error_msg = str(e)
            self.logger.warning(f"检测到连接断开: {error_msg}")
            
            # 检查是否是严重的连接错误
            if any(err in error_msg for err in ["Broken pipe", "Connection reset", "Connection refused", "Connection aborted"]):
                self.logger.warning("检测到严重连接错误，执行强制重连...")
                return self._force_reconnect()
            else:
                self.logger.warning("尝试普通重新连接...")
                try:
                    self._connect()
                    self.logger.info("普通重连成功")
                    return True
                except Exception as reconnect_error:
                    self.logger.error(f"普通重连失败: {str(reconnect_error)}")
                    # 普通重连失败，尝试强制重连
                    return self._force_reconnect()
        return False
    
    def _force_reconnect(self):
        """强制重新连接，用于处理严重的连接问题"""
        try:
            self.logger.info("开始强制重连流程...")
            original_poco_type = self.poco_type
            
            # 1. 强制清理所有连接资源
            self._cleanup_all_connections()
            
            # 2. 等待系统清理资源（增加等待时间）
            import time
            self.logger.info("等待系统资源清理...")
            time.sleep(8)  # 增加到8秒，确保资源完全释放
            
            # 3. 强制清理可能残留的端口连接
            try:
                import subprocess
                if self.device_id:
                    # 清理Unity Poco相关的端口转发
                    self.logger.info("清理端口转发...")
                    subprocess.run(f"adb -s {self.device_id} forward --remove-all", shell=True, capture_output=True, timeout=5)
                    time.sleep(2)
                    
                    # 额外的端口清理 - 确保Unity Poco端口完全释放
                    self.logger.info("清理Unity Poco特定端口...")
                    # Unity Poco通常使用5001端口，尝试清理
                    subprocess.run(f"adb -s {self.device_id} forward --remove tcp:5001", shell=True, capture_output=True, timeout=5)
                    time.sleep(1)
                    
                    # 尝试重新建立基础的ADB连接
                    self.logger.info("重新建立ADB连接...")
                    subprocess.run(f"adb -s {self.device_id} shell echo 'connection test'", shell=True, capture_output=True, timeout=5)
                    
            except Exception as port_cleanup_error:
                self.logger.debug(f"端口清理时发生异常: {str(port_cleanup_error)}")
            
            # 4. 重新等待游戏稳定（特别是第二次测试）
            self.logger.info("重新等待游戏稳定...")
            # 对于重连情况，增加额外等待时间
            extended_wait = self.game_startup_wait // 2 + 5  # 额外5秒
            self.logger.info(f"额外等待{extended_wait}秒确保游戏Unity服务完全重启...")
            time.sleep(extended_wait)  # 等待一半的游戏启动时间
            
            # 5. 如果原来是Unity Poco，先尝试重新连接Unity Poco
            if original_poco_type == 'unity':
                self.logger.info("尝试重新建立Unity Poco连接...")
                try:
                    # 直接尝试Unity Poco连接
                    if self._try_unity_poco():
                        self.logger.info("Unity Poco重连成功")
                        return True
                except Exception as unity_error:
                    self.logger.warning(f"Unity Poco重连失败: {str(unity_error)}")
                
                # Unity Poco重连失败，如果允许降级，切换到Android Poco
                if self.fallback_to_android:
                    self.logger.warning("Unity Poco重连失败，强制切换到Android Poco模式...")
                    try:
                        if self._try_android_poco():
                            self.logger.info("强制切换到Android Poco成功")
                            return True
                    except Exception as android_error:
                        self.logger.error(f"Android Poco连接也失败: {str(android_error)}")
                        return False
                else:
                    self.logger.error("Unity Poco重连失败且不允许降级到Android Poco")
                    return False
            
            # 6. 如果原来是Android Poco，直接重连Android Poco
            elif original_poco_type == 'android':
                self.logger.info("尝试重新建立Android Poco连接...")
                try:
                    if self._try_android_poco():
                        self.logger.info("Android Poco重连成功")
                        return True
                except Exception as android_error:
                    self.logger.error(f"Android Poco重连失败: {str(android_error)}")
                    return False
            
            # 7. 如果都失败了，使用通用连接方法
            else:
                self.logger.info("使用通用连接方法重连...")
                try:
                    self._connect()
                    self.logger.info("通用重连成功")
                    return True
                except Exception as general_error:
                    self.logger.error(f"通用重连失败: {str(general_error)}")
                    return False
            
        except Exception as e:
            self.logger.error(f"强制重连过程发生异常: {str(e)}")
            return False
    
    def _cleanup_all_connections(self):
        """清理所有连接资源"""
        try:
            self.logger.debug("开始清理所有连接资源...")
            
            # 清理当前poco引用（最重要的）
            if self.poco:
                try:
                    poco_type = getattr(self, 'poco_type', 'unknown')
                    self.logger.debug(f"清理{poco_type} Poco连接...")
                    
                    # 特殊处理Unity Poco
                    if poco_type == 'unity':
                        if hasattr(self.poco, 'agent') and self.poco.agent:
                            # 尝试关闭Unity Poco的网络连接
                            if hasattr(self.poco.agent, 'c') and self.poco.agent.c:
                                try:
                                    self.poco.agent.c.close()
                                    self.logger.debug("Unity Poco网络连接已关闭")
                                except:
                                    pass
                            # 尝试关闭agent
                            if hasattr(self.poco.agent, 'close'):
                                try:
                                    self.poco.agent.close()
                                    self.logger.debug("Unity Poco agent已关闭")
                                except:
                                    pass
                    
                    # 通用清理方法
                    if hasattr(self.poco, 'agent') and hasattr(self.poco.agent, 'close'):
                        try:
                            self.poco.agent.close()
                        except:
                            pass
                    
                    # 如果有close方法，调用它
                    if hasattr(self.poco, 'close'):
                        try:
                            self.poco.close()
                        except:
                            pass
                            
                except Exception as e:
                    self.logger.debug(f"清理当前poco时发生异常: {str(e)}")
                finally:
                    self.poco = None
            
            # 清理Unity Poco连接（如果有单独存储的话）
            if hasattr(self, 'unity_poco') and self.unity_poco:
                try:
                    if hasattr(self.unity_poco, 'agent') and self.unity_poco.agent:
                        if hasattr(self.unity_poco.agent, 'c') and self.unity_poco.agent.c:
                            self.unity_poco.agent.c.close()
                        if hasattr(self.unity_poco.agent, 'close'):
                            self.unity_poco.agent.close()
                except Exception as e:
                    self.logger.debug(f"清理Unity agent时发生异常: {str(e)}")
                finally:
                    self.unity_poco = None
            
            # 清理Android Poco连接（如果有单独存储的话）
            if hasattr(self, 'android_poco') and self.android_poco:
                try:
                    if hasattr(self.android_poco, 'agent') and self.android_poco.agent:
                        if hasattr(self.android_poco.agent, 'close'):
                            self.android_poco.agent.close()
                except Exception as e:
                    self.logger.debug(f"清理Android agent时发生异常: {str(e)}")
                finally:
                    self.android_poco = None
            
            # 重置连接状态
            self.poco_type = None
            
            self.logger.debug("连接资源清理完成")
            
        except Exception as e:
            self.logger.warning(f"清理连接资源时发生异常: {str(e)}")
    
    def is_connection_healthy(self):
        """检查连接是否健康"""
        try:
            if not self.poco or self._is_closed:
                return False
            
            # 简单的连接健康检查
            if self.poco_type == 'unity':
                self.poco.get_screen_size()
            elif self.poco_type == 'android':
                self.poco.get_screen_size()
            
            return True
        except:
            return False
    
    def get_element(self, *path):
        """
        获取元素（支持路径链式调用和带序号的同名元素）
        :param path: 元素路径，支持以下格式:
                    1. 普通路径: get_element("MainMenuPanel", "HomePanel_com", "lv_txtPro")
                    2. 带序号的同名元素: get_element("PendingArticleHangPoint", "propClock1_white1", 1)
                       等价于: poco("PendingArticleHangPoint").offspring("propClock1_white1")[1]
        :return: 元素对象
        
        使用示例:
        # 普通元素
        element = poco.get_element("MainMenuPanel", "HomePanel_com", "lv_txtPro")
        
        # 同名元素中的第2个（索引为1）
        element = poco.get_element("PendingArticleHangPoint", "propClock1_white1", 1)
        
        # 同名元素中的第1个（索引为0，可省略）
        element = poco.get_element("PendingArticleHangPoint", "propClock1_white1", 0)
        element = poco.get_element("PendingArticleHangPoint", "propClock1_white1")  # 等价
        """
        try:
            if not path:
                raise ValueError("元素路径不能为空")
            
            # 处理最后一个参数是否为数字索引
            index = None
            actual_path = list(path)
            
            # 检查最后一个参数是否为整数索引
            if len(path) >= 2 and isinstance(path[-1], int):
                index = path[-1]
                actual_path = list(path[:-1])  # 移除索引部分
            
            # 构建元素路径
            element = self.poco(actual_path[0])
            for node in actual_path[1:]:
                element = element.offspring(node)
            
            # 如果指定了索引，获取对应索引的元素
            if index is not None:
                if index < 0:
                    raise ValueError(f"元素索引不能为负数: {index}")
                
                # 获取所有同名元素
                elements = element
                if hasattr(elements, '__getitem__'):
                    try:
                        element = elements[index]
                        self.logger.debug(f"获取元素路径(带索引): {' -> '.join(map(str, actual_path))}[{index}]")
                    except IndexError:
                        raise IndexError(f"元素索引超出范围: {index}，路径: {' -> '.join(map(str, actual_path))}")
                else:
                    # 如果元素不支持索引，但用户指定了索引，则报错
                    raise ValueError(f"指定路径的元素不支持索引访问: {' -> '.join(map(str, actual_path))}")
            else:
                self.logger.debug(f"获取元素路径: {' -> '.join(map(str, actual_path))}")
            
            return element
            
        except Exception as e:
            path_str = ' -> '.join(map(str, path))
            self.logger.error(f"获取元素失败: {path_str} - {str(e)}")
            raise
    
    # ========== 核心封装操作方法 ==========
    
    def click(self, *path, **kwargs):
        """
        点击操作 - 增强版，具有更强的错误处理和智能重连
        支持两种调用方式:
        1. 传统方式: click("selector")
        2. 路径方式: click("MainMenuPanel", "HomePanel_com", "lv_txtPro")
        
        :param path: 元素路径
        :param kwargs: 操作参数 (wait_time, timeout)
        :return: 是否点击成功
        """
        max_retries = 2  # 最多重试2次
        for attempt in range(max_retries):
            try:
                element = self.get_element(*path)
                path_str = ' -> '.join(map(str, path))
                
                self.logger.info(f"执行点击操作: {path_str}")
                
                if element.exists():
                    # 增强的点击操作，增加连接检查
                    try:
                        element.click()
                        self.logger.info(f"点击成功: {path_str}")
                        
                        wait_time = kwargs.get('wait_time', 1)
                        if wait_time > 0:
                            time.sleep(wait_time)
                        
                        if self.screenshot_each_action:
                            self._take_screenshot(f"click_{len(path)}")
                        
                        return True
                        
                    except Exception as click_error:
                        click_error_msg = str(click_error)
                        if "Broken pipe" in click_error_msg:
                            self.logger.warning(f"点击时连接立即断开: {click_error_msg}")
                            # 将此异常向上抛出，让外层处理重连
                            raise click_error
                        else:
                            self.logger.error(f"点击执行失败: {click_error_msg}")
                            return False
                else:
                    self.logger.error(f"元素不存在: {path_str}")
                    return False
                    
            except Exception as e:
                error_msg = str(e)
                if any(err in error_msg for err in ["Broken pipe", "Connection reset", "Connection refused", "ConnectionError"]):
                    self.logger.warning(f"点击操作连接异常 (第{attempt + 1}次): {error_msg}")
                    if attempt < max_retries - 1:
                        self.logger.info(f"尝试重连并重试点击操作...")
                        # 使用强化重连，针对点击操作优化
                        time.sleep(2)  # 等待连接完全断开
                        
                        # 尝试多种重连策略
                        reconnect_success = False
                        
                        # 策略1: 标准重连
                        if self.reconnect_if_needed():
                            reconnect_success = True
                            self.logger.info("标准重连成功")
                        # 策略2: 强制重连（如果标准重连失败）
                        elif self._force_reconnect():
                            reconnect_success = True
                            self.logger.info("强制重连成功")
                        
                        if reconnect_success:
                            self.logger.info(f"重连成功，重试点击操作: {' -> '.join(map(str, path))}")
                            # 给连接一些时间稳定
                            time.sleep(1)
                            continue
                        else:
                            self.logger.error(f"重连失败，点击操作终止: {' -> '.join(map(str, path))}")
                            return False
                    else:
                        self.logger.error(f"点击操作最终失败: {' -> '.join(map(str, path))} - {error_msg}")
                        return False
                else:
                    self.logger.error(f"点击操作失败: {' -> '.join(map(str, path))} - {error_msg}")
                    return False
    
    def touch(self, *path, **kwargs):
        """触摸操作（与click相同）"""
        return self.click(*path, **kwargs)
    
    def double_click(self, *path, **kwargs):
        """
        双击操作
        :param path: 元素路径
        :param kwargs: 操作参数
        :return: 是否双击成功
        """
        try:
            element = self.get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            self.logger.info(f"执行双击操作: {path_str}")
            
            if element.exists():
                element.click()
                time.sleep(0.1)
                element.click()
                self.logger.info(f"双击成功: {path_str}")
                
                if self.screenshot_each_action:
                    self._take_screenshot(f"double_click_{len(path)}")
                
                return True
            else:
                self.logger.error(f"元素不存在: {path_str}")
                return False
                
        except Exception as e:
            self.logger.error(f"双击操作失败: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def long_click(self, *path, **kwargs):
        """
        长按操作
        :param path: 元素路径
        :param kwargs: 操作参数
        :return: 是否长按成功
        """
        try:
            element = self.get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            self.logger.info(f"执行长按操作: {path_str}")
            
            if element.exists():
                element.long_click()
                self.logger.info(f"长按成功: {path_str}")
                
                if self.screenshot_each_action:
                    self._take_screenshot(f"long_click_{len(path)}")
                
                return True
            else:
                self.logger.error(f"元素不存在: {path_str}")
                return False
                
        except Exception as e:
            self.logger.error(f"长按操作失败: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def text(self, *path, **kwargs):
        """
        文本操作（获取或设置）
        获取文本: text("path1", "path2")
        设置文本: text("path1", "path2", text="hello")
        
        :param path: 元素路径
        :param kwargs: 操作参数 (text, clear_first)
        :return: 文本内容或操作结果
        """
        try:
            element = self.get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            if 'text' in kwargs:
                # 设置文本
                text_value = kwargs.get('text', '')
                clear_first = kwargs.get('clear_first', True)
                
                self.logger.info(f"设置文本: {path_str} = '{text_value}'")
                
                if element.exists():
                    if clear_first:
                        element.set_text("")
                    element.set_text(text_value)
                    self.logger.info(f"文本设置成功: {path_str}")
                    
                    if self.screenshot_each_action:
                        self._take_screenshot(f"set_text_{len(path)}")
                    
                    return True
                else:
                    self.logger.error(f"元素不存在: {path_str}")
                    return False
            else:
                # 获取文本
                self.logger.info(f"获取文本: {path_str}")
                
                if element.exists():
                    text_value = element.get_text()
                    self.logger.info(f"获取文本成功: {path_str} = '{text_value}'")
                    return text_value
                else:
                    self.logger.error(f"元素不存在: {path_str}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"文本操作失败: {' -> '.join(map(str, path))} - {str(e)}")
            return None if 'text' not in kwargs else False
    
    def exists(self, *path, **kwargs):
        """
        检查元素是否存在
        :param path: 元素路径
        :param kwargs: 操作参数
        :return: 是否存在
        """
        max_retries = 2
        for attempt in range(max_retries):
            try:
                element = self.get_element(*path)
                path_str = ' -> '.join(map(str, path))
                
                exists = element.exists()
                self.logger.info(f"元素存在性: {path_str} = {exists}")
                return exists
                
            except Exception as e:
                error_msg = str(e)
                if any(err in error_msg for err in ["Broken pipe", "Connection reset", "Connection refused", "ConnectionError"]):
                    self.logger.warning(f"存在性检查连接异常 (第{attempt + 1}次): {error_msg}")
                    if attempt < max_retries - 1:
                        if self.reconnect_if_needed():
                            self.logger.debug(f"重连成功，重试存在性检查: {' -> '.join(map(str, path))}")
                            continue
                    self.logger.debug(f"元素存在性检查异常: {' -> '.join(map(str, path))} - {error_msg}")
                else:
                    self.logger.debug(f"元素存在性检查异常: {' -> '.join(map(str, path))} - {error_msg}")
                return False
            except Exception as e:
                self.logger.debug(f"元素存在性检查异常: {' -> '.join(map(str, path))} - {str(e)}")
                return False
    
    def wait(self, *path, **kwargs):
        """
        等待元素出现 - 最多等待3分钟
        :param path: 元素路径
        :param kwargs: 操作参数 
            - timeout: 超时时间（秒），默认180秒（3分钟）
            - check_interval: 检查间隔（秒），默认0.5秒
            - log_interval: 日志输出间隔（秒），默认30秒
        :return: 是否等待成功
        """
        max_retries = 2
        for attempt in range(max_retries):
            try:
                element = self.get_element(*path)
                path_str = ' -> '.join(map(str, path))
                timeout = kwargs.get('timeout', 180)  # 默认3分钟超时
                check_interval = kwargs.get('check_interval', 0.5)
                log_interval = kwargs.get('log_interval', 30)
                
                self.logger.info(f"等待元素出现: {path_str}, 超时: {timeout}秒")
                
                start_time = time.time()
                last_log_time = start_time
                
                while True:
                    # 检查元素是否存在
                    if element.exists():
                        elapsed_time = time.time() - start_time
                        self.logger.info(f"元素出现成功: {path_str}, 等待时间: {elapsed_time:.1f}秒")
                        return True
                    
                    current_time = time.time()
                    elapsed_time = current_time - start_time
                    
                    # 检查是否超时（现在总是有超时限制）
                    if elapsed_time >= timeout:
                        self.logger.warning(f"等待元素超时: {path_str}, 超时时间: {timeout}秒")
                        return False
                    
                    # 检查是否超过120秒，输出特殊日志
                    if elapsed_time > 120 and (current_time - last_log_time) >= log_interval:
                        self.logger.warning(f"等待元素已超过120秒: {path_str}, 当前等待时间: {elapsed_time:.1f}秒")
                        last_log_time = current_time
                    # 每隔log_interval秒输出一次等待日志
                    elif (current_time - last_log_time) >= log_interval:
                        self.logger.info(f"继续等待元素: {path_str}, 已等待: {elapsed_time:.1f}秒")
                        last_log_time = current_time
                    
                    # 等待一段时间后再次检查
                    time.sleep(check_interval)
                
            except Exception as e:
                error_msg = str(e)
                if any(err in error_msg for err in ["Broken pipe", "Connection reset", "Connection refused", "ConnectionError"]):
                    self.logger.warning(f"等待操作连接异常 (第{attempt + 1}次): {error_msg}")
                    if attempt < max_retries - 1:
                        if self.reconnect_if_needed():
                            self.logger.info(f"重连成功，重试等待操作: {' -> '.join(map(str, path))}")
                            continue
                    self.logger.error(f"等待元素异常: {' -> '.join(map(str, path))} - {error_msg}")
                else:
                    self.logger.error(f"等待元素异常: {' -> '.join(map(str, path))} - {error_msg}")
                return False
            except Exception as e:
                self.logger.error(f"等待元素异常: {' -> '.join(map(str, path))} - {str(e)}")
                return False
    
    def wait_and_click(self, *path, **kwargs):
        """
        等待元素出现后点击 - 组合操作，最多等待3分钟
        :param path: 元素路径
        :param kwargs: 操作参数 
            - timeout: 等待超时时间（秒），默认180秒（3分钟）
            - check_interval: 检查间隔（秒），默认0.5秒
            - log_interval: 日志输出间隔（秒），默认30秒
            - wait_time: 点击后等待时间（秒），默认1秒
        :return: 是否操作成功
        """
        max_retries = 2
        for attempt in range(max_retries):
            try:
                path_str = ' -> '.join(map(str, path))
                timeout = kwargs.get('timeout', 180)  # 默认3分钟超时
                check_interval = kwargs.get('check_interval', 0.5)
                log_interval = kwargs.get('log_interval', 30)
                wait_time = kwargs.get('wait_time', 1)
                
                self.logger.info(f"等待并点击元素: {path_str}, 超时: {timeout}秒")
                
                element = self.get_element(*path)
                start_time = time.time()
                last_log_time = start_time
                
                # 等待元素出现
                while True:
                    # 检查元素是否存在
                    if element.exists():
                        elapsed_time = time.time() - start_time
                        self.logger.info(f"元素出现成功: {path_str}, 等待时间: {elapsed_time:.1f}秒")
                        break
                    
                    current_time = time.time()
                    elapsed_time = current_time - start_time
                    
                    # 检查是否超时（现在总是有超时限制）
                    if elapsed_time >= timeout:
                        self.logger.warning(f"等待元素超时: {path_str}, 超时时间: {timeout}秒")
                        return False
                    
                    # 检查是否超过120秒，输出特殊日志
                    if elapsed_time > 120 and (current_time - last_log_time) >= log_interval:
                        self.logger.warning(f"等待元素已超过120秒: {path_str}, 当前等待时间: {elapsed_time:.1f}秒")
                        last_log_time = current_time
                    # 每隔log_interval秒输出一次等待日志
                    elif (current_time - last_log_time) >= log_interval:
                        self.logger.info(f"继续等待元素: {path_str}, 已等待: {elapsed_time:.1f}秒")
                        last_log_time = current_time
                    
                    # 等待一段时间后再次检查
                    time.sleep(check_interval)
                
                # 执行点击操作
                self.logger.info(f"开始点击元素: {path_str}")
                element.click()
                self.logger.info(f"点击成功: {path_str}")
                
                # 点击后等待
                if wait_time > 0:
                    time.sleep(wait_time)
                
                # 截图
                if self.screenshot_each_action:
                    self._take_screenshot(f"wait_and_click_{len(path)}")
                
                return True
                
            except Exception as e:
                error_msg = str(e)
                if any(err in error_msg for err in ["Broken pipe", "Connection reset", "Connection refused", "ConnectionError"]):
                    self.logger.warning(f"等待并点击操作连接异常 (第{attempt + 1}次): {error_msg}")
                    if attempt < max_retries - 1:
                        if self.reconnect_if_needed():
                            self.logger.info(f"重连成功，重试等待并点击操作: {' -> '.join(map(str, path))}")
                            continue
                    self.logger.error(f"等待并点击元素异常: {' -> '.join(map(str, path))} - {error_msg}")
                else:
                    self.logger.error(f"等待并点击元素异常: {' -> '.join(map(str, path))} - {error_msg}")
                return False
            except Exception as e:
                self.logger.error(f"等待并点击元素异常: {' -> '.join(map(str, path))} - {str(e)}")
                return False
    
    def wait_disappear(self, *path, **kwargs):
        """
        等待元素消失
        :param path: 元素路径
        :param kwargs: 操作参数 (timeout)
        :return: 是否消失成功
        """
        try:
            element = self.get_element(*path)
            path_str = ' -> '.join(map(str, path))
            timeout = kwargs.get('timeout', self.timeout)
            
            self.logger.info(f"等待元素消失: {path_str}")
            
            element.wait_for_disappearance(timeout=timeout)
            self.logger.info(f"元素消失成功: {path_str}")
            return True
            
        except PocoTargetTimeout:
            self.logger.warning(f"等待元素消失超时: {' -> '.join(map(str, path))}")
            return False
        except Exception as e:
            self.logger.error(f"等待元素消失异常: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def drag_to(self, *path, **kwargs):
        """
        拖拽操作
        :param path: 源元素路径
        :param kwargs: 操作参数 (target)
        :return: 是否拖拽成功
        """
        try:
            element = self.get_element(*path)
            path_str = ' -> '.join(map(str, path))
            target = kwargs.get('target')
            
            if not target:
                raise ValueError("drag_to操作需要target参数")
            
            self.logger.info(f"执行拖拽操作: {path_str} -> {target}")
            
            if element.exists():
                if isinstance(target, (list, tuple)) and len(target) > 0:
                    # target是路径
                    target_element = self.get_element(*target)
                    element.drag_to(target_element)
                else:
                    # target是坐标或其他
                    element.drag_to(target)
                
                self.logger.info(f"拖拽成功: {path_str} -> {target}")
                
                if self.screenshot_each_action:
                    self._take_screenshot(f"drag_to_{len(path)}")
                
                return True
            else:
                self.logger.error(f"元素不存在: {path_str}")
                return False
                
        except Exception as e:
            self.logger.error(f"拖拽操作失败: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def swipe(self, *path, **kwargs):
        """
        滑动操作
        :param path: 元素路径
        :param kwargs: 操作参数 (direction)
        :return: 是否滑动成功
        """
        try:
            element = self.get_element(*path)
            path_str = ' -> '.join(map(str, path))
            direction = kwargs.get('direction', 'up')
            
            self.logger.info(f"执行滑动操作: {path_str} direction={direction}")
            
            if element.exists():
                element.swipe(direction)
                self.logger.info(f"滑动成功: {path_str}")
                
                if self.screenshot_each_action:
                    self._take_screenshot(f"swipe_{len(path)}")
                
                return True
            else:
                self.logger.error(f"元素不存在: {path_str}")
                return False
                
        except Exception as e:
            self.logger.error(f"滑动操作失败: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def scroll_to(self, *path, **kwargs):
        """
        滚动到指定元素
        :param path: 目标元素路径
        :param kwargs: 操作参数 (container, max_attempts)
        :return: 是否找到元素
        """
        try:
            path_str = ' -> '.join(map(str, path))
            container_path = kwargs.get('container', [])
            max_attempts = kwargs.get('max_attempts', 10)
            
            self.logger.info(f"滚动查找元素: {path_str}")
            
            # 首先检查元素是否已经可见
            if self.exists(*path):
                self.logger.info(f"元素已可见: {path_str}")
                return True
            
            # 获取滚动容器
            if container_path:
                container = self.get_element(*container_path)
            else:
                container = self.poco  # 使用根容器
            
            # 尝试滚动查找
            for attempt in range(max_attempts):
                self.logger.debug(f"滚动查找尝试 {attempt + 1}/{max_attempts}")
                
                # 向下滚动
                container.swipe("up")
                time.sleep(0.5)
                
                if self.exists(*path):
                    self.logger.info(f"滚动找到元素: {path_str}")
                    return True
            
            self.logger.warning(f"滚动未找到元素: {path_str}")
            return False
            
        except Exception as e:
            self.logger.error(f"滚动查找异常: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def get_attribute(self, *path, **kwargs):
        """
        获取元素属性
        :param path: 元素路径
        :param kwargs: 操作参数 (attribute)
        :return: 属性值或None
        """
        try:
            element = self.get_element(*path)
            path_str = ' -> '.join(map(str, path))
            attribute = kwargs.get('attribute')
            
            if not attribute:
                raise ValueError("get_attribute操作需要attribute参数")
            
            self.logger.info(f"获取属性: {path_str}.{attribute}")
            
            if element.exists():
                value = element.attr(attribute)
                self.logger.info(f"获取属性成功: {path_str}.{attribute} = {value}")
                return value
            else:
                self.logger.error(f"元素不存在: {path_str}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取属性失败: {' -> '.join(map(str, path))} - {str(e)}")
            return None
    
    # ========== 辅助方法 ==========
    
    def _take_screenshot(self, tag=""):
        """
        截图操作
        :param tag: 截图标签
        """
        try:
            if self.poco:
                timestamp = int(time.time())
                filename = f"screenshot_{tag}_{timestamp}.png"
                # 这里可以添加具体的截图保存逻辑
                self.logger.debug(f"截图: {filename}")
        except Exception as e:
            self.logger.warning(f"截图失败: {str(e)}")
    
    def close(self):
        """关闭连接"""
        try:
            if self._is_closed:
                self.logger.debug("Poco连接已经关闭，跳过重复关闭")
                return
            
            self._is_closed = True
            if self.poco:
                # 对Unity Poco进行清理操作
                try:
                    # 尝试优雅关闭连接
                    if hasattr(self.poco, 'agent') and self.poco.agent:
                        # 关闭Unity Poco的agent连接
                        if hasattr(self.poco.agent, 'close'):
                            self.poco.agent.close()
                        elif hasattr(self.poco.agent, 'stop'):
                            self.poco.agent.stop()
                except Exception as agent_error:
                    self.logger.warning(f"关闭Unity Poco agent时发生错误: {str(agent_error)}")
                
                self.poco = None
                self.poco_type = None
                self.logger.info("Unity Poco连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭连接失败: {str(e)}")
    
    def is_closed(self):
        """检查连接是否已关闭"""
        return self._is_closed
    
    def is_connected(self):
        """检查连接是否有效"""
        if self._is_closed or not self.poco:
            return False

        try:
            # 快速连接测试
            if hasattr(self.poco, 'agent'):
                return self.poco.agent is not None
            return True
        except Exception:
            return False

    def get_native_poco(self):
        """
        获取原生的Poco对象（UnityPoco或AndroidUiautomationPoco）

        :return: 原生poco对象，可以直接使用原生poco的所有方法

        使用示例:
        unity_poco = PocoUnity(device_id="your_device")
        native_poco = unity_poco.get_native_poco()

        # Unity原生poco用法
        native_poco("SR_Canvas").offspring("SR_Content").child("Category(Clone)")[1].child("NumberOption(Clone)")[0].child("SR_Title").click()
        native_poco(text="卡包Id").click()

        # Android原生poco用法（如果降级到Android模式）
        native_poco("android.widget.Button").offspring("android.widget.TextView")[0].click()
        """
        if self._is_closed or not self.poco:
            self.logger.error("Poco连接已关闭或不存在，无法获取原生poco对象")
            raise RuntimeError("Poco连接已关闭或不存在")

        self.logger.info(f"返回原生{self.poco_type}Poco对象")
        return self.poco
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
    
    def click_all_children_by_name(self, *path, **kwargs):
        """
        根据输入的path，查找当前输入path的所有子节点，按名称分组并依次点击
        - 相同名称的子节点会全部点击
        - 完成一组同名节点后，再处理下一组不同名称的节点
        
        :param path: 父元素路径
        :param kwargs: 操作参数 (wait_time, timeout, click_interval)
        :return: 点击结果统计
        """
        max_retries = 2
        for attempt in range(max_retries):
            try:
                parent_element = self.get_element(*path)
                path_str = ' -> '.join(map(str, path))
                
                wait_time = kwargs.get('wait_time', 0.5)
                click_interval = kwargs.get('click_interval', 0.3)  # 同名元素之间的点击间隔
                
                self.logger.info(f"开始查找子节点并分组点击: {path_str}")
                
                if not parent_element.exists():
                    self.logger.error(f"父元素不存在: {path_str}")
                    return {
                        'success': False,
                        'message': '父元素不存在',
                        'groups_clicked': 0,
                        'total_clicked': 0
                    }
                
                # 获取所有子节点
                try:
                    children = parent_element.children()
                    
                    # 将迭代器转换为列表以便检查长度
                    children_list = list(children)
                    
                    self.logger.info(f"children()方法返回类型: {type(children)}")
                    self.logger.info(f"转换为列表后的长度: {len(children_list)}")
                    
                    if not children_list:
                        # 尝试其他方法获取子节点
                        self.logger.warning(f"children()方法未找到子节点，尝试其他方法: {path_str}")
                        
                        # 方法1: 尝试使用offspring()获取所有后代
                        try:
                            offspring = parent_element.offspring()
                            offspring_list = list(offspring)
                            self.logger.info(f"offspring()方法找到 {len(offspring_list)} 个后代节点")
                            
                            if offspring_list:
                                # 过滤出直接子节点（深度为1的节点）
                                direct_children = []
                                for child in offspring_list:
                                    try:
                                        # 检查是否为直接子节点
                                        child_parent = child.parent()
                                        if child_parent and child_parent == parent_element:
                                            direct_children.append(child)
                                    except:
                                        # 如果无法确定父子关系，也加入列表
                                        direct_children.append(child)
                                
                                if direct_children:
                                    children_list = direct_children
                                    self.logger.info(f"通过offspring()过滤得到 {len(children_list)} 个直接子节点")
                                else:
                                    children_list = offspring_list[:10]  # 限制数量避免过多
                                    self.logger.info(f"使用offspring()前10个节点作为子节点")
                        except Exception as offspring_e:
                            self.logger.warning(f"offspring()方法也失败: {str(offspring_e)}")
                        
                        # 方法2: 如果还是没有，尝试通过选择器查找
                        if not children_list:
                            try:
                                # 尝试查找所有子元素（使用通配符）
                                all_children = parent_element.child("*")
                                if all_children.exists():
                                    children_list = [all_children]
                                    self.logger.info(f"通过child('*')找到子节点")
                            except Exception as child_e:
                                self.logger.warning(f"child('*')方法失败: {str(child_e)}")
                    
                    if not children_list:
                        self.logger.warning(f"所有方法都未找到子节点: {path_str}")
                        
                        # 输出父元素的详细信息用于调试
                        try:
                            parent_attrs = parent_element.attr()
                            self.logger.info(f"父元素属性: {parent_attrs}")
                        except:
                            pass
                        
                        return {
                            'success': True,
                            'message': '未找到子节点',
                            'groups_clicked': 0,
                            'total_clicked': 0
                        }
                    
                    self.logger.info(f"最终找到 {len(children_list)} 个子节点")
                    children = children_list
                    
                except Exception as e:
                    self.logger.error(f"获取子节点失败: {path_str} - {str(e)}")
                    return {
                        'success': False,
                        'message': f'获取子节点失败: {str(e)}',
                        'groups_clicked': 0,
                        'total_clicked': 0
                    }
                
                # 按名称分组子节点
                name_groups = {}
                for child in children:
                    try:
                        # 尝试获取节点名称，可能是name、text或者类名
                        child_name = None
                        
                        # 优先尝试获取name属性
                        try:
                            child_name = child.attr('name')
                        except:
                            pass
                        
                        # 如果没有name，尝试获取text
                        if not child_name:
                            try:
                                child_name = child.get_text()
                            except:
                                pass
                        
                        # 如果都没有，使用节点类型
                        if not child_name:
                            try:
                                child_name = child.attr('type') or 'unknown'
                            except:
                                child_name = 'unknown'
                        
                        # 清理名称（去除空白字符）
                        child_name = str(child_name).strip()
                        if not child_name:
                            child_name = 'empty'
                        
                        # 分组
                        if child_name not in name_groups:
                            name_groups[child_name] = []
                        name_groups[child_name].append(child)
                        
                    except Exception as e:
                        self.logger.warning(f"处理子节点时出错: {str(e)}")
                        continue
                
                if not name_groups:
                    self.logger.warning(f"没有可处理的子节点: {path_str}")
                    return {
                        'success': True,
                        'message': '没有可处理的子节点',
                        'groups_clicked': 0,
                        'total_clicked': 0
                    }
                
                # 按组依次点击
                groups_clicked = 0
                total_clicked = 0
                click_results = {}
                
                for group_name, group_children in name_groups.items():
                    self.logger.info(f"开始点击同名组 '{group_name}' ({len(group_children)} 个元素)")
                    
                    group_success_count = 0
                    
                    # 点击该组的所有同名元素
                    for i, child in enumerate(group_children):
                        try:
                            if child.exists():
                                child.click()
                                group_success_count += 1
                                total_clicked += 1
                                self.logger.info(f"点击成功: {group_name}[{i+1}/{len(group_children)}]")
                                
                                # 同名元素之间的间隔
                                if i < len(group_children) - 1:  # 不是最后一个
                                    time.sleep(click_interval)
                            else:
                                self.logger.warning(f"元素不存在，跳过: {group_name}[{i+1}]")
                                
                        except Exception as e:
                            self.logger.error(f"点击失败: {group_name}[{i+1}] - {str(e)}")
                    
                    click_results[group_name] = {
                        'total': len(group_children),
                        'success': group_success_count,
                        'failed': len(group_children) - group_success_count
                    }
                    
                    if group_success_count > 0:
                        groups_clicked += 1
                    
                    # 组之间的等待时间
                    if wait_time > 0:
                        time.sleep(wait_time)
                
                self.logger.info(f"分组点击完成: 处理了 {groups_clicked} 个组，总共点击 {total_clicked} 个元素")
                
                # 截图
                if self.screenshot_each_action:
                    self._take_screenshot(f"click_all_children_{len(path)}")
                
                return {
                    'success': True,
                    'message': '分组点击完成',
                    'groups_clicked': groups_clicked,
                    'total_clicked': total_clicked,
                    'groups_detail': click_results
                }
                
            except ConnectionError as e:
                self.logger.warning(f"分组点击连接异常 (第{attempt + 1}次): {str(e)}")
                if attempt < max_retries - 1:
                    if self.reconnect_if_needed():
                        continue
                self.logger.error(f"分组点击操作失败: {' -> '.join(map(str, path))} - {str(e)}")
                return {
                    'success': False,
                    'message': f'连接异常: {str(e)}',
                    'groups_clicked': 0,
                    'total_clicked': 0
                }
            except Exception as e:
                self.logger.error(f"分组点击操作失败: {' -> '.join(map(str, path))} - {str(e)}")
                return {
                    'success': False,
                    'message': f'操作失败: {str(e)}',
                    'groups_clicked': 0,
                    'total_clicked': 0
                }
    
    def debug_element_structure(self, *path, **kwargs):
        """
        调试元素结构，输出详细的元素信息和子节点信息
        
        :param path: 元素路径
        :param kwargs: 调试参数 (max_depth, show_attrs)
        :return: 元素结构信息
        """
        try:
            element = self.get_element(*path)
            path_str = ' -> '.join(map(str, path))
            max_depth = kwargs.get('max_depth', 2)
            show_attrs = kwargs.get('show_attrs', True)
            
            self.logger.info(f"=== 开始调试元素结构: {path_str} ===")
            
            if not element.exists():
                self.logger.error(f"元素不存在: {path_str}")
                return {
                    'success': False,
                    'message': '元素不存在'
                }
            
            # 获取元素基本信息
            element_info = {
                'path': path_str,
                'exists': True,
                'children_methods': {}
            }
            
            # 尝试获取元素属性
            if show_attrs:
                try:
                    attrs = element.attr()
                    element_info['attributes'] = attrs
                    self.logger.info(f"元素属性: {attrs}")
                except Exception as e:
                    self.logger.warning(f"获取元素属性失败: {str(e)}")
                    element_info['attributes'] = f"获取失败: {str(e)}"
            
            # 方法1: 测试children()
            try:
                children = element.children()
                children_list = list(children)
                element_info['children_methods']['children()'] = {
                    'count': len(children_list),
                    'type': str(type(children)),
                    'success': True
                }
                self.logger.info(f"children()方法: 找到 {len(children_list)} 个子节点")
                
                # 显示前几个子节点的信息
                for i, child in enumerate(children_list[:3]):
                    try:
                        child_attrs = child.attr() if show_attrs else "未获取"
                        self.logger.info(f"  子节点[{i}]: {child_attrs}")
                    except Exception as e:
                        self.logger.warning(f"  子节点[{i}]属性获取失败: {str(e)}")
                        
            except Exception as e:
                element_info['children_methods']['children()'] = {
                    'success': False,
                    'error': str(e)
                }
                self.logger.warning(f"children()方法失败: {str(e)}")
            
            # 方法2: 测试offspring()
            try:
                offspring = element.offspring()
                offspring_list = list(offspring)
                element_info['children_methods']['offspring()'] = {
                    'count': len(offspring_list),
                    'type': str(type(offspring)),
                    'success': True
                }
                self.logger.info(f"offspring()方法: 找到 {len(offspring_list)} 个后代节点")
                
                # 显示前几个后代节点的信息
                for i, child in enumerate(offspring_list[:5]):
                    try:
                        child_attrs = child.attr() if show_attrs else "未获取"
                        self.logger.info(f"  后代节点[{i}]: {child_attrs}")
                    except Exception as e:
                        self.logger.warning(f"  后代节点[{i}]属性获取失败: {str(e)}")
                        
            except Exception as e:
                element_info['children_methods']['offspring()'] = {
                    'success': False,
                    'error': str(e)
                }
                self.logger.warning(f"offspring()方法失败: {str(e)}")
            
            # 方法3: 测试child()通配符
            try:
                child_wildcard = element.child("*")
                if child_wildcard.exists():
                    element_info['children_methods']['child("*")'] = {
                        'exists': True,
                        'success': True
                    }
                    self.logger.info(f"child('*')方法: 找到通配符子节点")
                    
                    if show_attrs:
                        try:
                            wildcard_attrs = child_wildcard.attr()
                            self.logger.info(f"  通配符子节点属性: {wildcard_attrs}")
                        except Exception as e:
                            self.logger.warning(f"  通配符子节点属性获取失败: {str(e)}")
                else:
                    element_info['children_methods']['child("*")'] = {
                        'exists': False,
                        'success': True
                    }
                    self.logger.info(f"child('*')方法: 未找到通配符子节点")
                    
            except Exception as e:
                element_info['children_methods']['child("*")'] = {
                    'success': False,
                    'error': str(e)
                }
                self.logger.warning(f"child('*')方法失败: {str(e)}")
            
            # 方法4: 尝试一些常见的子元素选择器
            common_selectors = ["Button", "Text", "Image", "Panel", "GameObject", "Transform"]
            element_info['common_children'] = {}
            
            for selector in common_selectors:
                try:
                    child_elem = element.child(selector)
                    if child_elem.exists():
                        element_info['common_children'][selector] = True
                        self.logger.info(f"找到 {selector} 类型的子元素")
                    else:
                        element_info['common_children'][selector] = False
                except Exception as e:
                    element_info['common_children'][selector] = f"错误: {str(e)}"
            
            self.logger.info(f"=== 元素结构调试完成: {path_str} ===")
            
            return {
                'success': True,
                'element_info': element_info
            }
            
        except Exception as e:
            self.logger.error(f"调试元素结构失败: {' -> '.join(map(str, path))} - {str(e)}")
            return {
                'success': False,
                'message': f'调试失败: {str(e)}'
            }

    def handle_special_elements(self, **kwargs):
        """
        处理特殊元素
        检查当前界面是否存在特殊元素列表中的元素，如果存在则按照配置的处理方式处理
        支持循环处理：当处理完一个特殊元素后，会重新检查是否有新的特殊元素出现，直到没有特殊元素为止
        
        :param kwargs: 可选参数
            - force_check: 是否强制检查所有特殊元素（每轮都检查完所有元素）
            - custom_elements: 自定义特殊元素配置，格式同self.special_elements
            - timeout: 检查超时时间
            - max_iterations: 最大循环次数，防止无限循环（默认10次）
            - iteration_delay: 每轮检查之间的延迟时间（默认0.5秒）
        :return: 处理结果
        {
            'success': bool,
            'message': str,
            'handled_elements': list,  # 已处理的元素列表
            'details': dict,  # 每个元素的处理详情
            'iterations': int,  # 实际循环次数
            'total_handled': int  # 总共处理的元素数量
        }
        """
        try:
            force_check = kwargs.get('force_check', False)
            custom_elements = kwargs.get('custom_elements', {})
            timeout = kwargs.get('timeout', self.timeout)
            max_iterations = kwargs.get('max_iterations', 10)
            iteration_delay = kwargs.get('iteration_delay', 0.5)
            
            # 合并自定义特殊元素配置
            elements_to_check = {**self.special_elements, **custom_elements}
            
            if not elements_to_check:
                return {
                    'success': True,
                    'message': '没有需要处理的特殊元素',
                    'handled_elements': [],
                    'details': {},
                    'iterations': 0,
                    'total_handled': 0
                }
            
            self.logger.info("开始循环检查特殊元素...")
            
            all_handled_elements = []
            all_details = {}
            iteration_count = 0
            
            # 循环处理特殊元素，直到没有特殊元素或达到最大循环次数
            while iteration_count < max_iterations:
                iteration_count += 1
                self.logger.info(f"第 {iteration_count} 轮特殊元素检查...")
                
                current_handled = []
                current_details = {}
                found_any_element = False
                
                for element_path, config in elements_to_check.items():
                    try:
                        # 将路径字符串转换为路径列表
                        path_parts = element_path.split('/')
                        time.sleep(1)
                        print(path_parts)
                        # 检查元素是否存在
                        if self.exists(*path_parts):
                            found_any_element = True
                            self.logger.info(f"发现特殊元素: {element_path}")
                            
                            # 获取处理方式和参数
                            action = config.get('action')
                            params = config.get('params', {})
                            
                            # 根据action执行相应的处理
                            result = None
                            if action == 'click':
                                # 使用正确的poco调用方式
                                result = self.click(*path_parts, **params)
                            elif action == 'wait_disappear':
                                result = self.wait_disappear(*path_parts, **params)
                            elif action == 'wait':
                                result = self.wait(*path_parts, **params)
                            elif action == 'text':
                                result = self.text(*path_parts, **params)
                            else:
                                self.logger.warning(f"未知的处理方式: {action}")
                                continue
                            
                            # 记录处理结果
                            current_handled.append(element_path)
                            current_details[element_path] = {
                                'action': action,
                                'result': result,
                                'params': params,
                                'path_parts': path_parts,
                                'iteration': iteration_count
                            }
                            
                            self.logger.info(f"处理特殊元素完成: {element_path}, 结果: {result}")
                            
                            # 如果不是强制检查，处理完一个元素后跳出当前轮次
                            if not force_check:
                                break
                                
                    except Exception as e:
                        self.logger.error(f"处理特殊元素失败: {element_path} - {str(e)}")
                        current_details[element_path] = {
                            'error': str(e),
                            'success': False,
                            'iteration': iteration_count
                        }
                
                # 将当前轮次的结果添加到总结果中
                all_handled_elements.extend(current_handled)
                all_details.update(current_details)
                
                # 如果本轮没有找到任何特殊元素，说明处理完成
                if not found_any_element:
                    self.logger.info(f"第 {iteration_count} 轮检查未发现特殊元素，处理完成")
                    break
                
                # 如果本轮处理了元素，等待一段时间再进行下一轮检查
                if current_handled and iteration_count < max_iterations:
                    self.logger.info(f"第 {iteration_count} 轮处理了 {len(current_handled)} 个特殊元素，等待 {iteration_delay} 秒后进行下一轮检查...")
                    time.sleep(iteration_delay)
            
            # 检查是否达到最大循环次数
            if iteration_count >= max_iterations:
                self.logger.warning(f"达到最大循环次数 {max_iterations}，停止特殊元素处理")
            
            total_handled = len(all_handled_elements)
            message = f'循环 {iteration_count} 轮，总共处理了 {total_handled} 个特殊元素'
            
            self.logger.info(f"特殊元素处理完成: {message}")
            
            return {
                'success': True,
                'message': message,
                'handled_elements': all_handled_elements,
                'details': all_details,
                'iterations': iteration_count,
                'total_handled': total_handled
            }
            
        except Exception as e:
            self.logger.error(f"处理特殊元素时发生错误: {str(e)}")
            return {
                'success': False,
                'message': f'处理特殊元素失败: {str(e)}',
                'handled_elements': [],
                'details': {},
                'iterations': 0,
                'total_handled': 0
            } 
        

#         # 基本使用 - 循环处理特殊元素直到没有为止
# result = poco.handle_special_elements()

# # 强制检查所有特殊元素（每轮都检查完所有元素）
# result = poco.handle_special_elements(force_check=True)

# # 自定义循环参数
# result = poco.handle_special_elements(
#     max_iterations=5,      # 最大循环5次
#     iteration_delay=1.0    # 每轮之间等待1秒
# )

# # 添加自定义特殊元素
# custom_elements = {
#     'CustomPanel/Button': {'action': 'click', 'params': {'wait_time': 1}}
# }
# result = poco.handle_special_elements(custom_elements=custom_elements)

# # 处理结果包含循环信息
# print(f"循环了 {result['iterations']} 轮")
# print(f"总共处理了 {result['total_handled']} 个特殊元素")
# print(f"处理的元素: {result['handled_elements']}")

    def click_by_index(self, *path, index=0, **kwargs):
        """
        根据索引点击同名元素中的指定元素
        :param path: 元素路径（不包含索引）
        :param index: 元素索引，默认为0（第一个）
        :param kwargs: 点击参数
        :return: 是否点击成功
        
        使用示例:
        # 点击第2个propClock1_white1元素（索引为1）
        poco.click_by_index("PendingArticleHangPoint", "propClock1_white1", index=1)
        
        # 等价于以下写法：
        poco.click("PendingArticleHangPoint", "propClock1_white1", 1)
        """
        return self.click(*path, index, **kwargs)
    
    def wait_and_click_by_index(self, *path, index=0, **kwargs):
        """
        等待并根据索引点击同名元素中的指定元素
        :param path: 元素路径（不包含索引）
        :param index: 元素索引，默认为0（第一个）
        :param kwargs: 操作参数
        :return: 是否操作成功
        
        使用示例:
        # 等待并点击第3个按钮元素（索引为2）
        poco.wait_and_click_by_index("ButtonPanel", "Button", index=2, timeout=10)
        """
        return self.wait_and_click(*path, index, **kwargs)
    
    def exists_by_index(self, *path, index=0, **kwargs):
        """
        检查指定索引的同名元素是否存在
        :param path: 元素路径（不包含索引）
        :param index: 元素索引，默认为0（第一个）
        :param kwargs: 检查参数
        :return: 是否存在
        
        使用示例:
        # 检查第2个元素是否存在
        if poco.exists_by_index("Panel", "Item", index=1):
            print("第2个Item元素存在")
        """
        return self.exists(*path, index, **kwargs)
    
    def text_by_index(self, *path, index=0, **kwargs):
        """
        获取或设置指定索引的同名元素的文本
        :param path: 元素路径（不包含索引）
        :param index: 元素索引，默认为0（第一个）
        :param kwargs: 文本操作参数
        :return: 文本内容或操作结果
        
        使用示例:
        # 获取第2个文本元素的内容
        text = poco.text_by_index("Panel", "TextLabel", index=1)
        
        # 设置第3个输入框的文本
        poco.text_by_index("Panel", "InputField", index=2, text="Hello World")
        """
        return self.text(*path, index, **kwargs)
    
    def get_elements_count(self, *path):
        """
        获取指定路径下同名元素的数量
        :param path: 元素路径
        :return: 元素数量
        
        使用示例:
        # 获取所有Button元素的数量
        count = poco.get_elements_count("ButtonPanel", "Button")
        print(f"共有{count}个按钮")
        """
        try:
            element = self.get_element(*path)
            if hasattr(element, '__len__'):
                count = len(element)
                self.logger.debug(f"元素数量: {' -> '.join(map(str, path))} = {count}")
                return count
            else:
                # 如果不是列表类型，说明只有一个元素或没有元素
                if element.exists():
                    return 1
                else:
                    return 0
        except Exception as e:
            path_str = ' -> '.join(map(str, path))
            self.logger.error(f"获取元素数量失败: {path_str} - {str(e)}")
            return 0
    
    def click_all_by_path(self, *path, **kwargs):
        """
        点击指定路径下的所有同名元素
        :param path: 元素路径
        :param kwargs: 点击参数 (click_interval, wait_time)
        :return: 点击结果统计
        
        使用示例:
        # 点击所有的收集按钮
        result = poco.click_all_by_path("Panel", "CollectButton", click_interval=0.5)
        print(f"成功点击了{result['success_count']}个按钮")
        """
        try:
            element = self.get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            click_interval = kwargs.get('click_interval', 0.3)
            wait_time = kwargs.get('wait_time', 0.5)
            
            success_count = 0
            total_count = 0
            
            if hasattr(element, '__len__'):
                # 多个同名元素
                total_count = len(element)
                self.logger.info(f"开始点击所有元素: {path_str} (共{total_count}个)")
                
                for i in range(total_count):
                    try:
                        if self.click(*path, i, wait_time=0):
                            success_count += 1
                            self.logger.info(f"成功点击第{i+1}个元素: {path_str}")
                        else:
                            self.logger.warning(f"点击第{i+1}个元素失败: {path_str}")
                        
                        if i < total_count - 1:  # 不是最后一个元素
                            time.sleep(click_interval)
                    except Exception as e:
                        self.logger.error(f"点击第{i+1}个元素异常: {path_str} - {str(e)}")
                        continue
            else:
                # 单个元素
                if element.exists():
                    total_count = 1
                    if self.click(*path, wait_time=0):
                        success_count = 1
                        self.logger.info(f"成功点击元素: {path_str}")
                    else:
                        self.logger.warning(f"点击元素失败: {path_str}")
                else:
                    self.logger.warning(f"元素不存在: {path_str}")
            
            # 最后等待
            if wait_time > 0:
                time.sleep(wait_time)
            
            result = {
                'success': True,
                'success_count': success_count,
                'total_count': total_count,
                'path': path_str
            }
            
            self.logger.info(f"批量点击完成: {path_str}, 成功{success_count}/{total_count}")
            return result
            
        except Exception as e:
            path_str = ' -> '.join(map(str, path))
            self.logger.error(f"批量点击失败: {path_str} - {str(e)}")
            return {
                'success': False,
                'success_count': 0,
                'total_count': 0,
                'path': path_str,
                'error': str(e)
            }

    def get_ui_tree(self, **kwargs):
        """
        获取当前整个UI树结构
        :param kwargs: 获取参数
                      - max_depth: 最大遍历深度，默认为10
                      - include_invisible: 是否包含不可见元素，默认为False
                      - save_to_file: 是否保存到文件，默认为None
                      - format_type: 输出格式，'dict'(默认) 或 'json' 或 'text'
                      - include_attributes: 包含的属性列表，默认为基础属性
        :return: UI树结构数据
        
        使用示例:
        # 获取基础UI树
        tree = poco.get_ui_tree()
        
        # 获取包含不可见元素的详细UI树
        tree = poco.get_ui_tree(include_invisible=True, max_depth=15)
        
        # 保存UI树到文件
        tree = poco.get_ui_tree(save_to_file="ui_tree.json", format_type="json")
        
        # 获取文本格式的UI树
        tree_text = poco.get_ui_tree(format_type="text")
        print(tree_text)
        """
        try:
            max_depth = kwargs.get('max_depth', 10)
            include_invisible = kwargs.get('include_invisible', False)
            save_to_file = kwargs.get('save_to_file', None)
            format_type = kwargs.get('format_type', 'dict')
            include_attributes = kwargs.get('include_attributes', [
                'name', 'text', 'type', 'visible', 'enabled', 'pos', 'size'
            ])
            
            self.logger.info(f"开始获取UI树结构 (深度: {max_depth}, 包含不可见: {include_invisible})")
            
            # 获取根节点
            if not self.poco:
                raise ConnectionError("Poco连接未建立")
            
            # 尝试获取根节点，根据poco类型使用不同的方法
            try:
                if self.poco_type == 'unity':
                    # Unity poco通常可以直接获取根节点
                    root_node = self.poco.agent.hierarchy.dump()
                else:
                    # Android poco使用不同的方法
                    root_node = self.poco.agent.hierarchy.dump()
            except Exception as e:
                # 如果直接获取失败，尝试通过遍历获取
                self.logger.warning(f"直接获取UI树失败，尝试手动遍历: {str(e)}")
                root_node = self._manual_traverse_ui_tree(max_depth, include_invisible, include_attributes)
            
            # 处理UI树数据
            if isinstance(root_node, dict):
                ui_tree = self._process_ui_node(root_node, max_depth, include_invisible, include_attributes)
            else:
                # 如果返回的不是字典，尝试手动遍历
                ui_tree = self._manual_traverse_ui_tree(max_depth, include_invisible, include_attributes)
            
            # 添加元数据
            ui_tree_data = {
                'metadata': {
                    'timestamp': time.time(),
                    'poco_type': self.poco_type,
                    'device_id': self.device_id,
                    'max_depth': max_depth,
                    'include_invisible': include_invisible,
                    'total_nodes': self._count_nodes(ui_tree),
                    'generation_time': time.strftime('%Y-%m-%d %H:%M:%S')
                },
                'ui_tree': ui_tree
            }
            
            # 根据格式类型处理输出
            if format_type == 'json':
                import json
                result = json.dumps(ui_tree_data, ensure_ascii=False, indent=2)
            elif format_type == 'text':
                result = self._format_tree_as_text(ui_tree_data)
            else:  # dict
                result = ui_tree_data
            
            # 保存到文件
            if save_to_file:
                self._save_ui_tree_to_file(result, save_to_file, format_type)
            
            self.logger.info(f"UI树获取完成，共{ui_tree_data['metadata']['total_nodes']}个节点")
            return result
            
        except Exception as e:
            self.logger.error(f"获取UI树失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'metadata': {
                    'timestamp': time.time(),
                    'poco_type': getattr(self, 'poco_type', 'unknown'),
                    'device_id': getattr(self, 'device_id', 'unknown')
                }
            }
    
    def _manual_traverse_ui_tree(self, max_depth, include_invisible, include_attributes):
        """
        手动遍历UI树（当直接获取失败时使用）
        """
        try:
            # 尝试从根节点开始遍历
            root_elements = []
            
            # 尝试获取所有顶级元素
            try:
                # 对于Unity，尝试获取Canvas或其他根容器
                common_root_names = ['Canvas', 'UIRoot', 'Main Camera', 'UI', 'Root']
                for root_name in common_root_names:
                    try:
                        root_element = self.poco(root_name)
                        if root_element.exists():
                            node_data = self._extract_node_data(root_element, include_attributes)
                            if include_invisible or node_data.get('visible', True):
                                children = self._traverse_children(root_element, max_depth - 1, include_invisible, include_attributes)
                                node_data['children'] = children
                                root_elements.append(node_data)
                            break
                    except:
                        continue
                
                # 如果没有找到根节点，尝试获取所有元素
                if not root_elements:
                    try:
                        all_elements = self.poco('*')  # 获取所有元素
                        if hasattr(all_elements, '__iter__'):
                            for element in all_elements:
                                try:
                                    node_data = self._extract_node_data(element, include_attributes)
                                    if include_invisible or node_data.get('visible', True):
                                        root_elements.append(node_data)
                                except:
                                    continue
                    except:
                        pass
                        
            except Exception as e:
                self.logger.warning(f"手动遍历UI树时发生错误: {str(e)}")
            
            return {
                'name': 'Root',
                'type': 'Root',
                'children': root_elements
            }
            
        except Exception as e:
            self.logger.error(f"手动遍历UI树失败: {str(e)}")
            return {'name': 'Root', 'type': 'Root', 'children': [], 'error': str(e)}
    
    def _process_ui_node(self, node, max_depth, include_invisible, include_attributes, current_depth=0):
        """
        处理UI节点数据
        """
        if current_depth >= max_depth:
            return None
        
        try:
            # 提取节点基本信息
            processed_node = {}
            
            # 处理节点属性
            for attr in include_attributes:
                if attr in node:
                    processed_node[attr] = node[attr]
            
            # 确保有基本属性
            if 'name' not in processed_node:
                processed_node['name'] = node.get('name', 'Unknown')
            if 'type' not in processed_node:
                processed_node['type'] = node.get('type', 'Unknown')
            
            # 检查可见性
            is_visible = node.get('visible', True)
            if not include_invisible and not is_visible:
                return None
            
            # 处理子节点
            children = []
            if 'children' in node and isinstance(node['children'], list):
                for child in node['children']:
                    processed_child = self._process_ui_node(
                        child, max_depth, include_invisible, include_attributes, current_depth + 1
                    )
                    if processed_child:
                        children.append(processed_child)
            
            processed_node['children'] = children
            processed_node['depth'] = current_depth
            
            return processed_node
            
        except Exception as e:
            self.logger.warning(f"处理UI节点时发生错误: {str(e)}")
            return {'name': 'Error', 'type': 'Error', 'error': str(e), 'children': []}
    
    def _traverse_children(self, parent_element, max_depth, include_invisible, include_attributes, current_depth=0):
        """
        遍历子元素
        """
        if current_depth >= max_depth:
            return []
        
        children = []
        try:
            # 尝试获取子元素
            child_elements = parent_element.children()
            if hasattr(child_elements, '__iter__'):
                for child in child_elements:
                    try:
                        node_data = self._extract_node_data(child, include_attributes)
                        if include_invisible or node_data.get('visible', True):
                            # 递归获取子元素的子元素
                            grandchildren = self._traverse_children(
                                child, max_depth, include_invisible, include_attributes, current_depth + 1
                            )
                            node_data['children'] = grandchildren
                            node_data['depth'] = current_depth
                            children.append(node_data)
                    except Exception as e:
                        self.logger.debug(f"处理子元素时发生错误: {str(e)}")
                        continue
        except Exception as e:
            self.logger.debug(f"获取子元素时发生错误: {str(e)}")
        
        return children
    
    def _extract_node_data(self, element, include_attributes):
        """
        提取元素数据
        """
        node_data = {}
        
        try:
            # 提取指定的属性
            for attr in include_attributes:
                try:
                    if attr == 'name':
                        node_data['name'] = element.attr('name') or 'Unknown'
                    elif attr == 'text':
                        node_data['text'] = element.get_text() or ''
                    elif attr == 'type':
                        node_data['type'] = element.attr('type') or 'Unknown'
                    elif attr == 'visible':
                        node_data['visible'] = element.attr('visible')
                    elif attr == 'enabled':
                        node_data['enabled'] = element.attr('enabled')
                    elif attr == 'pos':
                        node_data['pos'] = element.attr('pos')
                    elif attr == 'size':
                        node_data['size'] = element.attr('size')
                    else:
                        # 尝试获取其他属性
                        value = element.attr(attr)
                        if value is not None:
                            node_data[attr] = value
                except:
                    # 如果获取某个属性失败，继续获取其他属性
                    continue
            
            # 确保有基本属性
            if 'name' not in node_data:
                node_data['name'] = 'Unknown'
            if 'type' not in node_data:
                node_data['type'] = 'Unknown'
                
        except Exception as e:
            self.logger.debug(f"提取元素数据时发生错误: {str(e)}")
            node_data = {'name': 'Error', 'type': 'Error', 'error': str(e)}
        
        return node_data
    
    def _count_nodes(self, tree):
        """
        递归计算树中的节点数量
        """
        if not isinstance(tree, dict):
            return 0
        
        count = 1  # 当前节点
        children = tree.get('children', [])
        if isinstance(children, list):
            for child in children:
                count += self._count_nodes(child)
        
        return count
    
    def _format_tree_as_text(self, ui_tree_data):
        """
        将UI树格式化为文本
        """
        lines = []
        metadata = ui_tree_data.get('metadata', {})
        
        # 添加头部信息
        lines.append("=" * 60)
        lines.append("UI树结构")
        lines.append("=" * 60)
        lines.append(f"生成时间: {metadata.get('generation_time', 'Unknown')}")
        lines.append(f"Poco类型: {metadata.get('poco_type', 'Unknown')}")
        lines.append(f"设备ID: {metadata.get('device_id', 'Unknown')}")
        lines.append(f"最大深度: {metadata.get('max_depth', 'Unknown')}")
        lines.append(f"总节点数: {metadata.get('total_nodes', 'Unknown')}")
        lines.append("-" * 60)
        
        # 格式化树结构
        ui_tree = ui_tree_data.get('ui_tree', {})
        self._format_node_as_text(ui_tree, lines, 0)
        
        lines.append("=" * 60)
        return '\n'.join(lines)
    
    def _format_node_as_text(self, node, lines, depth):
        """
        递归格式化节点为文本
        """
        if not isinstance(node, dict):
            return
        
        indent = "  " * depth
        name = node.get('name', 'Unknown')
        node_type = node.get('type', 'Unknown')
        text = node.get('text', '')
        visible = node.get('visible', True)
        
        # 构建节点信息
        node_info = f"{indent}├─ {name} ({node_type})"
        if text:
            node_info += f" ['{text}']"
        if not visible:
            node_info += " [隐藏]"
        
        lines.append(node_info)
        
        # 处理子节点
        children = node.get('children', [])
        if isinstance(children, list):
            for child in children:
                self._format_node_as_text(child, lines, depth + 1)
    
    def _save_ui_tree_to_file(self, tree_data, filename, format_type):
        """
        保存UI树到文件
        """
        try:
            import os
            
            # 确保目录存在
            directory = os.path.dirname(filename) if os.path.dirname(filename) else '.'
            if not os.path.exists(directory):
                os.makedirs(directory)
            
            # 根据格式保存文件
            with open(filename, 'w', encoding='utf-8') as f:
                if format_type == 'json':
                    if isinstance(tree_data, str):
                        f.write(tree_data)
                    else:
                        import json
                        json.dump(tree_data, f, ensure_ascii=False, indent=2)
                else:
                    f.write(str(tree_data))
            
            self.logger.info(f"UI树已保存到文件: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存UI树到文件失败: {str(e)}")
    
    def get_ui_tree_simple(self):
        """
        获取简化的UI树结构（快速版本）
        :return: 简化的UI树数据
        
        使用示例:
        tree = poco.get_ui_tree_simple()
        print(tree)
        """
        return self.get_ui_tree(
            max_depth=5,
            include_invisible=False,
            format_type='dict',
            include_attributes=['name', 'type', 'text', 'visible']
        )
    
    def print_ui_tree(self, **kwargs):
        """
        打印UI树结构到控制台
        :param kwargs: 同get_ui_tree的参数
        
        使用示例:
        # 打印简单的UI树
        poco.print_ui_tree()
        
        # 打印详细的UI树
        poco.print_ui_tree(include_invisible=True, max_depth=10)
        """
        try:
            kwargs['format_type'] = 'text'
            tree_text = self.get_ui_tree(**kwargs)
            print(tree_text)
        except Exception as e:
            self.logger.error(f"打印UI树失败: {str(e)}")
            print(f"打印UI树失败: {str(e)}")
    
    def find_elements_in_tree(self, search_criteria, **kwargs):
        """
        在UI树中查找符合条件的元素
        :param search_criteria: 搜索条件字典，如 {'name': 'Button', 'text': '确定'}
        :param kwargs: 同get_ui_tree的参数
        :return: 符合条件的元素列表
        
        使用示例:
        # 查找所有名为Button的元素
        buttons = poco.find_elements_in_tree({'name': 'Button'})
        
        # 查找包含特定文本的元素
        elements = poco.find_elements_in_tree({'text': '确定'})
        
        # 多条件查找
        elements = poco.find_elements_in_tree({'name': 'Button', 'visible': True})
        """
        try:
            ui_tree_data = self.get_ui_tree(**kwargs)
            if 'ui_tree' not in ui_tree_data:
                return []
            
            found_elements = []
            self._search_in_tree(ui_tree_data['ui_tree'], search_criteria, found_elements)
            
            self.logger.info(f"在UI树中找到{len(found_elements)}个符合条件的元素")
            return found_elements
            
        except Exception as e:
            self.logger.error(f"在UI树中查找元素失败: {str(e)}")
            return []
    
    def _search_in_tree(self, node, criteria, results, path=""):
        """
        递归搜索UI树中的元素
        """
        if not isinstance(node, dict):
            return
        
        # 检查当前节点是否符合条件
        matches = True
        for key, value in criteria.items():
            node_value = node.get(key)
            if isinstance(value, str) and isinstance(node_value, str):
                # 字符串包含匹配
                if value.lower() not in node_value.lower():
                    matches = False
                    break
            else:
                # 精确匹配
                if node_value != value:
                    matches = False
                    break
        
        if matches:
            result_node = node.copy()
            result_node['path'] = path
            results.append(result_node)
        
        # 搜索子节点
        children = node.get('children', [])
        if isinstance(children, list):
            for i, child in enumerate(children):
                child_path = f"{path}/{node.get('name', 'Unknown')}[{i}]" if path else node.get('name', 'Unknown')
                self._search_in_tree(child, criteria, results, child_path)

    def verify_connection_stability(self, test_duration=5):
        """验证连接稳定性，执行多次基础操作确保连接可靠"""
        try:
            self.logger.info(f"开始验证Unity Poco连接稳定性（{test_duration}秒测试）...")
            
            import time
            start_time = time.time()
            success_count = 0
            total_tests = 0
            
            while time.time() - start_time < test_duration:
                try:
                    # 测试1: 获取屏幕尺寸
                    if self.poco and hasattr(self.poco, 'get_screen_size'):
                        screen_size = self.poco.get_screen_size()
                        success_count += 1
                    
                    # 测试2: 获取UI层次结构
                    if self.poco and hasattr(self.poco, 'agent'):
                        hierarchy = self.poco.agent.hierarchy.dump()
                        success_count += 1
                    
                    total_tests += 2
                    time.sleep(0.5)  # 短暂等待后继续测试
                    
                except Exception as e:
                    error_msg = str(e)
                    if any(err in error_msg for err in ["Broken pipe", "Connection reset", "Connection refused"]):
                        self.logger.warning(f"稳定性测试中发现连接问题: {error_msg}")
                        return False
                    else:
                        self.logger.debug(f"稳定性测试中的非关键错误: {error_msg}")
                        total_tests += 1
            
            success_rate = (success_count / total_tests * 100) if total_tests > 0 else 0
            self.logger.info(f"连接稳定性测试完成: 成功率 {success_rate:.1f}% ({success_count}/{total_tests})")
            
            # 如果成功率低于80%，认为连接不稳定
            if success_rate < 80:
                self.logger.warning(f"连接稳定性不足: {success_rate:.1f}%，建议重新连接")
                return False
            
            self.logger.info(f"连接稳定性验证通过: {success_rate:.1f}%")
            return True
            
        except Exception as e:
            self.logger.error(f"连接稳定性验证异常: {str(e)}")
            return False

    def pre_test_connection_check(self):
        """测试用例执行前的连接检查和重连"""
        try:
            self.logger.info("执行测试前连接检查...")
            
            # 首先检查连接是否有效
            if not self.is_connected():
                self.logger.warning("连接无效，尝试重新连接...")
                if not self.reconnect_if_needed():
                    self.logger.error("重新连接失败")
                    return False
            
            # 执行快速连接测试
            try:
                if self.poco and hasattr(self.poco, 'get_screen_size'):
                    screen_size = self.poco.get_screen_size()
                    self.logger.info(f"连接测试通过，屏幕尺寸: {screen_size}")
                    return True
            except Exception as e:
                error_msg = str(e)
                if any(err in error_msg for err in ["Broken pipe", "Connection reset", "Connection refused"]):
                    self.logger.warning(f"连接测试失败: {error_msg}，尝试重新连接...")
                    if self.reconnect_if_needed():
                        self.logger.info("重新连接成功")
                        return True
                    else:
                        self.logger.error("重新连接失败")
                        return False
                else:
                    self.logger.warning(f"连接测试出现非关键错误: {error_msg}")
                    return True
            
            return True
            
        except Exception as e:
            self.logger.error(f"测试前连接检查异常: {str(e)}")
            return False

    def handle_special_elements_by_uitree(self, **kwargs):
        """
        使用UI树方式高效处理特殊元素
        相比handle_special_elements方法，此方法通过获取UI树并直接搜索的方式，
        避免了逐个exists判断的性能问题，大大提高了处理速度
        
        :param kwargs: 可选参数
            - force_check: 是否强制检查所有特殊元素（每轮都检查完所有元素）
            - custom_elements: 自定义特殊元素配置，格式同self.special_elements
            - max_iterations: 最大循环次数，防止无限循环（默认10次）
            - iteration_delay: 每轮检查之间的延迟时间（默认0.5秒）
            - ui_tree_params: 传递给get_ui_tree的参数
        :return: 处理结果
        {
            'success': bool,
            'message': str,
            'handled_elements': list,  # 已处理的元素列表
            'details': dict,  # 每个元素的处理详情
            'iterations': int,  # 实际循环次数
            'total_handled': int,  # 总共处理的元素数量
            'ui_tree_time': float  # UI树获取耗时
        }
        """
        try:
            force_check = kwargs.get('force_check', False)
            custom_elements = kwargs.get('custom_elements', {})
            max_iterations = kwargs.get('max_iterations', 10)
            iteration_delay = kwargs.get('iteration_delay', 0.5)
            ui_tree_params = kwargs.get('ui_tree_params', {})
            
            # 合并自定义特殊元素配置
            elements_to_check = {**self.special_elements, **custom_elements}
            
            if not elements_to_check:
                return {
                    'success': True,
                    'message': '没有需要处理的特殊元素',
                    'handled_elements': [],
                    'details': {},
                    'iterations': 0,
                    'total_handled': 0,
                    'ui_tree_time': 0.0
                }
            
            self.logger.info("开始使用UI树方式循环检查特殊元素...")
            
            all_handled_elements = []
            all_details = {}
            iteration_count = 0
            total_ui_tree_time = 0.0
            
            # 循环处理特殊元素，直到没有特殊元素或达到最大循环次数
            while iteration_count < max_iterations:
                iteration_count += 1
                self.logger.info(f"第 {iteration_count} 轮UI树特殊元素检查...")
                
                # 获取UI树
                ui_tree_start_time = time.time()
                try:
                    ui_tree_result = self.get_ui_tree(**ui_tree_params)
                    ui_tree_data = ui_tree_result.get('ui_tree')
                    ui_tree_time = time.time() - ui_tree_start_time
                    total_ui_tree_time += ui_tree_time
                    self.logger.debug(f"UI树获取耗时: {ui_tree_time:.3f}秒")
                    
                    if not ui_tree_data:
                        self.logger.warning("UI树获取失败，跳过本轮检查")
                        continue
                        
                except Exception as e:
                    ui_tree_time = time.time() - ui_tree_start_time
                    total_ui_tree_time += ui_tree_time
                    self.logger.error(f"获取UI树失败: {str(e)}")
                    continue
                
                current_handled = []
                current_details = {}
                found_any_element = False
                
                # 在UI树中搜索特殊元素
                for element_path, config in elements_to_check.items():
                    try:
                        # 在UI树中搜索元素
                        found_paths = self._search_element_in_ui_tree(ui_tree_data, element_path)
                        
                        if found_paths:
                            found_any_element = True
                            self.logger.info(f"在UI树中发现特殊元素: {element_path} (找到{len(found_paths)}个匹配)")
                            
                            # 获取处理方式和参数
                            action = config.get('action')
                            params = config.get('params', {})
                            
                            # 对每个找到的路径执行操作（通常只取第一个）
                            for i, path_parts in enumerate(found_paths):
                                if i > 0 and not force_check:
                                    break  # 非强制检查模式下只处理第一个匹配的元素
                                
                                try:
                                    # 根据action执行相应的处理
                                    result = None
                                    if action == 'click':
                                        result = self.click(*path_parts, **params)
                                    elif action == 'wait_disappear':
                                        result = self.wait_disappear(*path_parts, **params)
                                    elif action == 'wait':
                                        result = self.wait(*path_parts, **params)
                                    elif action == 'text':
                                        result = self.text(*path_parts, **params)
                                    else:
                                        self.logger.warning(f"未知的处理方式: {action}")
                                        continue
                                    
                                    # 记录处理结果
                                    element_key = f"{element_path}[{i}]" if len(found_paths) > 1 else element_path
                                    current_handled.append(element_key)
                                    current_details[element_key] = {
                                        'action': action,
                                        'result': result,
                                        'params': params,
                                        'path_parts': path_parts,
                                        'full_path': ' -> '.join(path_parts),
                                        'iteration': iteration_count,
                                        'method': 'ui_tree'
                                    }
                                    
                                    self.logger.info(f"处理特殊元素完成: {element_key}, 路径: {' -> '.join(path_parts)}, 结果: {result}")
                                    
                                    # 如果不是强制检查，处理完一个元素后跳出
                                    if not force_check:
                                        break
                                        
                                except Exception as e:
                                    element_key = f"{element_path}[{i}]" if len(found_paths) > 1 else element_path
                                    self.logger.error(f"处理特殊元素失败: {element_key} - {str(e)}")
                                    current_details[element_key] = {
                                        'error': str(e),
                                        'success': False,
                                        'iteration': iteration_count,
                                        'method': 'ui_tree',
                                        'path_parts': path_parts
                                    }
                            
                            # 如果不是强制检查，处理完一个特殊元素后跳出
                            if not force_check and current_handled:
                                break
                                
                    except Exception as e:
                        self.logger.error(f"搜索特殊元素失败: {element_path} - {str(e)}")
                        current_details[element_path] = {
                            'error': str(e),
                            'success': False,
                            'iteration': iteration_count,
                            'method': 'ui_tree'
                        }
                
                # 将当前轮次的结果添加到总结果中
                all_handled_elements.extend(current_handled)
                all_details.update(current_details)
                
                # 如果本轮没有找到任何特殊元素，说明处理完成
                if not found_any_element:
                    self.logger.info(f"第 {iteration_count} 轮UI树检查未发现特殊元素，处理完成")
                    break
                
                # 如果本轮处理了元素，等待一段时间再进行下一轮检查
                if current_handled and iteration_count < max_iterations:
                    self.logger.info(f"第 {iteration_count} 轮处理了 {len(current_handled)} 个特殊元素，等待 {iteration_delay} 秒后进行下一轮检查...")
                    time.sleep(iteration_delay)
            
            # 检查是否达到最大循环次数
            if iteration_count >= max_iterations:
                self.logger.warning(f"达到最大循环次数 {max_iterations}，停止特殊元素处理")
            
            total_handled = len(all_handled_elements)
            message = f'循环 {iteration_count} 轮，总共处理了 {total_handled} 个特殊元素 (UI树总耗时: {total_ui_tree_time:.3f}秒)'
            
            self.logger.info(f"UI树特殊元素处理完成: {message}")
            
            return {
                'success': True,
                'message': message,
                'handled_elements': all_handled_elements,
                'details': all_details,
                'iterations': iteration_count,
                'total_handled': total_handled,
                'ui_tree_time': total_ui_tree_time
            }
            
        except Exception as e:
            self.logger.error(f"UI树特殊元素处理时发生错误: {str(e)}")
            return {
                'success': False,
                'message': f'UI树特殊元素处理失败: {str(e)}',
                'handled_elements': [],
                'details': {},
                'iterations': 0,
                'total_handled': 0,
                'ui_tree_time': 0.0
            }
    
    def _search_element_in_ui_tree(self, ui_tree_data, element_path):
        """
        在UI树中搜索特殊元素
        
        :param ui_tree_data: UI树数据
        :param element_path: 元素路径，支持两种格式：
                            1. 单个元素名称：'BtnClose_btn'
                            2. 路径格式：'UIMgr/green_btn'
        :return: 找到的元素路径列表 [['path', 'to', 'element'], ...]
        """
        try:
            if not ui_tree_data:
                return []
            
            # 检查是否为路径格式
            if '/' in element_path:
                # 路径格式：按照路径依次匹配
                path_parts = element_path.split('/')
                return self._search_by_path(ui_tree_data, path_parts)
            else:
                # 单个元素名称：在整个UI树中搜索
                return self._search_by_name(ui_tree_data, element_path)
                
        except Exception as e:
            self.logger.error(f"搜索UI树中的元素失败: {element_path} - {str(e)}")
            return []
    
    def _search_by_path(self, ui_tree_data, path_parts, current_path=None):
        """
        按照路径依次匹配节点
        
        :param ui_tree_data: UI树数据
        :param path_parts: 路径片段列表
        :param current_path: 当前路径
        :return: 找到的完整路径列表
        """
        if current_path is None:
            current_path = []
        
        if not path_parts:
            return [current_path]
        
        target_name = path_parts[0]
        remaining_path = path_parts[1:]
        results = []
        
        # 递归搜索当前节点及其子节点
        def search_node(node, current_node_path):
            if not isinstance(node, dict):
                return
            
            node_name = node.get('name', '')
            node_path = current_node_path + [node_name]
            
            # 如果当前节点匹配目标名称
            if node_name == target_name:
                if not remaining_path:
                    # 已到达路径末尾
                    results.append(node_path)
                else:
                    # 继续在子节点中搜索剩余路径
                    children = node.get('children', [])
                    for child in children:
                        child_results = self._search_by_path(child, remaining_path, node_path)
                        results.extend(child_results)
            
            # 在子节点中继续搜索
            children = node.get('children', [])
            for child in children:
                search_node(child, node_path)
        
        # 从根节点开始搜索
        search_node(ui_tree_data, current_path)
        
        return results
    
    def _search_by_name(self, ui_tree_data, target_name, current_path=None):
        """
        在整个UI树中搜索指定名称的节点
        
        :param ui_tree_data: UI树数据
        :param target_name: 目标节点名称
        :param current_path: 当前路径
        :return: 找到的完整路径列表
        """
        if current_path is None:
            current_path = []
        
        results = []
        
        def search_node(node, current_node_path):
            if not isinstance(node, dict):
                return
            
            node_name = node.get('name', '')
            node_path = current_node_path + [node_name]
            
            # 如果当前节点匹配目标名称
            if node_name == target_name:
                results.append(node_path)
            
            # 在子节点中继续搜索
            children = node.get('children', [])
            for child in children:
                search_node(child, node_path)
        
        # 从根节点开始搜索
        search_node(ui_tree_data, current_path)
        
        return results