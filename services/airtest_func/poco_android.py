import time
from poco.drivers.android.uiautomation import AndroidUiautomationPoco
from poco.exceptions import PocoNoSuchNodeException, PocoTargetTimeout
from ..log_common import LogService

class pocoAndroid:
    """Android设备操作封装类"""
    
    def __init__(self, device_id=None, timeout=10, screenshot_each_action=False):
        """
        初始化Android操作处理器
        :param device_id: 设备ID
        :param timeout: 操作超时时间
        :param screenshot_each_action: 每次操作后是否截图
        """
        self.device_id = device_id
        self.timeout = timeout
        self.screenshot_each_action = screenshot_each_action
        self.logger = LogService('android_privacy_handler')
        self.poco = None
        self._is_closed = False  # 添加关闭状态标志
        self._connect()
    
    def _connect(self):
        """连接到Android设备"""
        try:
            # 如果指定了device_id，需要先连接到该设备
            if self.device_id:
                from airtest.core.api import connect_device
                device = connect_device(f"android:///{self.device_id}")
                self.poco = AndroidUiautomationPoco(
                    device=device,
                    use_airtest_input=True, 
                    screenshot_each_action=self.screenshot_each_action
                )
            else:
                # 使用默认设备
                self.poco = AndroidUiautomationPoco(
                    use_airtest_input=True, 
                    screenshot_each_action=self.screenshot_each_action
                )
            self.logger.info(f"AndroidPoco连接成功: {self.device_id or '默认设备'}")
        except Exception as e:
            self.logger.error(f"AndroidPoco连接失败: {str(e)}")
            raise
    
    def _get_element(self, *path):
        """
        获取元素（支持路径链式调用和带序号的同名元素）
        :param path: 元素路径，支持以下格式:
                    1. 普通路径: _get_element("android.widget.FrameLayout", "com.toy.match.game:id/acceptBtn")
                    2. 带序号的同名元素: _get_element("android.widget.FrameLayout", "android.widget.Button", 1)
                       等价于: poco("android.widget.FrameLayout").offspring("android.widget.Button")[1]
        :return: 元素对象
        
        使用示例:
        # 普通元素
        element = handler._get_element("android.widget.FrameLayout", "com.toy.match.game:id/acceptBtn")
        
        # 同名元素中的第2个（索引为1）
        element = handler._get_element("android.widget.FrameLayout", "android.widget.Button", 1)
        
        # 同名元素中的第1个（索引为0，可省略）
        element = handler._get_element("android.widget.FrameLayout", "android.widget.Button", 0)
        element = handler._get_element("android.widget.FrameLayout", "android.widget.Button")  # 等价
        """
        try:
            if not path:
                raise ValueError("元素路径不能为空")
            
            # 处理最后一个参数是否为数字索引
            index = None
            actual_path = list(path)
            
            # 检查最后一个参数是否为整数索引
            if len(path) >= 2 and isinstance(path[-1], int):
                index = path[-1]
                actual_path = list(path[:-1])  # 移除索引部分
            
            # 构建元素路径
            element = self.poco(actual_path[0])
            for node in actual_path[1:]:
                element = element.offspring(node)
            
            # 如果指定了索引，获取对应索引的元素
            if index is not None:
                if index < 0:
                    raise ValueError(f"元素索引不能为负数: {index}")
                
                # 获取所有同名元素
                elements = element
                if hasattr(elements, '__getitem__'):
                    try:
                        element = elements[index]
                        self.logger.debug(f"获取元素路径(带索引): {' -> '.join(map(str, actual_path))}[{index}]")
                    except IndexError:
                        raise IndexError(f"元素索引超出范围: {index}，路径: {' -> '.join(map(str, actual_path))}")
                else:
                    # 如果元素不支持索引，但用户指定了索引，则报错
                    raise ValueError(f"指定路径的元素不支持索引访问: {' -> '.join(map(str, actual_path))}")
            else:
                self.logger.debug(f"获取元素路径: {' -> '.join(map(str, actual_path))}")
            
            return element
            
        except Exception as e:
            path_str = ' -> '.join(map(str, path))
            self.logger.error(f"获取元素失败: {path_str} - {str(e)}")
            raise
    
    def click(self, *path, **kwargs):
        """
        点击操作
        :param path: 元素路径，如 click("android.widget.FrameLayout", "com.toy.match.game:id/acceptBtn")
        :param kwargs: 操作参数 (wait_time, timeout)
        :return: 是否点击成功
        
        使用示例:
        handler.click("android.widget.FrameLayout", "com.toy.match.game:id/acceptBtn")
        等价于: poco("android.widget.FrameLayout").offspring("com.toy.match.game:id/acceptBtn").click()
        """
        try:
            element = self._get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            self.logger.info(f"执行点击操作: {path_str}")
            
            if element.exists():
                element.click()
                self.logger.info(f"点击成功: {path_str}")
                
                wait_time = kwargs.get('wait_time', 1)
                if wait_time > 0:
                    time.sleep(wait_time)
                
                return True
            else:
                self.logger.warning(f"元素不存在: {path_str}")
                return False
                
        except Exception as e:
            self.logger.error(f"点击操作失败: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def long_click(self, *path, **kwargs):
        """
        长按操作
        :param path: 元素路径
        :param kwargs: 操作参数
        :return: 是否长按成功
        """
        try:
            element = self._get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            self.logger.info(f"执行长按操作: {path_str}")
            
            if element.exists():
                element.long_click()
                self.logger.info(f"长按成功: {path_str}")
                
                wait_time = kwargs.get('wait_time', 1)
                if wait_time > 0:
                    time.sleep(wait_time)
                
                return True
            else:
                self.logger.warning(f"元素不存在: {path_str}")
                return False
                
        except Exception as e:
            self.logger.error(f"长按操作失败: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def swipe(self, *path, **kwargs):
        """
        滑动操作
        :param path: 元素路径
        :param kwargs: 滑动参数 (direction, distance, duration)
        :return: 是否滑动成功
        
        使用示例:
        handler.swipe("android.widget.ScrollView", direction="up", distance=0.5)
        """
        try:
            element = self._get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            direction = kwargs.get('direction', 'up')
            distance = kwargs.get('distance', 0.5)
            duration = kwargs.get('duration', 1.0)
            
            self.logger.info(f"执行滑动操作: {path_str}, 方向: {direction}, 距离: {distance}")
            
            if element.exists():
                if direction.lower() == 'up':
                    element.swipe('up', distance=distance, duration=duration)
                elif direction.lower() == 'down':
                    element.swipe('down', distance=distance, duration=duration)
                elif direction.lower() == 'left':
                    element.swipe('left', distance=distance, duration=duration)
                elif direction.lower() == 'right':
                    element.swipe('right', distance=distance, duration=duration)
                else:
                    self.logger.error(f"不支持的滑动方向: {direction}")
                    return False
                
                self.logger.info(f"滑动成功: {path_str}")
                
                wait_time = kwargs.get('wait_time', 1)
                if wait_time > 0:
                    time.sleep(wait_time)
                
                return True
            else:
                self.logger.warning(f"元素不存在: {path_str}")
                return False
                
        except Exception as e:
            self.logger.error(f"滑动操作失败: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def exists(self, *path, **kwargs):
        """
        检查元素是否存在
        :param path: 元素路径
        :param kwargs: 操作参数 (timeout)
        :return: 是否存在
        
        使用示例:
        if handler.exists("android.widget.Button", "确定"):
            print("确定按钮存在")
        """
        try:
            element = self._get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            timeout = kwargs.get('timeout', 10)
            
            self.logger.debug(f"检查元素是否存在: {path_str}")
            
            exists = element.exists()
            self.logger.debug(f"元素存在性检查结果: {path_str} = {exists}")
            
            return exists
            
        except Exception as e:
            self.logger.error(f"检查元素存在性失败: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def wait(self, *path, **kwargs):
        """
        等待元素出现
        :param path: 元素路径
        :param kwargs: 等待参数 (timeout, interval)
        :return: 是否等待成功
        
        使用示例:
        if handler.wait("android.widget.Button", "确定", timeout=10):
            handler.click("android.widget.Button", "确定")
        """
        try:
            element = self._get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            timeout = kwargs.get('timeout', self.timeout)
            interval = kwargs.get('interval', 0.5)
            
            self.logger.info(f"等待元素出现: {path_str}, 超时: {timeout}秒")
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                if element.exists():
                    self.logger.info(f"元素已出现: {path_str}")
                    return True
                time.sleep(interval)
            
            self.logger.warning(f"等待元素超时: {path_str}")
            return False
            
        except Exception as e:
            self.logger.error(f"等待元素失败: {' -> '.join(map(str, path))} - {str(e)}")
            return False
    
    def text(self, *path, **kwargs):
        """
        文本操作（获取或设置）
        获取文本: text("path1", "path2")
        设置文本: text("path1", "path2", text="hello")
        
        :param path: 元素路径
        :param kwargs: 操作参数 (text, clear_first)
        :return: 文本内容或操作结果
        
        使用示例:
        # 获取文本
        content = handler.text("android.widget.EditText")
        
        # 设置文本
        handler.text("android.widget.EditText", text="hello world")
        """
        try:
            element = self._get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            if 'text' in kwargs:
                # 设置文本
                text_value = kwargs.get('text', '')
                clear_first = kwargs.get('clear_first', True)
                
                self.logger.info(f"设置文本: {path_str} = '{text_value}'")
                
                if element.exists():
                    if clear_first:
                        element.set_text("")
                    element.set_text(text_value)
                    self.logger.info(f"文本设置成功: {path_str}")
                    
                    wait_time = kwargs.get('wait_time', 1)
                    if wait_time > 0:
                        time.sleep(wait_time)
                    
                    return True
                else:
                    self.logger.warning(f"元素不存在: {path_str}")
                    return False
            else:
                # 获取文本
                self.logger.debug(f"获取文本: {path_str}")
                
                if element.exists():
                    text_value = element.get_text()
                    self.logger.debug(f"获取文本成功: {path_str} = '{text_value}'")
                    return text_value
                else:
                    self.logger.warning(f"元素不存在: {path_str}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"文本操作失败: {' -> '.join(map(str, path))} - {str(e)}")
            return None if 'text' not in kwargs else False
    
    def get_attribute(self, *path, **kwargs):
        """
        获取元素属性
        :param path: 元素路径
        :param kwargs: 属性参数 (attr)
        :return: 属性值
        
        使用示例:
        enabled = handler.get_attribute("android.widget.Button", attr="enabled")
        """
        try:
            element = self._get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            attr = kwargs.get('attr', 'text')
            
            self.logger.debug(f"获取元素属性: {path_str}.{attr}")
            
            if element.exists():
                attr_value = element.attr(attr)
                self.logger.debug(f"获取属性成功: {path_str}.{attr} = '{attr_value}'")
                return attr_value
            else:
                self.logger.warning(f"元素不存在: {path_str}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取属性失败: {' -> '.join(map(str, path))}.{kwargs.get('attr', 'text')} - {str(e)}")
            return None
    
    def screenshot(self, filename=None):
        """
        截图
        :param filename: 截图文件名
        :return: 截图文件路径
        """
        try:
            if filename is None:
                filename = f"screenshot_{int(time.time())}.png"
            
            screenshot_path = self.poco.snapshot(filename)
            self.logger.info(f"截图成功: {screenshot_path}")
            return screenshot_path
            
        except Exception as e:
            self.logger.error(f"截图失败: {str(e)}")
            return None
    
    def close(self):
        """关闭连接"""
        try:
            if self._is_closed:
                self.logger.debug("Android Poco连接已经关闭，跳过重复关闭")
                return
            
            self._is_closed = True
            if self.poco:
                # 对Android Poco进行清理操作
                try:
                    # Android UiAutomation Poco通常不需要特殊关闭
                    # 但我们可以尝试一些清理操作
                    if hasattr(self.poco, '_driver') and self.poco._driver:
                        if hasattr(self.poco._driver, 'stop'):
                            self.poco._driver.stop()
                except Exception as cleanup_error:
                    self.logger.warning(f"清理Android Poco资源时发生错误: {str(cleanup_error)}")
                
                self.poco = None
                self.logger.info("Android Poco连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭连接失败: {str(e)}")
    
    def is_closed(self):
        """检查连接是否已关闭"""
        return self._is_closed
    
    def is_connected(self):
        """检查连接是否有效"""
        if self._is_closed or not self.poco:
            return False

        try:
            # 对于Android Poco，简单检查对象存在性
            return self.poco is not None
        except Exception:
            return False

    def get_native_poco(self):
        """
        获取原生的AndroidUiautomationPoco对象

        :return: AndroidUiautomationPoco对象，可以直接使用原生poco的所有方法

        使用示例:
        android_poco = pocoAndroid(device_id="your_device")
        native_poco = android_poco.get_native_poco()
        native_poco(text="确定").click()
        native_poco("android.widget.Button").offspring("android.widget.TextView")[0].click()
        """
        if self._is_closed or not self.poco:
            self.logger.error("Poco连接已关闭或不存在，无法获取原生poco对象")
            raise RuntimeError("Poco连接已关闭或不存在")

        self.logger.info("返回原生AndroidUiautomationPoco对象")
        return self.poco
    
    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()

    def privacyhandler(self,app_package_name):
        print(f"进入隐私弹窗判断")
        try:
            self.wait("android.widget.FrameLayout", f"{app_package_name}:id/acceptBtn", timeout=50)
            if self.exists("android.widget.FrameLayout", f"{app_package_name}:id/acceptBtn"):
                print(f"判断 是否存在")
                self.click("android.widget.FrameLayout", f"{app_package_name}:id/acceptBtn")
                print(f"判断点击")
        except Exception as e:
            self.logger.error(f"隐私弹窗判断->: {str(e)}")
    def google_play_buy(self):
        try:
            self.click("android.widget.FrameLayout","android.widget.LinearLayout","android.widget.FrameLayout","android:id/content","com.android.vending:id/0_resource_name_obfuscated","com.android.vending:id/0_resource_name_obfuscated","com.android.vending:id/0_resource_name_obfuscated","com.android.vending:id/0_resource_name_obfuscated","com.android.vending:id/0_resource_name_obfuscated","com.android.vending:id/button_group","com.android.vending:id/0_resource_name_obfuscated")
        except Exception as e:
            self.logger.error(f"google play购买错误->: {str(e)}")

    def click_by_index(self, *path, index=0, **kwargs):
        """
        根据索引点击同名元素中的指定元素
        :param path: 元素路径（不包含索引）
        :param index: 元素索引，默认为0（第一个）
        :param kwargs: 点击参数
        :return: 是否点击成功
        
        使用示例:
        # 点击第2个Button元素（索引为1）
        handler.click_by_index("android.widget.FrameLayout", "android.widget.Button", index=1)
        
        # 等价于以下写法：
        handler.click("android.widget.FrameLayout", "android.widget.Button", 1)
        """
        return self.click(*path, index, **kwargs)
    
    def exists_by_index(self, *path, index=0, **kwargs):
        """
        检查指定索引的同名元素是否存在
        :param path: 元素路径（不包含索引）
        :param index: 元素索引，默认为0（第一个）
        :param kwargs: 检查参数
        :return: 是否存在
        
        使用示例:
        # 检查第2个元素是否存在
        if handler.exists_by_index("android.widget.LinearLayout", "android.widget.Button", index=1):
            print("第2个Button元素存在")
        """
        return self.exists(*path, index, **kwargs)
    
    def text_by_index(self, *path, index=0, **kwargs):
        """
        获取或设置指定索引的同名元素的文本
        :param path: 元素路径（不包含索引）
        :param index: 元素索引，默认为0（第一个）
        :param kwargs: 文本操作参数
        :return: 文本内容或操作结果
        
        使用示例:
        # 获取第2个TextView元素的内容
        text = handler.text_by_index("android.widget.LinearLayout", "android.widget.TextView", index=1)
        
        # 设置第3个EditText的文本
        handler.text_by_index("android.widget.LinearLayout", "android.widget.EditText", index=2, text="Hello World")
        """
        return self.text(*path, index, **kwargs)
    
    def get_elements_count(self, *path):
        """
        获取指定路径下同名元素的数量
        :param path: 元素路径
        :return: 元素数量
        
        使用示例:
        # 获取所有Button元素的数量
        count = handler.get_elements_count("android.widget.LinearLayout", "android.widget.Button")
        print(f"共有{count}个按钮")
        """
        try:
            element = self._get_element(*path)
            if hasattr(element, '__len__'):
                count = len(element)
                self.logger.debug(f"元素数量: {' -> '.join(map(str, path))} = {count}")
                return count
            else:
                # 如果不是列表类型，说明只有一个元素或没有元素
                if element.exists():
                    return 1
                else:
                    return 0
        except Exception as e:
            path_str = ' -> '.join(map(str, path))
            self.logger.error(f"获取元素数量失败: {path_str} - {str(e)}")
            return 0
    
    def click_all_by_path(self, *path, **kwargs):
        """
        点击指定路径下的所有同名元素
        :param path: 元素路径
        :param kwargs: 点击参数 (click_interval, wait_time)
        :return: 点击结果统计
        
        使用示例:
        # 点击所有的按钮
        result = handler.click_all_by_path("android.widget.LinearLayout", "android.widget.Button", click_interval=0.5)
        print(f"成功点击了{result['success_count']}个按钮")
        """
        try:
            element = self._get_element(*path)
            path_str = ' -> '.join(map(str, path))
            
            click_interval = kwargs.get('click_interval', 0.3)
            wait_time = kwargs.get('wait_time', 0.5)
            
            success_count = 0
            total_count = 0
            
            if hasattr(element, '__len__'):
                # 多个同名元素
                total_count = len(element)
                self.logger.info(f"开始点击所有元素: {path_str} (共{total_count}个)")
                
                for i in range(total_count):
                    try:
                        if self.click(*path, i, wait_time=0):
                            success_count += 1
                            self.logger.info(f"成功点击第{i+1}个元素: {path_str}")
                        else:
                            self.logger.warning(f"点击第{i+1}个元素失败: {path_str}")
                        
                        if i < total_count - 1:  # 不是最后一个元素
                            time.sleep(click_interval)
                    except Exception as e:
                        self.logger.error(f"点击第{i+1}个元素异常: {path_str} - {str(e)}")
                        continue
            else:
                # 单个元素
                if element.exists():
                    total_count = 1
                    if self.click(*path, wait_time=0):
                        success_count = 1
                        self.logger.info(f"成功点击元素: {path_str}")
                    else:
                        self.logger.warning(f"点击元素失败: {path_str}")
                else:
                    self.logger.warning(f"元素不存在: {path_str}")
            
            # 最后等待
            if wait_time > 0:
                time.sleep(wait_time)
            
            result = {
                'success': True,
                'success_count': success_count,
                'total_count': total_count,
                'path': path_str
            }
            
            self.logger.info(f"批量点击完成: {path_str}, 成功{success_count}/{total_count}")
            return result
            
        except Exception as e:
            path_str = ' -> '.join(map(str, path))
            self.logger.error(f"批量点击失败: {path_str} - {str(e)}")
            return {
                'success': False,
                'success_count': 0,
                'total_count': 0,
                'path': path_str,
                'error': str(e)
            }

    def get_ui_tree(self, **kwargs):
        """
        获取当前整个UI树结构（Android版本）
        :param kwargs: 获取参数
                      - max_depth: 最大遍历深度，默认为8
                      - include_invisible: 是否包含不可见元素，默认为False
                      - save_to_file: 是否保存到文件，默认为None
                      - format_type: 输出格式，'dict'(默认) 或 'json' 或 'text'
                      - include_attributes: 包含的属性列表，默认为Android基础属性
        :return: UI树结构数据
        
        使用示例:
        # 获取基础UI树
        tree = android_poco.get_ui_tree()
        
        # 获取详细UI树并保存
        tree = android_poco.get_ui_tree(include_invisible=True, save_to_file="android_ui_tree.json", format_type="json")
        """
        try:
            max_depth = kwargs.get('max_depth', 8)
            include_invisible = kwargs.get('include_invisible', False)
            save_to_file = kwargs.get('save_to_file', None)
            format_type = kwargs.get('format_type', 'dict')
            include_attributes = kwargs.get('include_attributes', [
                'name', 'text', 'type', 'visible', 'enabled', 'pos', 'size', 'package'
            ])
            
            self.logger.info(f"开始获取Android UI树结构 (深度: {max_depth}, 包含不可见: {include_invisible})")
            
            # 获取根节点
            if not self.poco:
                raise ConnectionError("Android Poco连接未建立")
            
            # 尝试获取UI树
            try:
                root_node = self.poco.agent.hierarchy.dump()
            except Exception as e:
                # 如果直接获取失败，尝试手动遍历
                self.logger.warning(f"直接获取Android UI树失败，尝试手动遍历: {str(e)}")
                root_node = self._manual_traverse_android_tree(max_depth, include_invisible, include_attributes)
            
            # 处理UI树数据
            if isinstance(root_node, dict):
                ui_tree = self._process_android_node(root_node, max_depth, include_invisible, include_attributes)
            else:
                ui_tree = self._manual_traverse_android_tree(max_depth, include_invisible, include_attributes)
            
            # 添加元数据
            ui_tree_data = {
                'metadata': {
                    'timestamp': time.time(),
                    'poco_type': 'android',
                    'device_id': self.device_id,
                    'max_depth': max_depth,
                    'include_invisible': include_invisible,
                    'total_nodes': self._count_android_nodes(ui_tree),
                    'generation_time': time.strftime('%Y-%m-%d %H:%M:%S')
                },
                'ui_tree': ui_tree
            }
            
            # 根据格式类型处理输出
            if format_type == 'json':
                import json
                result = json.dumps(ui_tree_data, ensure_ascii=False, indent=2)
            elif format_type == 'text':
                result = self._format_android_tree_as_text(ui_tree_data)
            else:  # dict
                result = ui_tree_data
            
            # 保存到文件
            if save_to_file:
                self._save_android_tree_to_file(result, save_to_file, format_type)
            
            self.logger.info(f"Android UI树获取完成，共{ui_tree_data['metadata']['total_nodes']}个节点")
            return result
            
        except Exception as e:
            self.logger.error(f"获取Android UI树失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'metadata': {
                    'timestamp': time.time(),
                    'poco_type': 'android',
                    'device_id': getattr(self, 'device_id', 'unknown')
                }
            }
    
    def _manual_traverse_android_tree(self, max_depth, include_invisible, include_attributes):
        """
        手动遍历Android UI树
        """
        try:
            root_elements = []
            
            # Android常见的根容器
            android_root_patterns = [
                'android.widget.FrameLayout',
                'android.widget.LinearLayout',
                'android.widget.RelativeLayout',
                'android.view.ViewGroup',
                'android.widget.ScrollView'
            ]
            
            for pattern in android_root_patterns:
                try:
                    root_element = self.poco(pattern)
                    if root_element.exists():
                        node_data = self._extract_android_node_data(root_element, include_attributes)
                        if include_invisible or node_data.get('visible', True):
                            children = self._traverse_android_children(root_element, max_depth - 1, include_invisible, include_attributes)
                            node_data['children'] = children
                            root_elements.append(node_data)
                        break
                except:
                    continue
            
            return {
                'name': 'AndroidRoot',
                'type': 'Root',
                'children': root_elements
            }
            
        except Exception as e:
            self.logger.error(f"手动遍历Android UI树失败: {str(e)}")
            return {'name': 'AndroidRoot', 'type': 'Root', 'children': [], 'error': str(e)}
    
    def _process_android_node(self, node, max_depth, include_invisible, include_attributes, current_depth=0):
        """
        处理Android UI节点数据
        """
        if current_depth >= max_depth:
            return None
        
        try:
            processed_node = {}
            
            # 处理节点属性
            for attr in include_attributes:
                if attr in node:
                    processed_node[attr] = node[attr]
            
            # 确保有基本属性
            if 'name' not in processed_node:
                processed_node['name'] = node.get('name', 'Unknown')
            if 'type' not in processed_node:
                processed_node['type'] = node.get('type', 'Unknown')
            
            # 检查可见性
            is_visible = node.get('visible', True)
            if not include_invisible and not is_visible:
                return None
            
            # 处理子节点
            children = []
            if 'children' in node and isinstance(node['children'], list):
                for child in node['children']:
                    processed_child = self._process_android_node(
                        child, max_depth, include_invisible, include_attributes, current_depth + 1
                    )
                    if processed_child:
                        children.append(processed_child)
            
            processed_node['children'] = children
            processed_node['depth'] = current_depth
            
            return processed_node
            
        except Exception as e:
            self.logger.warning(f"处理Android UI节点时发生错误: {str(e)}")
            return {'name': 'Error', 'type': 'Error', 'error': str(e), 'children': []}
    
    def _traverse_android_children(self, parent_element, max_depth, include_invisible, include_attributes, current_depth=0):
        """
        遍历Android子元素
        """
        if current_depth >= max_depth:
            return []
        
        children = []
        try:
            child_elements = parent_element.children()
            if hasattr(child_elements, '__iter__'):
                for child in child_elements:
                    try:
                        node_data = self._extract_android_node_data(child, include_attributes)
                        if include_invisible or node_data.get('visible', True):
                            grandchildren = self._traverse_android_children(
                                child, max_depth, include_invisible, include_attributes, current_depth + 1
                            )
                            node_data['children'] = grandchildren
                            node_data['depth'] = current_depth
                            children.append(node_data)
                    except Exception as e:
                        self.logger.debug(f"处理Android子元素时发生错误: {str(e)}")
                        continue
        except Exception as e:
            self.logger.debug(f"获取Android子元素时发生错误: {str(e)}")
        
        return children
    
    def _extract_android_node_data(self, element, include_attributes):
        """
        提取Android元素数据
        """
        node_data = {}
        
        try:
            for attr in include_attributes:
                try:
                    if attr == 'name':
                        node_data['name'] = element.attr('name') or 'Unknown'
                    elif attr == 'text':
                        node_data['text'] = element.get_text() or ''
                    elif attr == 'type':
                        node_data['type'] = element.attr('type') or 'Unknown'
                    elif attr == 'visible':
                        node_data['visible'] = element.attr('visible')
                    elif attr == 'enabled':
                        node_data['enabled'] = element.attr('enabled')
                    elif attr == 'pos':
                        node_data['pos'] = element.attr('pos')
                    elif attr == 'size':
                        node_data['size'] = element.attr('size')
                    elif attr == 'package':
                        node_data['package'] = element.attr('package')
                    else:
                        value = element.attr(attr)
                        if value is not None:
                            node_data[attr] = value
                except:
                    continue
            
            # 确保有基本属性
            if 'name' not in node_data:
                node_data['name'] = 'Unknown'
            if 'type' not in node_data:
                node_data['type'] = 'Unknown'
                
        except Exception as e:
            self.logger.debug(f"提取Android元素数据时发生错误: {str(e)}")
            node_data = {'name': 'Error', 'type': 'Error', 'error': str(e)}
        
        return node_data
    
    def _count_android_nodes(self, tree):
        """
        递归计算Android树中的节点数量
        """
        if not isinstance(tree, dict):
            return 0
        
        count = 1
        children = tree.get('children', [])
        if isinstance(children, list):
            for child in children:
                count += self._count_android_nodes(child)
        
        return count
    
    def _format_android_tree_as_text(self, ui_tree_data):
        """
        将Android UI树格式化为文本
        """
        lines = []
        metadata = ui_tree_data.get('metadata', {})
        
        # 添加头部信息
        lines.append("=" * 60)
        lines.append("Android UI树结构")
        lines.append("=" * 60)
        lines.append(f"生成时间: {metadata.get('generation_time', 'Unknown')}")
        lines.append(f"设备ID: {metadata.get('device_id', 'Unknown')}")
        lines.append(f"最大深度: {metadata.get('max_depth', 'Unknown')}")
        lines.append(f"总节点数: {metadata.get('total_nodes', 'Unknown')}")
        lines.append("-" * 60)
        
        # 格式化树结构
        ui_tree = ui_tree_data.get('ui_tree', {})
        self._format_android_node_as_text(ui_tree, lines, 0)
        
        lines.append("=" * 60)
        return '\n'.join(lines)
    
    def _format_android_node_as_text(self, node, lines, depth):
        """
        递归格式化Android节点为文本
        """
        if not isinstance(node, dict):
            return
        
        indent = "  " * depth
        name = node.get('name', 'Unknown')
        node_type = node.get('type', 'Unknown')
        text = node.get('text', '')
        visible = node.get('visible', True)
        package = node.get('package', '')
        
        # 构建节点信息
        node_info = f"{indent}├─ {name} ({node_type})"
        if text:
            node_info += f" ['{text}']"
        if package:
            node_info += f" [{package}]"
        if not visible:
            node_info += " [隐藏]"
        
        lines.append(node_info)
        
        # 处理子节点
        children = node.get('children', [])
        if isinstance(children, list):
            for child in children:
                self._format_android_node_as_text(child, lines, depth + 1)
    
    def _save_android_tree_to_file(self, tree_data, filename, format_type):
        """
        保存Android UI树到文件
        """
        try:
            import os
            
            directory = os.path.dirname(filename) if os.path.dirname(filename) else '.'
            if not os.path.exists(directory):
                os.makedirs(directory)
            
            with open(filename, 'w', encoding='utf-8') as f:
                if format_type == 'json':
                    if isinstance(tree_data, str):
                        f.write(tree_data)
                    else:
                        import json
                        json.dump(tree_data, f, ensure_ascii=False, indent=2)
                else:
                    f.write(str(tree_data))
            
            self.logger.info(f"Android UI树已保存到文件: {filename}")
            
        except Exception as e:
            self.logger.error(f"保存Android UI树到文件失败: {str(e)}")
    
    def get_ui_tree_simple(self):
        """
        获取简化的Android UI树结构（快速版本）
        :return: 简化的UI树数据
        """
        return self.get_ui_tree(
            max_depth=5,
            include_invisible=False,
            format_type='dict',
            include_attributes=['name', 'type', 'text', 'visible']
        )
    
    def print_ui_tree(self, **kwargs):
        """
        打印Android UI树结构到控制台
        :param kwargs: 同get_ui_tree的参数
        """
        try:
            kwargs['format_type'] = 'text'
            tree_text = self.get_ui_tree(**kwargs)
            print(tree_text)
        except Exception as e:
            self.logger.error(f"打印Android UI树失败: {str(e)}")
            print(f"打印Android UI树失败: {str(e)}")
    
    def find_elements_in_tree(self, search_criteria, **kwargs):
        """
        在Android UI树中查找符合条件的元素
        :param search_criteria: 搜索条件字典
        :param kwargs: 同get_ui_tree的参数
        :return: 符合条件的元素列表
        """
        try:
            ui_tree_data = self.get_ui_tree(**kwargs)
            if 'ui_tree' not in ui_tree_data:
                return []
            
            found_elements = []
            self._search_in_android_tree(ui_tree_data['ui_tree'], search_criteria, found_elements)
            
            self.logger.info(f"在Android UI树中找到{len(found_elements)}个符合条件的元素")
            return found_elements
            
        except Exception as e:
            self.logger.error(f"在Android UI树中查找元素失败: {str(e)}")
            return []
    
    def _search_in_android_tree(self, node, criteria, results, path=""):
        """
        递归搜索Android UI树中的元素
        """
        if not isinstance(node, dict):
            return
        
        # 检查当前节点是否符合条件
        matches = True
        for key, value in criteria.items():
            node_value = node.get(key)
            if isinstance(value, str) and isinstance(node_value, str):
                if value.lower() not in node_value.lower():
                    matches = False
                    break
            else:
                if node_value != value:
                    matches = False
                    break
        
        if matches:
            result_node = node.copy()
            result_node['path'] = path
            results.append(result_node)
        
        # 搜索子节点
        children = node.get('children', [])
        if isinstance(children, list):
            for i, child in enumerate(children):
                child_path = f"{path}/{node.get('name', 'Unknown')}[{i}]" if path else node.get('name', 'Unknown')
                self._search_in_android_tree(child, criteria, results, child_path)

def android_poco(device_id=None, **kwargs):
    """
    便捷函数：创建AndroidPrivacyHandler实例
    :param device_id: 设备ID
    :param kwargs: 其他参数
    :return: AndroidPrivacyHandler实例
    
    使用示例:
    handler = android_privacy_handler(device_id="R5CWC1899HY")
    handler.click("android.widget.FrameLayout", "com.toy.match.game:id/acceptBtn")
    """
    return pocoAndroid(device_id=device_id, **kwargs)