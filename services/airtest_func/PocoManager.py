import time
import threading
from typing import Optional, Dict, Any
from .poco_unity import PocoUnity
from .poco_android import pocoAndroid
from ..log_common import LogService


class DeviceTestManager:
    """设备测试管理器 - 管理单个设备的测试状态和多种Poco连接"""
    
    def __init__(self, device_id: str):
        self.device_id = device_id
        self.is_testing = False
        self.poco_connections = {}  # 存储多种poco连接 {'android': poco_instance, 'unity': poco_instance}
        self.test_thread = None
        self.stop_event = threading.Event()
        self.logger = LogService(f'DeviceTestManager_{device_id}')
        self._lock = threading.Lock()
    
    def start_test(self, poco_type: str = 'unity', **kwargs):
        """开始测试，创建指定类型的Poco连接"""
        with self._lock:
            self.logger.info(f"设备 {self.device_id} 收到 {poco_type} Poco启动请求")
            
            # 如果是第一次启动测试，初始化测试状态
            if not self.is_testing:
                self.logger.info(f"设备 {self.device_id} 开始测试会话")
                self.is_testing = True
                self.stop_event.clear()
            
            # 检查是否已存在该类型的连接
            if poco_type in self.poco_connections:
                self.logger.info(f"设备 {self.device_id} {poco_type} Poco连接已存在，返回现有连接")
                return self.poco_connections[poco_type]
            
            self.logger.info(f"设备 {self.device_id} 创建 {poco_type} Poco连接")
            
            try:
                # 根据类型创建Poco实例
                if poco_type == 'unity':
                    unity_defaults = {
                        'game_startup_wait': 25,     # 增加默认等待时间
                        'fallback_to_android': True,  # 默认允许降级
                        'max_unity_retries': 5,      # 增加重试次数
                        'unity_retry_interval': 5,   # 增加重试间隔
                        'timeout': 15                # 增加操作超时时间
                    }
                    unity_params = {**unity_defaults, **kwargs}
                    # 移除PocoUnity不支持的参数
                    unity_params.pop('dynamic_detection', None)
                    poco_instance = PocoUnity(device_id=self.device_id, **unity_params)
                else:
                    poco_instance = pocoAndroid(device_id=self.device_id, **kwargs)
                
                # 存储连接
                self.poco_connections[poco_type] = poco_instance
                self.logger.info(f"设备 {self.device_id} {poco_type} Poco连接创建成功")
                return poco_instance
                
            except Exception as e:
                self.logger.error(f"设备 {self.device_id} {poco_type} Poco连接创建失败: {str(e)}")
                # 如果没有任何连接，重置测试状态
                if not self.poco_connections:
                    self.is_testing = False
                raise
    
    def stop_test(self):
        """停止测试，清理所有Poco连接"""
        with self._lock:
            if not self.is_testing:
                self.logger.debug(f"设备 {self.device_id} 当前未在测试中，无需停止")
                return
            
            self.logger.info(f"设备 {self.device_id} 停止测试，清理所有Poco连接")
            self.stop_event.set()
            self.is_testing = False
            self.logger.info(f"设备 {self.device_id} 测试状态已重置: is_testing={self.is_testing}")
            
            # 关闭所有Poco连接
            for poco_type, poco_instance in self.poco_connections.items():
                try:
                    self.logger.info(f"关闭设备 {self.device_id} 的 {poco_type} Poco连接")
                    
                    # 强制关闭连接
                    if hasattr(poco_instance, 'close'):
                        poco_instance.close()
                    
                    # 额外的清理操作
                    if hasattr(poco_instance, 'poco') and poco_instance.poco:
                        try:
                            # 尝试关闭底层连接
                            if hasattr(poco_instance.poco, 'agent'):
                                if hasattr(poco_instance.poco.agent, 'close'):
                                    poco_instance.poco.agent.close()
                        except Exception as agent_error:
                            self.logger.debug(f"关闭{poco_type} agent连接时发生异常: {str(agent_error)}")
                    
                    self.logger.info(f"设备 {self.device_id} {poco_type} Poco连接已关闭")
                except Exception as e:
                    self.logger.warning(f"关闭设备 {self.device_id} {poco_type} Poco连接时发生异常: {str(e)}")
            
            # 清空连接字典
            self.poco_connections.clear()
                    
            # 强制等待一段时间，确保连接完全关闭
            import time
            time.sleep(0.5)
    
    def get_poco(self, poco_type: str = None):
        """获取指定类型的Poco实例"""
        if not self.is_testing:
            return None
        
        if poco_type:
            return self.poco_connections.get(poco_type)
        else:
            # 如果没有指定类型，优先返回Unity Poco，其次Android Poco
            if 'unity' in self.poco_connections:
                return self.poco_connections['unity']
            elif 'android' in self.poco_connections:
                return self.poco_connections['android']
            return None
    
    def is_poco_valid(self, poco_type: str = None):
        """检查指定类型的Poco连接是否有效"""
        if not self.is_testing:
            return False
        
        if poco_type:
            poco_instance = self.poco_connections.get(poco_type)
            if not poco_instance:
                return False
        else:
            # 检查所有连接是否至少有一个有效
            if not self.poco_connections:
                return False
            poco_instance = next(iter(self.poco_connections.values()))
        
        try:
            if hasattr(poco_instance, 'reconnect_if_needed'):
                return poco_instance.reconnect_if_needed() or True
            else:
                return hasattr(poco_instance, 'poco') and poco_instance.poco is not None
        except Exception as e:
            self.logger.warning(f"设备 {self.device_id} Poco连接检查失败: {str(e)}")
            return False
    
    def get_available_poco_types(self):
        """获取当前可用的Poco类型列表"""
        return list(self.poco_connections.keys())


class PocoManager:
    """
    Poco管理器 - 统一管理Unity和Android Poco实例
    优化的异步测试管理，支持设备级的状态控制
    """
    
    _device_managers: Dict[str, DeviceTestManager] = {}  # 设备管理器
    _instances: Dict[str, Any] = {}  # 存储不同设备和类型的poco实例（向后兼容）
    _lock = threading.Lock()  # 线程锁，确保线程安全
    _logger = LogService('PocoManager')
    
    # 支持的poco类型
    POCO_TYPES = {
        'unity': PocoUnity,
        'android': pocoAndroid
    }
    
    @classmethod
    def start_device_test(cls, device_id: str, poco_type: str = 'unity', **kwargs) -> Any:
        """
        开始设备测试 - 创建设备管理器并启动测试
        
        :param device_id: 设备ID
        :param poco_type: poco类型，'unity' 或 'android'
        :param kwargs: 传递给poco构造函数的参数
        :return: poco实例
        """
        if poco_type not in cls.POCO_TYPES:
            raise ValueError(f"不支持的poco类型: {poco_type}。支持的类型: {list(cls.POCO_TYPES.keys())}")
        
        with cls._lock:
            # 获取或创建设备管理器
            if device_id not in cls._device_managers:
                cls._device_managers[device_id] = DeviceTestManager(device_id)
            
            device_manager = cls._device_managers[device_id]
            
            # 启动测试，创建Poco连接
            poco_instance = device_manager.start_test(poco_type, **kwargs)
            cls._logger.info(f"设备 {device_id} 测试已启动，Poco类型: {poco_type}")
            return poco_instance
    
    @classmethod
    def stop_device_test(cls, device_id: str):
        """
        停止设备测试 - 清理Poco连接和资源
        
        :param device_id: 设备ID
        """
        with cls._lock:
            if device_id in cls._device_managers:
                device_manager = cls._device_managers[device_id]
                device_manager.stop_test()
                cls._logger.info(f"设备 {device_id} 测试已停止")
    
    @classmethod
    def get_device_poco(cls, device_id: str, poco_type: str = None) -> Optional[Any]:
        """
        获取设备的指定类型Poco实例
        
        :param device_id: 设备ID
        :param poco_type: poco类型，None表示获取默认poco
        :return: poco实例或None
        """
        with cls._lock:
            if device_id in cls._device_managers:
                return cls._device_managers[device_id].get_poco(poco_type)
            return None
    
    @classmethod
    def is_device_testing(cls, device_id: str) -> bool:
        """
        检查设备是否正在测试
        
        :param device_id: 设备ID
        :return: 是否正在测试
        """
        with cls._lock:
            if device_id in cls._device_managers:
                return cls._device_managers[device_id].is_testing
            return False
    
    @classmethod
    def get_poco(cls, device_id: Optional[str] = None, 
                 poco_type: str = 'unity', 
                 force_recreate: bool = False,
                 **kwargs) -> Any:
        """
        获取或创建poco实例 (向后兼容方法)
        建议使用 start_device_test 方法替代
        
        :param device_id: 设备ID，None表示使用默认设备
        :param poco_type: poco类型，'unity' 或 'android'
        :param force_recreate: 是否强制重新创建实例
        :param kwargs: 传递给poco构造函数的参数
        :return: poco实例
        """
        if device_id is None:
            device_id = "default"
        
        if poco_type not in cls.POCO_TYPES:
            raise ValueError(f"不支持的poco类型: {poco_type}。支持的类型: {list(cls.POCO_TYPES.keys())}")
        
        # 生成唯一的实例键
        key = cls._generate_key(device_id, poco_type)
        
        with cls._lock:
            # 如果需要强制重新创建或实例不存在，则创建新实例
            if force_recreate or key not in cls._instances:
                cls._create_instance(key, device_id, poco_type, **kwargs)
            
            # 检查实例是否有效
            if not cls._is_instance_valid(key):
                cls._logger.warning(f"实例{key}无效，重新创建")
                cls._create_instance(key, device_id, poco_type, **kwargs)
            
            cls._logger.debug(f"返回poco实例: {key}")
            return cls._instances[key]
    
    @classmethod
    def _generate_key(cls, device_id: Optional[str], poco_type: str) -> str:
        """生成实例的唯一键"""
        device_key = device_id
        return f"{device_key}_{poco_type}"
    
    @classmethod
    def _create_instance(cls, key: str, device_id: Optional[str], 
                        poco_type: str, **kwargs):
        """创建新的poco实例"""
        try:
            # 如果已存在实例，先关闭它
            if key in cls._instances:
                cls._close_instance(key)
            
            cls._logger.info(f"创建新的{poco_type} poco实例: {key}")
            
            # 创建相应类型的poco实例
            poco_class = cls.POCO_TYPES[poco_type]
            
            if poco_type == 'unity':
                # 设置Unity Poco的默认参数，支持用户自定义覆盖
                unity_defaults = {
                    'game_startup_wait': 20,      # 默认20秒启动等待
                    'fallback_to_android': False, # 默认不允许降级
                    'max_unity_retries': 5,       # 默认重试5次
                    'unity_retry_interval': 5     # 默认重试间隔5秒
                }
                # 用户参数覆盖默认参数
                unity_params = {**unity_defaults, **kwargs}
                instance = poco_class(device_id=device_id, **unity_params)
            else:
                instance = poco_class(device_id=device_id, **kwargs)
            
            cls._instances[key] = instance
            cls._logger.info(f"成功创建{poco_type} poco实例: {key}")
            
        except Exception as e:
            cls._logger.error(f"创建poco实例失败: {key} - {str(e)}")
            raise
    
    @classmethod
    def _is_instance_valid(cls, key: str) -> bool:
        """检查实例是否有效"""
        if key not in cls._instances:
            return False
        
        try:
            instance = cls._instances[key]
            # 检查poco对象是否存在
            if not hasattr(instance, 'poco') or instance.poco is None:
                return False
            
            # 尝试简单的连接测试
            if hasattr(instance, 'reconnect_if_needed'):
                # 对于PocoUniversal，使用其自带的连接检查
                return instance.reconnect_if_needed() or True
            else:
                # 对于AndroidPrivacyHandler，检查poco对象是否可用
                return instance.poco is not None
                
        except Exception as e:
            cls._logger.debug(f"实例有效性检查失败: {key} - {str(e)}")
            return False
    
    @classmethod
    def _close_instance(cls, key: str):
        """关闭并清理实例"""
        if key in cls._instances:
            try:
                instance = cls._instances[key]
                if hasattr(instance, 'close'):
                    instance.close()
                cls._logger.debug(f"关闭poco实例: {key}")
            except Exception as e:
                cls._logger.warning(f"关闭poco实例异常: {key} - {str(e)}")
            finally:
                del cls._instances[key]
    
    @classmethod
    def release_poco(cls, device_id: Optional[str] = None, 
                    poco_type: Optional[str] = None):
        """
        释放poco实例
        
        :param device_id: 设备ID，None表示释放所有设备
        :param poco_type: poco类型，None表示释放所有类型
        """
        with cls._lock:
            keys_to_remove = []
            
            for key in cls._instances.keys():
                should_remove = True
                
                if device_id is not None:
                    # 检查设备ID是否匹配
                    device_key = device_id or "default"
                    if not key.startswith(f"{device_key}_"):
                        should_remove = False
                
                if poco_type is not None and should_remove:
                    # 检查poco类型是否匹配
                    if not key.endswith(f"_{poco_type}"):
                        should_remove = False
                
                if should_remove:
                    keys_to_remove.append(key)
            
            # 释放符合条件的实例
            for key in keys_to_remove:
                cls._close_instance(key)
                cls._logger.info(f"释放poco实例: {key}")
    
    @classmethod
    def release_all(cls):
        """释放所有poco实例"""
        with cls._lock:
            # 停止所有设备测试
            device_ids = list(cls._device_managers.keys())
            for device_id in device_ids:
                cls.stop_device_test(device_id)
            cls._device_managers.clear()
            
            # 清理旧的实例（向后兼容）
            keys = list(cls._instances.keys())
            for key in keys:
                cls._close_instance(key)
            cls._logger.info("释放所有poco实例")
    
    @classmethod
    def get_device_status(cls) -> Dict[str, Dict[str, Any]]:
        """
        获取所有设备的测试状态
        
        :return: 设备状态字典
        """
        with cls._lock:
            status = {}
            for device_id, manager in cls._device_managers.items():
                available_types = manager.get_available_poco_types()
                status[device_id] = {
                    'is_testing': manager.is_testing,
                    'poco_types': available_types,
                    'poco_valid': {poco_type: manager.is_poco_valid(poco_type) for poco_type in available_types}
                }
            return status
    
    @classmethod
    def get_active_instances(cls) -> Dict[str, str]:
        """
        获取当前活跃的实例信息
        
        :return: {key: type} 格式的字典
        """
        with cls._lock:
            result = {}
            for key, instance in cls._instances.items():
                instance_type = type(instance).__name__
                result[key] = instance_type
            return result
    
    @classmethod
    def auto_switch_poco(cls, device_id: Optional[str] = None,
                        operation_type: str = 'game', **kwargs) -> Any:
        """
        根据操作类型自动选择合适的poco

        :param device_id: 设备ID
        :param operation_type: 操作类型
            - 'game': 游戏操作，使用unity poco
            - 'system': 系统操作，使用android poco
            - 'privacy': 隐私弹窗处理，使用android poco
            - 'purchase': 购买操作，使用android poco
        :param kwargs: 传递给poco的参数
        :return: 对应的poco实例
        """
        # 根据操作类型选择poco类型
        if operation_type in ['system', 'privacy', 'purchase']:
            poco_type = 'android'
        else:
            poco_type = 'unity'

        cls._logger.info(f"自动选择poco类型: {operation_type} -> {poco_type}")
        return cls.get_poco(device_id=device_id, poco_type=poco_type, **kwargs)

    @classmethod
    def get_native_poco(cls, device_id: Optional[str] = None,
                       poco_type: str = 'unity', **kwargs) -> Any:
        """
        获取原生的poco对象，可以直接使用原生poco的所有方法

        :param device_id: 设备ID
        :param poco_type: poco类型 ('unity' 或 'android')
        :param kwargs: 传递给poco的参数
        :return: 原生poco对象 (UnityPoco 或 AndroidUiautomationPoco)

        使用示例:
        # 获取Unity原生poco
        native_poco = PocoManager.get_native_poco(device_id="your_device", poco_type='unity')
        native_poco("SR_Canvas").offspring("SR_Content").child("Category(Clone)")[1].child("NumberOption(Clone)")[0].child("SR_Title").click()
        native_poco(text="卡包Id").click()

        # 获取Android原生poco
        native_poco = PocoManager.get_native_poco(device_id="your_device", poco_type='android')
        native_poco("android.widget.Button").offspring("android.widget.TextView")[0].click()
        """
        cls._logger.info(f"获取原生{poco_type}Poco对象")

        # 先获取封装的poco实例
        poco_instance = cls.get_poco(device_id=device_id, poco_type=poco_type, **kwargs)

        # 调用实例的get_native_poco方法获取原生poco对象
        return poco_instance.get_native_poco()


class SmartPoco:
    """
    智能Poco封装类 - 提供统一的接口，自动选择合适的poco类型
    """
    
    def __init__(self, device_id: Optional[str] = None, **kwargs):
        """
        初始化智能Poco
        
        :param device_id: 设备ID
        :param kwargs: 传递给poco的参数
        """
        self.device_id = device_id
        self.kwargs = kwargs
        self.logger = LogService('SmartPoco')
        
        # 预创建unity poco（游戏操作最常用）
        self._unity_poco = None
        self._android_poco = None
    
    def get_unity_poco(self) -> PocoUnity:
        """获取Unity Poco实例"""
        if self._unity_poco is None:
            self._unity_poco = PocoManager.get_poco(
                device_id=self.device_id, 
                poco_type='unity', 
                **self.kwargs
            )
        return self._unity_poco
    
    def get_android_poco(self) -> pocoAndroid:
        """获取Android Poco实例"""
        if self._android_poco is None:
            self._android_poco = PocoManager.get_poco(
                device_id=self.device_id,
                poco_type='android',
                **self.kwargs
            )
        return self._android_poco

    def get_native_unity_poco(self):
        """获取原生Unity Poco对象"""
        unity_poco = self.get_unity_poco()
        return unity_poco.get_native_poco()

    def get_native_android_poco(self):
        """获取原生Android Poco对象"""
        android_poco = self.get_android_poco()
        return android_poco.get_native_poco()

    def get_native_poco(self, operation_type: str = 'game'):
        """
        根据操作类型获取原生poco对象

        :param operation_type: 操作类型
            - 'game': 游戏操作，返回原生Unity poco
            - 'system': 系统操作，返回原生Android poco
            - 'privacy': 隐私弹窗处理，返回原生Android poco
            - 'purchase': 购买操作，返回原生Android poco
        :return: 原生poco对象

        使用示例:
        smart_poco = SmartPoco(device_id="your_device")

        # 获取Unity原生poco
        native_poco = smart_poco.get_native_poco('game')
        native_poco("SR_Canvas").offspring("SR_Content").child("Category(Clone)")[1].click()

        # 获取Android原生poco
        native_poco = smart_poco.get_native_poco('system')
        native_poco("android.widget.Button").click()
        """
        if operation_type in ['system', 'privacy', 'purchase']:
            return self.get_native_android_poco()
        else:
            return self.get_native_unity_poco()
    
    def click(self, *path, operation_type: str = 'game', **kwargs):
        """
        智能点击 - 根据操作类型自动选择poco
        
        :param path: 元素路径
        :param operation_type: 操作类型
        :param kwargs: 点击参数
        :return: 操作结果
        """
        if operation_type in ['system', 'privacy', 'purchase']:
            poco = self.get_android_poco()
        else:
            poco = self.get_unity_poco()
        
        self.logger.info(f"智能点击: {operation_type} -> {type(poco).__name__}")
        return poco.click(*path, **kwargs)
    
    def wait_and_click(self, *path, operation_type: str = 'game', **kwargs):
        """
        智能等待并点击
        
        :param path: 元素路径
        :param operation_type: 操作类型
        :param kwargs: 操作参数
        :return: 操作结果
        """
        if operation_type in ['system', 'privacy', 'purchase']:
            poco = self.get_android_poco()
            # Android poco可能没有wait_and_click方法，先等待再点击
            if hasattr(poco, 'wait_and_click'):
                return poco.wait_and_click(*path, **kwargs)
            else:
                if poco.wait(*path, **kwargs):
                    return poco.click(*path, **kwargs)
                return False
        else:
            poco = self.get_unity_poco()
            return poco.wait_and_click(*path, **kwargs)
    
    def handle_privacy_dialogs(self, app_package_name: str):
        """处理隐私弹窗"""
        android_poco = self.get_android_poco()
        return android_poco.privacyhandler(app_package_name)
    
    def close(self):
        """关闭所有poco连接"""
        PocoManager.release_poco(device_id=self.device_id)
    
    def get_ui_tree(self, operation_type: str = 'game', **kwargs):
        """
        智能获取UI树 - 根据操作类型自动选择poco
        
        :param operation_type: 操作类型，决定使用Unity还是Android poco
        :param kwargs: UI树获取参数
        :return: UI树结构数据
        """
        if operation_type in ['system', 'privacy', 'purchase']:
            poco = self.get_android_poco()
        else:
            poco = self.get_unity_poco()
        
        self.logger.info(f"智能获取UI树: {operation_type} -> {type(poco).__name__}")
        return poco.get_ui_tree(**kwargs)
    
    def print_ui_tree(self, operation_type: str = 'game', **kwargs):
        """
        智能打印UI树到控制台
        
        :param operation_type: 操作类型
        :param kwargs: UI树参数
        """
        if operation_type in ['system', 'privacy', 'purchase']:
            poco = self.get_android_poco()
        else:
            poco = self.get_unity_poco()
        
        self.logger.info(f"智能打印UI树: {operation_type} -> {type(poco).__name__}")
        return poco.print_ui_tree(**kwargs)
    
    def find_elements_in_tree(self, search_criteria, operation_type: str = 'game', **kwargs):
        """
        智能在UI树中查找元素
        
        :param search_criteria: 搜索条件
        :param operation_type: 操作类型
        :param kwargs: 搜索参数
        :return: 符合条件的元素列表
        """
        if operation_type in ['system', 'privacy', 'purchase']:
            poco = self.get_android_poco()
        else:
            poco = self.get_unity_poco()
        
        self.logger.info(f"智能搜索UI树: {operation_type} -> {type(poco).__name__}")
        return poco.find_elements_in_tree(search_criteria, **kwargs)
    
    def get_ui_tree_simple(self, operation_type: str = 'game'):
        """
        智能获取简化的UI树
        
        :param operation_type: 操作类型
        :return: 简化的UI树数据
        """
        if operation_type in ['system', 'privacy', 'purchase']:
            poco = self.get_android_poco()
        else:
            poco = self.get_unity_poco()
        
        self.logger.info(f"智能获取简化UI树: {operation_type} -> {type(poco).__name__}")
        return poco.get_ui_tree_simple()
    
    def save_ui_tree_comparison(self, filename_prefix="ui_comparison", **kwargs):
        """
        保存Unity和Android的UI树对比
        
        :param filename_prefix: 文件名前缀
        :param kwargs: UI树参数
        """
        try:
            import time
            timestamp = int(time.time())
            
            # 获取Unity UI树
            try:
                unity_poco = self.get_unity_poco()
                unity_tree = unity_poco.get_ui_tree(format_type='json', **kwargs)
                unity_filename = f"{filename_prefix}_unity_{timestamp}.json"
                with open(unity_filename, 'w', encoding='utf-8') as f:
                    f.write(unity_tree)
                self.logger.info(f"Unity UI树已保存: {unity_filename}")
            except Exception as e:
                self.logger.warning(f"保存Unity UI树失败: {str(e)}")
            
            # 获取Android UI树
            try:
                android_poco = self.get_android_poco()
                android_tree = android_poco.get_ui_tree(format_type='json', **kwargs)
                android_filename = f"{filename_prefix}_android_{timestamp}.json"
                with open(android_filename, 'w', encoding='utf-8') as f:
                    f.write(android_tree)
                self.logger.info(f"Android UI树已保存: {android_filename}")
            except Exception as e:
                self.logger.warning(f"保存Android UI树失败: {str(e)}")
                
        except Exception as e:
            self.logger.error(f"保存UI树对比失败: {str(e)}")


# 便捷函数
def get_poco(device_id: Optional[str] = None, 
            poco_type: str = 'unity', 
            **kwargs) -> Any:
    """
    便捷函数：获取poco实例
    
    :param device_id: 设备ID
    :param poco_type: poco类型 ('unity' 或 'android')
    :param kwargs: poco参数
    :return: poco实例
    """
    return PocoManager.get_poco(device_id=device_id, poco_type=poco_type, **kwargs)


def get_smart_poco(device_id: Optional[str] = None, **kwargs) -> SmartPoco:
    """
    便捷函数：获取智能Poco实例
    
    :param device_id: 设备ID
    :param kwargs: poco参数
    :return: SmartPoco实例
    """
    return SmartPoco(device_id=device_id, **kwargs)


def release_all_poco():
    """便捷函数：释放所有poco实例"""
    PocoManager.release_all() 