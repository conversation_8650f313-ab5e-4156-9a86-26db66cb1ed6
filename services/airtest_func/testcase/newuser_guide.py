from ..models import *
from ...web_logger import log
from services.request_service import RequestService
from services.firebase_service import FirebaseService
param_example= {
    "operation" : "reset",
    "test_param" :{
    'fid' : '0_daaca2075df3db294bd3f54466d18120', #firebase数据库账号id
    'remark': 'pixel9', #备注
    'appName':'tm3d' #项目名称
    }
}

def newuser_guide(device_id, test_data=None):
    log("🎮 开始新手引导测试")

    try:
        # # 获取测试参数
        # if test_data and 'test_params' in test_data:
        #     test_params = test_data['test_params']
        #     log(f"📋 测试参数: {test_params}")
        #
        #     # 使用参数中的信息
        #     if 'firebaseid' in test_params:
        #         log(f"🔑 Firebase ID: {test_params['firebaseid']}")
        #     if 'note' in test_params:
        #         log(f"📝 备注: {test_params['note']}")
        # else:
        #     log("📋 未传入测试参数，使用默认配置")

        log("🏆 初始化新手引导对象")
        newguide = NewGuide(device_id)
        log("🏆第1关")
        newguide.first_level()

        log("🏆第2关")
        newguide.secend_level()

        log("🏆第3关")
        newguide.third_level()

        log("🏆第4关")
        newguide.fourth_level()

        log("🏆第5关")
        newguide.fifth_level()

        log("🏆第6关")
        newguide.sixth_level()

        log("🏆第7关")
        newguide.seventh_level()

        log("🏆第8关")
        newguide.eighth_level()

        log("🏆第9关")
        newguide.ninth_level()

        log("🏆第10关")
        newguide.ten_level()

        log("🏆第11关")
        newguide.eleventh_level()

        log("🏆第12关")
        newguide.twelfth_level()

        log("🏆第13关")
        newguide.thirteenth_level()

        log("🏆第14关")
        newguide.fourteenth_level()

        log("🏆第15关")
        newguide.Fifteenth_level()

        log("🏆第16关")
        newguide.Sixteenth_level()

        log("🏆第17关")
        newguide.Seventeenth_level()

        log("🏆第18关")
        newguide.Eighteenth_level()

        log("🏆第19关")
        newguide.Nineteenth_level()

        log("🏆第20关")
        newguide.Twentieth_level()

        log("🏆第21-30关")
        newguide.lv21_2_31()

        log("✅ 新手引导测试完成")
        return True

    except Exception as e:
        log(f"❌ 新手引导测试失败: {str(e)}", 'error')
        raise e


