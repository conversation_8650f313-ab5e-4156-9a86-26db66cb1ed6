import platform
import subprocess
import os
from .log_common import LogService

class PlatformCompatUtils:
    """平台兼容性工具类，提供跨平台的命令支持"""
    
    def __init__(self):
        self.logger = LogService()
        self.system = platform.system()
        self.is_windows = self.system == 'Windows'
        self.is_macos = self.system == 'Darwin'
        self.is_linux = self.system == 'Linux'
        
    def get_grep_command(self, pattern, target_string=None):
        """
        根据平台返回相应的grep命令
        :param pattern: 要搜索的模式
        :param target_string: 目标字符串（可选）
        :return: 适合当前平台的grep命令
        """
        if self.is_windows:
            # Windows使用findstr
            if target_string:
                return f'findstr "{pattern}" "{target_string}"'
            else:
                return f'findstr "{pattern}"'
        else:
            # macOS/Linux使用grep
            if target_string:
                return f'grep "{pattern}" "{target_string}"'
            else:
                return f'grep "{pattern}"'
    
    def execute_adb_with_filter(self, adb_command, filter_pattern, device_id=None):
        """
        执行ADB命令并过滤结果，跨平台兼容
        :param adb_command: ADB命令（不包含过滤部分）
        :param filter_pattern: 过滤模式
        :param device_id: 设备ID（可选）
        :return: 过滤后的结果
        """
        try:
            # 构建完整的ADB命令
            if device_id:
                if adb_command.startswith('adb '):
                    full_command = adb_command.replace('adb ', f'adb -s {device_id} ', 1)
                else:
                    full_command = f"adb -s {device_id} {adb_command}"
            else:
                full_command = adb_command if adb_command.startswith('adb ') else f"adb {adb_command}"
            
            # 执行ADB命令
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                self.logger.error(f"ADB命令执行失败: {full_command}, 错误: {result.stderr}")
                return None
                
            output = result.stdout.strip()
            if not output:
                return None
                
            # 在Python中过滤结果，避免平台差异
            filtered_lines = []
            for line in output.split('\n'):
                if filter_pattern in line:
                    filtered_lines.append(line)
            
            return '\n'.join(filtered_lines) if filtered_lines else None
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"ADB命令超时: {full_command}")
            return None
        except Exception as e:
            self.logger.error(f"ADB命令执行异常: {full_command}, 错误: {e}")
            return None
    
    def execute_adb_with_filter_silent(self, adb_command, filter_pattern, device_id=None):
        """
        静默执行ADB命令并过滤结果，跨平台兼容，不输出错误日志
        :param adb_command: ADB命令（不包含过滤部分）
        :param filter_pattern: 过滤模式
        :param device_id: 设备ID（可选）
        :return: 过滤后的结果
        """
        try:
            # 构建完整的ADB命令
            if device_id:
                if adb_command.startswith('adb '):
                    full_command = adb_command.replace('adb ', f'adb -s {device_id} ', 1)
                else:
                    full_command = f"adb -s {device_id} {adb_command}"
            else:
                full_command = adb_command if adb_command.startswith('adb ') else f"adb {adb_command}"
            
            # 执行ADB命令
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                return None
                
            output = result.stdout.strip()
            if not output:
                return None
                
            # 在Python中过滤结果，避免平台差异
            filtered_lines = []
            for line in output.split('\n'):
                if filter_pattern in line:
                    filtered_lines.append(line)
            
            return '\n'.join(filtered_lines) if filtered_lines else None
            
        except:
            return None
    
    def count_lines_with_pattern(self, text, pattern):
        """
        计算文本中包含指定模式的行数
        :param text: 源文本
        :param pattern: 匹配模式
        :return: 行数
        """
        if not text:
            return 0
        
        count = 0
        for line in text.split('\n'):
            if pattern in line:
                count += 1
        return count
    
    def extract_version_info(self, text, pattern):
        """
        从文本中提取版本信息，跨平台兼容
        :param text: 源文本
        :param pattern: 匹配模式
        :return: 匹配的行
        """
        if not text:
            return None
        
        for line in text.split('\n'):
            if pattern in line:
                return line.strip()
        return None
    
    def get_first_line(self, text):
        """
        获取文本的第一行，模拟head -1功能
        :param text: 源文本
        :return: 第一行文本
        """
        if not text:
            return None
        
        lines = text.split('\n')
        return lines[0].strip() if lines else None
    
    def get_lines(self, text, count):
        """
        获取文本的前N行，模拟head -n功能
        :param text: 源文本
        :param count: 行数
        :return: 前N行文本
        """
        if not text:
            return None
        
        lines = text.split('\n')
        selected_lines = lines[:count]
        return '\n'.join(selected_lines) if selected_lines else None
    
    def execute_adb_and_get_first_line(self, adb_command, device_id=None):
        """
        执行ADB命令并获取第一行结果，跨平台兼容
        :param adb_command: ADB命令
        :param device_id: 设备ID（可选）
        :return: 第一行结果
        """
        try:
            # 构建完整的ADB命令
            if device_id:
                if adb_command.startswith('adb '):
                    full_command = adb_command.replace('adb ', f'adb -s {device_id} ', 1)
                else:
                    full_command = f"adb -s {device_id} {adb_command}"
            else:
                full_command = adb_command if adb_command.startswith('adb ') else f"adb {adb_command}"
            
            # 执行ADB命令
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                self.logger.error(f"ADB命令执行失败: {full_command}, 错误: {result.stderr}")
                return None
                
            output = result.stdout.strip()
            if not output:
                return None
                
            # 返回第一行
            return self.get_first_line(output)
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"ADB命令超时: {full_command}")
            return None
        except Exception as e:
            self.logger.error(f"ADB命令执行异常: {full_command}, 错误: {e}")
            return None
    
    def get_platform_info(self):
        """
        获取平台信息
        :return: 平台信息字典
        """
        return {
            'system': self.system,
            'is_windows': self.is_windows,
            'is_macos': self.is_macos,
            'is_linux': self.is_linux,
            'platform': platform.platform(),
            'machine': platform.machine(),
            'processor': platform.processor()
        } 