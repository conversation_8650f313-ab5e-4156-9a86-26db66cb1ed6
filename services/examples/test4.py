from services.airtest_func.PocoManager import PocoManager


device = '45170DLAQ001LL'
pocounity = PocoManager.get_device_poco(device_id=device, poco_type='unity')

# print(pocounity("SR_OptionsContent","SR_Content"))



# 通过PocoManager获取原生poco
native_poco = PocoManager.get_native_poco(device_id="45170DLAQ001LL", poco_type='unity')
# native_poco("SR_Canvas").offspring("SR_Content").child("Category(Clone)")[1].child("NumberOption(Clone)")[0].child("SR_Title").click()

father = native_poco("ViewRoot").offspring("Content")[1]

for i in father:
    print(i)