"""
PocoManager 使用示例
===================

这个文件展示了如何使用PocoManager来优化poco实例管理，提高性能并保持灵活性。
"""

from services.airtest_func.PocoManager import PocoManager, SmartPoco, get_poco, get_smart_poco, release_all_poco


# ===============================
# 基本使用方式
# ===============================

def example_basic_usage():
    """基本使用示例"""
    device_id = "your_device_id"
    
    # 1. 获取Unity Poco实例（第一次会创建，之后会复用）
    unity_poco = PocoManager.get_poco(device_id=device_id, poco_type='unity')
    unity_poco.click("StartButton")
    
    # 2. 获取Android Poco实例
    android_poco = PocoManager.get_poco(device_id=device_id, poco_type='android')
    android_poco.click("android.widget.Button", "确定")
    
    # 3. 再次获取Unity Poco（复用之前的实例，不会重新初始化）
    unity_poco2 = PocoManager.get_poco(device_id=device_id, poco_type='unity')
    unity_poco2.wait_and_click("NextButton")  # 使用新封装的wait_and_click方法
    
    # 4. 使用便捷函数
    poco = get_poco(device_id=device_id, poco_type='unity')
    poco.click("MenuButton")


def example_testcase_usage(device_id):
    """测试用例中的使用示例"""
    # 在测试用例开始时获取poco实例
    poco = get_poco(device_id=device_id, poco_type='unity')
    
    # 执行游戏操作
    poco.wait_and_click("StartGameButton", timeout=30)
    poco.click("Level1Button")
    
    # 需要处理系统弹窗时，获取Android poco
    android_poco = get_poco(device_id=device_id, poco_type='android')
    android_poco.handle_privacy_dialogs("com.your.game.package")
    
    # 继续游戏操作（复用Unity poco实例）
    poco.click("PlayButton")
    poco.wait_and_click("EndGameButton", timeout=60)
    
    # 测试结束时可选择释放资源（也可以不释放，留给下次测试复用）
    # PocoManager.release_poco(device_id=device_id)


# ===============================
# 智能Poco使用方式
# ===============================

def example_smart_poco_usage():
    """智能Poco使用示例"""
    device_id = "your_device_id"
    
    # 创建智能Poco实例
    smart_poco = get_smart_poco(device_id=device_id)
    
    # 游戏操作（自动使用Unity poco）
    smart_poco.click("StartButton", operation_type='game')
    smart_poco.wait_and_click("PlayButton", operation_type='game')
    
    # 隐私弹窗处理（自动使用Android poco）
    smart_poco.click("android.widget.Button", "允许", operation_type='privacy')
    
    # 购买操作（自动使用Android poco）
    smart_poco.click("purchase_button", operation_type='purchase')
    
    # 专门的隐私处理方法
    smart_poco.handle_privacy_dialogs("com.your.game.package")
    
    # 关闭时释放该设备的所有poco实例
    smart_poco.close()


# ===============================
# 高级使用方式
# ===============================

def example_advanced_usage():
    """高级使用示例"""
    device_id = "your_device_id"
    
    # 1. 强制重新创建实例（用于poco连接异常时）
    poco = PocoManager.get_poco(
        device_id=device_id, 
        poco_type='unity', 
        force_recreate=True,
        timeout=15,
        screenshot_each_action=True
    )
    
    # 2. 自动选择poco类型
    game_poco = PocoManager.auto_switch_poco(device_id, operation_type='game')
    system_poco = PocoManager.auto_switch_poco(device_id, operation_type='system')
    
    # 3. 查看当前活跃的实例
    active_instances = PocoManager.get_active_instances()
    print(f"当前活跃实例: {active_instances}")
    
    # 4. 释放特定类型的实例
    PocoManager.release_poco(device_id=device_id, poco_type='android')
    
    # 5. 释放特定设备的所有实例
    PocoManager.release_poco(device_id=device_id)
    
    # 6. 释放所有实例
    release_all_poco()


# ===============================
# 在现有代码中的迁移示例
# ===============================

def old_way_example(device_id):
    """旧的使用方式（性能差，每次都重新初始化）"""
    from .poco_unity import PocoUnity
    from .poco_android import pocoAndroid
    
    # 每次都重新创建实例
    unity_poco = PocoUnity(device_id=device_id)
    unity_poco.click("StartButton")
    unity_poco.close()  # 关闭连接
    
    # 又要重新创建
    android_poco = pocoAndroid(device_id=device_id)
    android_poco.click("android.widget.Button", "确定")
    android_poco.close()  # 关闭连接
    
    # 再次需要unity poco时又要重新创建
    unity_poco2 = PocoUnity(device_id=device_id)
    unity_poco2.click("NextButton")
    unity_poco2.close()


def new_way_example(device_id):
    """新的使用方式（高性能，复用实例）"""
    # 获取实例（第一次创建，后续复用）
    unity_poco = get_poco(device_id=device_id, poco_type='unity')
    unity_poco.click("StartButton")
    # 不需要手动关闭，由PocoManager管理
    
    # 获取Android poco实例
    android_poco = get_poco(device_id=device_id, poco_type='android')
    android_poco.click("android.widget.Button", "确定")
    
    # 再次获取unity poco（复用之前的实例）
    unity_poco2 = get_poco(device_id=device_id, poco_type='unity')
    unity_poco2.wait_and_click("NextButton")  # 使用新的wait_and_click方法
    
    # 测试结束时可选择释放（也可以留给下次测试复用）
    # PocoManager.release_poco(device_id=device_id)


# ===============================
# 实际业务场景示例
# ===============================

def game_test_scenario(device_id):
    """完整的游戏测试场景"""
    print("开始游戏测试...")
    
    # 使用智能Poco，自动处理不同类型的操作
    smart_poco = get_smart_poco(device_id=device_id)
    
    try:
        # 1. 启动游戏
        smart_poco.wait_and_click("StartGameButton", operation_type='game', timeout=30)
        
        # 2. 处理可能的隐私弹窗
        smart_poco.handle_privacy_dialogs("com.your.game.package")
        
        # 3. 选择关卡
        smart_poco.click("Level1Button", operation_type='game')
        
        # 4. 开始游戏
        smart_poco.wait_and_click("PlayButton", operation_type='game', timeout=10)
        
        # 5. 游戏过程中可能的购买操作
        if smart_poco.click("PurchaseButton", operation_type='game'):
            # 处理Google Play购买弹窗
            smart_poco.click("android.widget.Button", "购买", operation_type='purchase')
        
        # 6. 结束游戏
        smart_poco.wait_and_click("EndGameButton", operation_type='game', timeout=60)
        
        print("游戏测试完成")
        
    except Exception as e:
        print(f"测试异常: {e}")
        # 异常时可以强制重新创建poco实例
        unity_poco = PocoManager.get_poco(device_id=device_id, poco_type='unity', force_recreate=True)
        
    finally:
        # 可选：释放资源（如果确定不再使用）
        smart_poco.close()


def batch_device_test():
    """多设备批量测试示例"""
    device_list = ["device1", "device2", "device3"]
    
    try:
        for device_id in device_list:
            print(f"测试设备: {device_id}")
            
            # 每个设备都有独立的poco实例
            poco = get_poco(device_id=device_id, poco_type='unity')
            poco.click("StartButton")
            poco.wait_and_click("TestButton", timeout=20)
            
            # 查看当前所有活跃实例
            print(f"活跃实例: {PocoManager.get_active_instances()}")
    
    finally:
        # 测试结束后释放所有实例
        release_all_poco()
        print("已释放所有poco实例")


# ===============================
# 性能对比示例
# ===============================

def performance_comparison():
    """性能对比示例"""
    import time
    
    device_id = "test_device"
    
    # 测试旧方式（每次重新初始化）
    print("测试旧方式...")
    start_time = time.time()
    
    for i in range(5):
        from .poco_unity import PocoUnity
        poco = PocoUnity(device_id=device_id)
        # 模拟操作
        poco.close()
    
    old_way_time = time.time() - start_time
    print(f"旧方式耗时: {old_way_time:.2f}秒")
    
    # 测试新方式（复用实例）
    print("测试新方式...")
    start_time = time.time()
    
    for i in range(5):
        poco = get_poco(device_id=device_id, poco_type='unity')
        # 模拟操作（复用同一个实例）
    
    new_way_time = time.time() - start_time
    print(f"新方式耗时: {new_way_time:.2f}秒")
    print(f"性能提升: {old_way_time/new_way_time:.1f}倍")
    
    # 清理
    release_all_poco()


if __name__ == "__main__":
    # 运行示例
    device_id = "your_device_id"
    
    print("=== 基本使用示例 ===")
    example_basic_usage()
    
    print("\n=== 智能Poco示例 ===")
    example_smart_poco_usage()
    
    print("\n=== 完整测试场景 ===")
    game_test_scenario(device_id) 