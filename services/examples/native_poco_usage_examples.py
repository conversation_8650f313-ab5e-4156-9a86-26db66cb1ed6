"""
原生Poco使用示例
演示如何获取和使用原生的poco对象，支持链式调用和复杂的元素定位
"""

from services.airtest_func.PocoManager import PocoManager, SmartPoco
from services.airtest_func.poco_unity import PocoUnity
from services.airtest_func.poco_android import pocoAndroid


def example_unity_native_poco():
    """Unity原生poco使用示例"""
    print("=== Unity原生Poco使用示例 ===")
    
    device_id = "your_device_id"
    
    # 方法1: 通过PocoManager获取原生Unity poco
    native_poco = PocoManager.get_native_poco(device_id=device_id, poco_type='unity')
    
    # 使用原生poco的链式调用方法
    try:
        # 复杂的链式调用示例
        native_poco("SR_Canvas").offspring("SR_Content").child("Category(Clone)")[1].child("NumberOption(Clone)")[0].child("SR_Title").click()
        print("✓ 复杂链式调用成功")
        
        # 使用text属性查找元素
        native_poco(text="卡包Id").click()
        print("✓ 文本查找点击成功")
        
        # 使用name属性查找元素
        native_poco(name="StartButton").click()
        print("✓ 名称查找点击成功")
        
        # 获取元素属性
        element = native_poco("PlayerName")
        if element.exists():
            player_name = element.get_text()
            print(f"✓ 获取玩家名称: {player_name}")
        
        # 使用索引访问同名元素
        buttons = native_poco("Button")
        if len(buttons) > 0:
            buttons[0].click()  # 点击第一个Button
            print("✓ 索引访问元素成功")
        
    except Exception as e:
        print(f"✗ Unity原生poco操作失败: {str(e)}")
    
    # 方法2: 通过实例获取原生poco
    unity_poco = PocoManager.get_poco(device_id=device_id, poco_type='unity')
    native_poco2 = unity_poco.get_native_poco()
    
    try:
        # 使用原生poco的高级功能
        screen_size = native_poco2.get_screen_size()
        print(f"✓ 获取屏幕尺寸: {screen_size}")
        
        # 等待元素出现
        native_poco2("LoadingPanel").wait_for_appearance(timeout=10)
        print("✓ 等待元素出现成功")
        
    except Exception as e:
        print(f"✗ Unity原生poco高级操作失败: {str(e)}")


def example_android_native_poco():
    """Android原生poco使用示例"""
    print("\n=== Android原生Poco使用示例 ===")
    
    device_id = "your_device_id"
    
    # 方法1: 通过PocoManager获取原生Android poco
    native_poco = PocoManager.get_native_poco(device_id=device_id, poco_type='android')
    
    try:
        # Android原生poco的链式调用
        native_poco("android.widget.FrameLayout").offspring("android.widget.Button")[0].click()
        print("✓ Android链式调用成功")
        
        # 使用text属性查找Android元素
        native_poco(text="确定").click()
        print("✓ Android文本查找成功")
        
        # 使用resourceId查找元素
        native_poco(resourceId="com.example.app:id/button").click()
        print("✓ Android resourceId查找成功")
        
        # 使用className查找元素
        native_poco(className="android.widget.EditText").set_text("测试文本")
        print("✓ Android输入文本成功")
        
    except Exception as e:
        print(f"✗ Android原生poco操作失败: {str(e)}")
    
    # 方法2: 通过实例获取原生poco
    android_poco = PocoManager.get_poco(device_id=device_id, poco_type='android')
    native_poco2 = android_poco.get_native_poco()
    
    try:
        # Android原生poco的高级功能
        hierarchy = native_poco2.agent.hierarchy.dump()
        print("✓ 获取Android UI层次结构成功")
        
        # 滑动操作
        native_poco2.swipe([0.5, 0.8], [0.5, 0.2])
        print("✓ Android滑动操作成功")
        
    except Exception as e:
        print(f"✗ Android原生poco高级操作失败: {str(e)}")


def example_smart_poco_native():
    """SmartPoco原生poco使用示例"""
    print("\n=== SmartPoco原生Poco使用示例 ===")
    
    device_id = "your_device_id"
    smart_poco = SmartPoco(device_id=device_id)
    
    try:
        # 根据操作类型自动获取原生poco
        
        # 游戏操作 - 自动使用Unity原生poco
        game_native_poco = smart_poco.get_native_poco('game')
        game_native_poco("StartButton").click()
        print("✓ 游戏操作使用Unity原生poco成功")
        
        # 系统操作 - 自动使用Android原生poco
        system_native_poco = smart_poco.get_native_poco('system')
        system_native_poco(text="允许").click()
        print("✓ 系统操作使用Android原生poco成功")
        
        # 隐私弹窗处理 - 自动使用Android原生poco
        privacy_native_poco = smart_poco.get_native_poco('privacy')
        privacy_native_poco("android.widget.Button", text="同意").click()
        print("✓ 隐私弹窗使用Android原生poco成功")
        
        # 直接获取特定类型的原生poco
        unity_native = smart_poco.get_native_unity_poco()
        android_native = smart_poco.get_native_android_poco()
        
        print("✓ SmartPoco原生poco获取成功")
        
    except Exception as e:
        print(f"✗ SmartPoco原生poco操作失败: {str(e)}")


def example_complex_native_operations():
    """复杂原生poco操作示例"""
    print("\n=== 复杂原生Poco操作示例 ===")
    
    device_id = "your_device_id"
    
    try:
        # 获取Unity原生poco
        unity_native = PocoManager.get_native_poco(device_id=device_id, poco_type='unity')
        
        # 复杂的游戏UI操作示例
        print("执行复杂游戏UI操作...")
        
        # 1. 导航到特定界面
        unity_native("MainMenu").child("SettingsButton").click()
        
        # 2. 在设置界面中进行复杂操作
        settings_panel = unity_native("SettingsPanel")
        if settings_panel.exists():
            # 找到音效设置滑块并调整
            audio_slider = settings_panel.offspring("AudioSettings").child("VolumeSlider")
            if audio_slider.exists():
                audio_slider.drag_to([0.8, 0.5])  # 拖拽到80%位置
                print("✓ 音效滑块调整成功")
        
        # 3. 处理多层嵌套的UI元素
        inventory_items = unity_native("InventoryPanel").offspring("ItemGrid").children("Item")
        for i, item in enumerate(inventory_items):
            if item.attr("rarity") == "legendary":  # 查找传说级物品
                item.click()
                print(f"✓ 点击传说级物品 {i}")
                break
        
        # 4. 使用原生poco的高级查询功能
        all_buttons = unity_native("*").offspring().filter(lambda node: node.attr("type") == "Button")
        print(f"✓ 找到 {len(all_buttons)} 个按钮")
        
        # 获取Android原生poco进行系统级操作
        android_native = PocoManager.get_native_poco(device_id=device_id, poco_type='android')
        
        # 5. 处理系统弹窗
        if android_native(text="允许").exists():
            android_native(text="允许").click()
            print("✓ 处理系统权限弹窗")
        
        # 6. 复杂的Android UI导航
        android_native("android.widget.FrameLayout").offspring(
            "android.widget.LinearLayout"
        ).child("android.widget.Button")[2].click()
        print("✓ 复杂Android UI导航成功")
        
    except Exception as e:
        print(f"✗ 复杂原生poco操作失败: {str(e)}")


def example_native_poco_comparison():
    """原生poco与封装poco的对比示例"""
    print("\n=== 原生Poco与封装Poco对比 ===")
    
    device_id = "your_device_id"
    
    # 封装poco的使用方式
    print("封装poco使用方式:")
    unity_poco = PocoManager.get_poco(device_id=device_id, poco_type='unity')
    try:
        unity_poco.click("StartButton")  # 使用封装的click方法
        print("✓ 封装poco点击成功")
    except Exception as e:
        print(f"✗ 封装poco操作失败: {str(e)}")
    
    # 原生poco的使用方式
    print("\n原生poco使用方式:")
    native_poco = unity_poco.get_native_poco()
    try:
        native_poco("StartButton").click()  # 使用原生poco的方法
        print("✓ 原生poco点击成功")
        
        # 原生poco支持更多高级功能
        element = native_poco("PlayerLevel")
        if element.exists():
            # 获取元素的所有属性
            attrs = element.attr()
            print(f"✓ 元素属性: {attrs}")
            
            # 获取元素的边界框
            bbox = element.get_bounds()
            print(f"✓ 元素边界: {bbox}")
            
            # 获取元素在屏幕上的位置
            pos = element.get_position()
            print(f"✓ 元素位置: {pos}")
        
    except Exception as e:
        print(f"✗ 原生poco操作失败: {str(e)}")


if __name__ == "__main__":
    print("原生Poco使用示例")
    print("=" * 50)
    
    # 运行所有示例
    example_unity_native_poco()
    example_android_native_poco()
    example_smart_poco_native()
    example_complex_native_operations()
    example_native_poco_comparison()
    
    print("\n" + "=" * 50)
    print("示例运行完成")
