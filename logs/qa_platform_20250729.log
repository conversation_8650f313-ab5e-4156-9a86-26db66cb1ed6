2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 找到提交: 4497a976 - Merge remote-tracking branch 'origin/feature/theme_26' into qa_test_3.29.0
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 找到提交: a7289e76 - 需求 【无尽礼包】付费节点之间免费项未领取完时，主界面icon文字显示Claim
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 找到 4 个相关提交
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 最终找到 4 个Excel文件变更
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\DartRivalsProgress.html
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:08:59 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:09:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:09:00 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\[调用]多语言.html (大小: 36495 bytes)
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\[调用]多语言.html
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:09:05 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:09:08 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:09:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:09:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\MatchModelV2.html
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\DartRivalsProgressRewards.html
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:09:16 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:09:16 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753754897_c6794a0d
2025-07-29 10:10:08 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:10:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:10:16 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:10:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:14:16 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753754897_c6794a0d
2025-07-29 10:15:47 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:15:51 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:17:26 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:00 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:04 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:17 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:27 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:19:09 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 找到提交: 4497a976 - Merge remote-tracking branch 'origin/feature/theme_26' into qa_test_3.29.0
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 找到提交: a7289e76 - 需求 【无尽礼包】付费节点之间免费项未领取完时，主界面icon文字显示Claim
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 找到 4 个相关提交
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 最终找到 4 个Excel文件变更
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\DartRivalsProgress.html
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:19:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:19:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:19:29 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\[调用]多语言.html (大小: 36495 bytes)
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\[调用]多语言.html
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:19:36 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:19:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:19:41 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\MatchModelV2.html
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\DartRivalsProgressRewards.html
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753755564_8511cf7f
2025-07-29 10:23:40 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:24:43 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753755564_8511cf7f
2025-07-29 10:31:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 找到提交: 4497a976 - Merge remote-tracking branch 'origin/feature/theme_26' into qa_test_3.29.0
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 找到提交: a7289e76 - 需求 【无尽礼包】付费节点之间免费项未领取完时，主界面icon文字显示Claim
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 找到 4 个相关提交
2025-07-29 10:31:27 - SimpleConfigCompare-TM3D - INFO - 最终找到 4 个Excel文件变更
2025-07-29 10:31:27 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:31:36 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:31:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:31:45 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\MatchModelV2.html
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\DartRivalsProgress.html
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:31:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:31:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:31:50 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\[调用]多语言.html (大小: 36495 bytes)
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\[调用]多语言.html
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\DartRivalsProgressRewards.html
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753756285_cb946962
2025-07-29 10:31:58 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:32:04 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:32:08 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:32:11 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: 4497a976 - Merge remote-tracking branch 'origin/feature/theme_26' into qa_test_3.29.0
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: a7289e76 - 需求 【无尽礼包】付费节点之间免费项未领取完时，主界面icon文字显示Claim
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 找到 4 个相关提交
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 最终找到 4 个Excel文件变更
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:40:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:40:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:40:23 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\DartRivalsProgressRewards.html
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\DartRivalsProgress.html
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:40:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\[调用]多语言.html (大小: 36495 bytes)
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\[调用]多语言.html
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:40:38 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 10:40:38 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:40:48 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 10:40:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:40:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\MatchModelV2.html
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753756821_81b79d8a
2025-07-29 10:40:55 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:40:58 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:03 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:20 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:31 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:35 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:40 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:43:11 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:45:49 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753756821_81b79d8a
2025-07-29 10:47:19 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:23 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:28 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:31 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:32 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:47:32 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:47:32 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:47:32 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:47:46 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:46 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:47:46 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:47:46 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:47:47 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:49:09 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:49:10 - SimpleConfigCompare-TM3D - ERROR - 配置加载失败: 'SimpleConfigCompareService' object has no attribute '_test_bcompare_installation'
2025-07-29 10:49:18 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:49:18 - SimpleConfigCompare-TM3D - ERROR - 配置加载失败: 'SimpleConfigCompareService' object has no attribute '_test_bcompare_installation'
2025-07-29 10:49:45 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:49:46 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:50:44 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:50:45 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:50:46 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 找到提交: 5e948de9 - fixbug 【card系统】获得勋章时点击物理返回,退出弹窗在勋章下层级
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 找到 9 个相关提交
2025-07-29 10:52:01 - SimpleConfigCompare-TM3D - INFO - 最终找到 12 个Excel文件变更
2025-07-29 10:52:01 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:52:10 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 10:52:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:52:18 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 10:52:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:52:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\MatchModelV2.html
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 进度: 44% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\DartRivalsProgressRewards.html
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:52:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:52:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:52:24 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\[调用]多语言.html (大小: 1465044 bytes)
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\[调用]多语言.html
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - 进度: 60% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:52:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:52:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:52:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\DartRivalsProgress.html
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - INFO - 进度: 69% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: CardBadge.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\CardBadge.html (大小: 3863 bytes)
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\CardBadge.html
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: Badge.xlsx
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\Badge.html (大小: 7178 bytes)
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\Badge.html
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - 进度: 85% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753757459_4cded443
2025-07-29 10:53:06 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:08 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:10 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:15 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:18 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:22 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:28 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:31 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:55:35 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:55:35 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:19:34 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:19:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:19:37 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:19:38 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:19:49 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:20:48 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:20:48 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:20:48 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:20:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:20:49 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\MainInterfaceShow.html
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:20:52 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:20:54 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:20:54 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\[调用]多语言.html (大小: 1465044 bytes)
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\[调用]多语言.html
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: WindowsView.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\WindowsView.html
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: Badge.xlsx
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Badge.html (大小: 7178 bytes)
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Badge.html
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: CardBadge.xlsx
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\CardBadge.html
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:20:59 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:20:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:20:59 - SimpleConfigCompare-TM3D - INFO - 执行对比: Toy Match 3D_埋点方案
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Toy Match 3D_埋点方案.html
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - ERROR - 简化脚本文件不存在: D:\git\qa_tools\config\compare_folder_simple.txt
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\DartRivalsProgress.html
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:21:09 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:21:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:21:15 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:21:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:21:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\MatchModelV2.html
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:21:17 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:17 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:21:17 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: MatchModel.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: Purchase.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Purchase.html (大小: 8082 bytes)
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Purchase.html
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: 游戏常量.xlsx
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\游戏常量.html
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\DartRivalsProgressRewards.html
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:21:24 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:21:24 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:21:24 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753759188_040e6060
2025-07-29 11:21:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:21:43 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:21:47 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:22:19 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:22:20 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:23:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:23:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:24:32 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:32:05 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:32:06 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:32:07 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:33:08 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:33:08 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:33:16 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:33:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:33:23 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:33:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:33:23 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\MatchModelV2.html
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\DartRivalsProgress.html
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: WindowsView.xlsx
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\WindowsView.html
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Badge.xlsx
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Badge.html (大小: 7178 bytes)
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Badge.html
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:33:30 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:33:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:33:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: Toy Match 3D_埋点方案
2025-07-29 11:33:31 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:33:31 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Toy Match 3D_埋点方案.html
2025-07-29 11:33:31 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败，尝试简化脚本对比: Toy Match 3D_埋点方案
2025-07-29 11:33:31 - SimpleConfigCompare-TM3D - INFO - 尝试使用简化脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:33:32 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:33:32 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Toy Match 3D_埋点方案.html
2025-07-29 11:33:32 - SimpleConfigCompare-TM3D - WARNING - 简化脚本也失败，尝试安全脚本: Toy_Match_3D_埋点方案
2025-07-29 11:33:32 - SimpleConfigCompare-TM3D - INFO - 尝试使用安全脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 107: 脚本加载文件夹或文件失败
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Toy Match 3D_埋点方案.html
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - ERROR - 安全脚本对比也失败，返回码: 107
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\MainInterfaceShow.html
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModel.xlsx
2025-07-29 11:33:38 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:33:38 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:33:38 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:33:38 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: Purchase.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Purchase.html (大小: 8082 bytes)
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Purchase.html
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: CardBadge.xlsx
2025-07-29 11:33:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:33:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:33:41 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\CardBadge.html
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\DartRivalsProgressRewards.html
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:33:45 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:33:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:33:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\[调用]多语言.html (大小: 1465042 bytes)
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\[调用]多语言.html
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: 游戏常量.xlsx
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\游戏常量.html
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753759929_84a007a0
2025-07-29 11:39:47 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:39:47 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:39:47 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\DartRivalsProgressRewards.html
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:40:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:40:51 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:40:51 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\[调用]多语言.html (大小: 1465042 bytes)
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\[调用]多语言.html
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:40:59 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:40:59 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:41:05 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:41:05 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:41:05 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\MatchModelV2.html
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: 游戏常量.xlsx
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\游戏常量.html
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - INFO - 执行对比: Toy Match 3D_埋点方案
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Toy Match 3D_埋点方案.html
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试简化脚本对比: Toy Match 3D_埋点方案
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - INFO - 尝试使用简化脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:41:10 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:41:10 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Toy Match 3D_埋点方案.html
2025-07-29 11:41:10 - SimpleConfigCompare-TM3D - WARNING - 简化脚本也失败，尝试安全脚本: Toy_Match_3D_埋点方案
2025-07-29 11:41:10 - SimpleConfigCompare-TM3D - INFO - 尝试使用安全脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 107: 脚本加载文件夹或文件失败
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Toy Match 3D_埋点方案.html
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - ERROR - 安全脚本对比也失败，返回码: 107
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - ERROR - 简化脚本也失败: Toy Match 3D_埋点方案
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: CardBadge.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\CardBadge.html
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\MainInterfaceShow.html
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: MatchModel.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: Purchase.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Purchase.html (大小: 8082 bytes)
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Purchase.html
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Badge.xlsx
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Badge.html (大小: 7178 bytes)
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Badge.html
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\DartRivalsProgress.html
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: WindowsView.xlsx
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\WindowsView.html
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:41:23 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753760387_ac5540c4
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:43:02 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:43:02 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: 游戏常量.xlsx
2025-07-29 11:43:02 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\游戏常量.html
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: Badge.xlsx
2025-07-29 11:43:04 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:43:04 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:43:04 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Badge.html (大小: 7178 bytes)
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Badge.html
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:43:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:43:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:43:07 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\[调用]多语言.html (大小: 1465042 bytes)
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\[调用]多语言.html
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Purchase.xlsx
2025-07-29 11:43:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:43:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:43:09 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Purchase.html (大小: 8082 bytes)
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Purchase.html
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 执行对比: Toy Match 3D_埋点方案
2025-07-29 11:43:11 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:43:11 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Toy Match 3D_埋点方案.html
2025-07-29 11:43:11 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试简化脚本对比: Toy Match 3D_埋点方案
2025-07-29 11:43:11 - SimpleConfigCompare-TM3D - INFO - 尝试使用简化脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:43:12 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:43:12 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Toy Match 3D_埋点方案.html
2025-07-29 11:43:12 - SimpleConfigCompare-TM3D - WARNING - 简化脚本也失败，尝试安全脚本: Toy_Match_3D_埋点方案
2025-07-29 11:43:12 - SimpleConfigCompare-TM3D - INFO - 尝试使用安全脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 107: 脚本加载文件夹或文件失败
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Toy Match 3D_埋点方案.html
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - ERROR - 安全脚本对比也失败，返回码: 107
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - ERROR - 简化脚本也失败: Toy Match 3D_埋点方案
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\MainInterfaceShow.html
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:43:15 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:15 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:43:15 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:43:21 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:43:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:43:25 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:43:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:43:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\MatchModelV2.html
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\DartRivalsProgress.html
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: MatchModel.xlsx
2025-07-29 11:43:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:43:29 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:43:29 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:43:29 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\DartRivalsProgressRewards.html
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: CardBadge.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\CardBadge.html
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: WindowsView.xlsx
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\WindowsView.html
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:43:35 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:43:35 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753760519_f3b44403
2025-07-29 11:47:34 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:53:10 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:53:11 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:53:11 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:54:46 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:54:46 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:54:46 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:55:54 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:55:54 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModel.xlsx
2025-07-29 11:55:57 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\DartRivalsProgressRewards.html
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:56:01 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:56:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:56:02 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 11:56:02 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\调用_多语言_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\调用_多语言_new
2025-07-29 11:56:02 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\[调用]多语言.html (大小: 1464972 bytes)
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\[调用]多语言.html
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\DartRivalsProgress.html
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Purchase.xlsx
2025-07-29 11:56:05 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:56:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:56:06 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Purchase.html (大小: 8082 bytes)
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Purchase.html
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_埋点方案_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_埋点方案_new
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Toy Match 3D_埋点方案.html
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: Badge.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Badge.html (大小: 7178 bytes)
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Badge.html
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: WindowsView.xlsx
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\WindowsView.html
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: 游戏常量.xlsx
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\游戏常量.html
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: CardBadge.xlsx
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\CardBadge.html
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:56:16 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:16 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:56:16 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:56:22 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:56:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:56:27 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:56:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:56:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\MatchModelV2.html
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\MainInterfaceShow.html
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753761286_a5dcf333
2025-07-29 11:57:14 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:57:21 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:57:21 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:57:30 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:57:30 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:57:30 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:57:30 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:57:49 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:57:51 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: WindowsView.xlsx
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\WindowsView.html
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: Purchase.xlsx
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Purchase.html (大小: 8082 bytes)
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Purchase.html
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:58:34 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:58:34 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:58:34 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\MainInterfaceShow.html
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\DartRivalsProgress.html
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:58:38 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:58:38 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 11:58:38 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_埋点方案_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_埋点方案_new
2025-07-29 11:58:38 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Toy Match 3D_埋点方案.html
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: Badge.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Badge.html (大小: 7178 bytes)
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Badge.html
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:58:47 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:58:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:58:54 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:58:54 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:58:54 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\MatchModelV2.html
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: CardBadge.xlsx
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\CardBadge.html
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:58:57 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:58:57 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:58:58 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:58:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:58:59 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 11:58:59 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\调用_多语言_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\调用_多语言_new
2025-07-29 11:58:59 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\[调用]多语言.html (大小: 1464972 bytes)
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\[调用]多语言.html
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\DartRivalsProgressRewards.html
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: 游戏常量.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\游戏常量.html
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: MatchModel.xlsx
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753761449_936adf2e
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:59:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:06:29 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:06:29 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:06:30 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:06:41 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:06:41 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 12:06:41 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: Purchase.xlsx
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Purchase.html (大小: 8082 bytes)
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Purchase.html
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: MatchModelV2.xlsx
2025-07-29 12:08:13 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 12:08:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:08:18 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 12:08:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:08:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\MatchModelV2.html
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: CardBadge.xlsx
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\CardBadge.html (大小: 3863 bytes)
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\CardBadge.html
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: WindowsView.xlsx
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\WindowsView.html (大小: 24164 bytes)
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\WindowsView.html
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: 游戏常量.xlsx
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\游戏常量.html (大小: 4481 bytes)
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\游戏常量.html
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: MatchModel.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 12:08:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:08:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:08:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\DartRivalsProgress.html
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: guanqia.xlsx
2025-07-29 12:08:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:08:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:08:29 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\guanqia.html (大小: 10374 bytes)
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\guanqia.html
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Badge.xlsx
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Badge.html (大小: 7178 bytes)
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Badge.html
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\MainInterfaceShow.html
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 12:08:33 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 12:08:33 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 12:08:33 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_new
2025-07-29 12:08:33 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 12:08:34 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 12:08:34 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Toy Match 3D_埋点方案.html
2025-07-29 12:08:34 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 12:08:34 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: [调用]多语言.xlsx
2025-07-29 12:08:35 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:08:36 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:08:36 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 12:08:36 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_477c04f0_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_477c04f0_new
2025-07-29 12:08:36 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\[调用]多语言.html (大小: 1464966 bytes)
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\[调用]多语言.html
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\DartRivalsProgressRewards.html
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753762001_c91f7d92
2025-07-29 12:13:38 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753762001_c91f7d92
2025-07-29 12:16:53 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:17:12 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:17:19 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:17:29 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:17:45 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:18:01 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 12:28:17 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:28:19 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:28:19 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:28:20 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 12:29:43 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 12:29:43 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: WindowsView.xlsx
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\WindowsView.html (大小: 24164 bytes)
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\WindowsView.html
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Toy Match 3D_埋点方案.html
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: Purchase.xlsx
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Purchase.html (大小: 8082 bytes)
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Purchase.html
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\MainInterfaceShow.html
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\DartRivalsProgress.html
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: guanqia.xlsx
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\guanqia.html (大小: 10374 bytes)
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\guanqia.html
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: 游戏常量.xlsx
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\游戏常量.html (大小: 4481 bytes)
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\游戏常量.html
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\DartRivalsProgressRewards.html
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: CardBadge.xlsx
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\CardBadge.html (大小: 3863 bytes)
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\CardBadge.html
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: MatchModel.xlsx
2025-07-29 12:29:58 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 12:29:58 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 12:29:58 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 12:29:58 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: [调用]多语言.xlsx
2025-07-29 12:30:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:30:01 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:30:01 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 12:30:01 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 12:30:01 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\[调用]多语言.html
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: MatchModelV2.xlsx
2025-07-29 12:30:08 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 12:30:08 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:30:13 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 12:30:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:30:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\MatchModelV2.html
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: Badge.xlsx
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Badge.html (大小: 7178 bytes)
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Badge.html
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753763305_9db10b11
2025-07-29 12:35:17 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753763305_9db10b11
2025-07-29 13:30:54 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:33:34 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:34:26 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:37:50 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:37:50 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:37:50 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\DartRivalsProgress.html
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: WindowsView.xlsx
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\WindowsView.html (大小: 24164 bytes)
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\WindowsView.html
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 13:39:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 13:39:11 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: [调用]多语言.xlsx
2025-07-29 13:39:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:39:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:39:13 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 13:39:13 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 13:39:13 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\[调用]多语言.html
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: MatchModel.xlsx
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: guanqia.xlsx
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:39:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:39:17 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\guanqia.html (大小: 10374 bytes)
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\guanqia.html
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:39:19 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:39:19 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\DartRivalsProgressRewards.html
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: Purchase.xlsx
2025-07-29 13:39:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:39:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:39:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Purchase.html (大小: 8082 bytes)
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Purchase.html
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: CardBadge.xlsx
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\CardBadge.html (大小: 3863 bytes)
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\CardBadge.html
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\MainInterfaceShow.html
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Toy Match 3D_埋点方案.html
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: 游戏常量.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\游戏常量.html (大小: 4481 bytes)
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\游戏常量.html
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: MatchModelV2.xlsx
2025-07-29 13:39:32 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:39:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:39:38 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:39:38 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:39:38 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\MatchModelV2.html
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: Badge.xlsx
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Badge.html (大小: 7178 bytes)
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Badge.html
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753767474_437adf1c
2025-07-29 13:39:50 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:40:46 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:40:47 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:40:47 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:40:57 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:40:57 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 13:40:57 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: CardBadge.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\CardBadge.html (大小: 3863 bytes)
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\CardBadge.html
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: Purchase.xlsx
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Purchase.html (大小: 8082 bytes)
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Purchase.html
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: WindowsView.xlsx
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\WindowsView.html (大小: 24164 bytes)
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\WindowsView.html
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Toy Match 3D_埋点方案.html
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: guanqia.xlsx
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\guanqia.html (大小: 10374 bytes)
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\guanqia.html
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\DartRivalsProgress.html
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: [调用]多语言.xlsx
2025-07-29 13:42:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:42:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:42:23 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 13:42:23 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 13:42:23 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\[调用]多语言.html
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 13:42:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:42:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:42:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\DartRivalsProgressRewards.html
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: MatchModel.xlsx
2025-07-29 13:42:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Badge.xlsx
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Badge.html (大小: 7178 bytes)
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Badge.html
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: MatchModelV2.xlsx
2025-07-29 13:42:34 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:42:34 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:42:40 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:42:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:42:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\MatchModelV2.html
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: 游戏常量.xlsx
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\游戏常量.html (大小: 4481 bytes)
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\游戏常量.html
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 13:42:43 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:42:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:42:43 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\MainInterfaceShow.html
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753767657_5d8e4ac9
2025-07-29 13:47:44 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: 游戏常量.xlsx
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\游戏常量.html (大小: 4481 bytes)
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\游戏常量.html
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: Purchase.xlsx
2025-07-29 13:49:43 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:49:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:49:43 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Purchase.html (大小: 8082 bytes)
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Purchase.html
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\DartRivalsProgressRewards.html
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Badge.xlsx
2025-07-29 13:49:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:49:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:49:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Badge.html (大小: 7178 bytes)
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Badge.html
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: WindowsView.xlsx
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\WindowsView.html (大小: 24164 bytes)
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\WindowsView.html
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: MatchModel.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\MainInterfaceShow.html
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\DartRivalsProgress.html
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: MatchModelV2.xlsx
2025-07-29 13:50:00 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:50:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:50:05 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:50:05 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:50:05 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\MatchModelV2.html
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: CardBadge.xlsx
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\CardBadge.html (大小: 3863 bytes)
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\CardBadge.html
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Toy Match 3D_埋点方案.html
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: guanqia.xlsx
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\guanqia.html (大小: 10374 bytes)
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\guanqia.html
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: [调用]多语言.xlsx
2025-07-29 13:50:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:50:14 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:50:14 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 13:50:14 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 13:50:14 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\[调用]多语言.html
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:50:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 13:50:16 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 13:50:16 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 13:50:16 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753768109_52b27f6d
2025-07-29 13:55:16 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753768109_52b27f6d
2025-07-29 13:55:34 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:39 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:41 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:44 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:46 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:49 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:52 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:57 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:59 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:58:21 - SimpleConfigCompare-TEST - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 13:58:21 - SimpleConfigCompare-TEST - INFO - 配置加载成功: TEST
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: Purchase.xlsx
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Purchase.html (大小: 8082 bytes)
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Purchase.html
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:04:54 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 14:04:54 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:04:55 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:04:56 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:04:56 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:04:56 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:04:56 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\[调用]多语言.html
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: 游戏常量.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\游戏常量.html (大小: 4481 bytes)
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\游戏常量.html
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: MatchModel.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: Badge.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Badge.html (大小: 7178 bytes)
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Badge.html
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgressRewards.html
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgress.html
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: guanqia.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\guanqia.html (大小: 10374 bytes)
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\guanqia.html
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: CardBadge.xlsx
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\CardBadge.html (大小: 3863 bytes)
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\CardBadge.html
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:05:15 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:05:15 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:05:21 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:05:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:05:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\MatchModelV2.html
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\MainInterfaceShow.html
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Toy Match 3D_埋点方案.html
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: WindowsView.xlsx
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\WindowsView.html (大小: 24164 bytes)
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\WindowsView.html
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753769017_ce852422
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 14:05:47 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:05:48 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:05:48 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:05:48 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:05:49 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:05:49 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:05:49 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:05:50 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:06:10 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:06:10 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:06:10 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:06:11 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:06:11 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:06:12 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:06:12 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:06:12 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:06:12 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:06:16 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:06:17 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:06:17 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:06:17 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:07:31 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:07:31 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:07:31 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:07:32 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 找到提交: 92b7c69e - theme 25 models
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:07:36 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:07:36 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:07:36 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:07:36 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\DartRivalsProgressRewards.html
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\DartRivalsProgress.html
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: CardBadge.xlsx
2025-07-29 14:08:40 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:08:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:08:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\CardBadge.html (大小: 3863 bytes)
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\CardBadge.html
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: Purchase.xlsx
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Purchase.html (大小: 8082 bytes)
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Purchase.html
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:08:51 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:08:51 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:08:58 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:08:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:08:58 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\MatchModelV2.html
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\MainInterfaceShow.html
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: MatchModel.xlsx
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: 游戏常量.xlsx
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\游戏常量.html (大小: 4481 bytes)
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\游戏常量.html
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:09:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:09:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:09:07 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:09:07 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:09:07 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\[调用]多语言.html
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: Badge.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Badge.html (大小: 7178 bytes)
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Badge.html
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: WindowsView.xlsx
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\WindowsView.html (大小: 24164 bytes)
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\WindowsView.html
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Toy Match 3D_埋点方案.html
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:09:17 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753769255_b623f1e9
2025-07-29 14:16:15 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:16:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:16:23 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:18:27 - SimpleConfigCompare-TEST - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:18:27 - SimpleConfigCompare-TEST - INFO - 配置加载成功: TEST
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 找到提交: 92b7c69e - theme 25 models
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 14:21:14 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 14:21:14 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:21:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:21:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:21:18 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:21:18 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:21:18 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\[调用]多语言.html
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:21:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:21:20 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\DartRivalsProgress.html
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: MatchModel.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Badge.xlsx
2025-07-29 14:21:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:21:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:21:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Badge.html (大小: 7178 bytes)
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Badge.html
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\MainInterfaceShow.html
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\DartRivalsProgressRewards.html
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: Purchase.xlsx
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Purchase.html (大小: 8082 bytes)
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Purchase.html
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:21:39 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:21:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:21:45 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:21:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:21:45 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\MatchModelV2.html
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: CardBadge.xlsx
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\CardBadge.html (大小: 3863 bytes)
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\CardBadge.html
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: WindowsView.xlsx
2025-07-29 14:21:48 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:21:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:21:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\WindowsView.html (大小: 24164 bytes)
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\WindowsView.html
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: 游戏常量.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\游戏常量.html (大小: 4481 bytes)
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\游戏常量.html
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:51 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 14:21:51 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Toy_Match_3D_埋点方案.html
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753770011_264d24ac
2025-07-29 14:26:54 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753770011_264d24ac
2025-07-29 14:29:50 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:31:32 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:35:59 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 找到提交: c27ad30a - Merge remote-tracking branch 'origin/feature/fixbug_3.29.0_zdx' into qa_test_3.29.0
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 找到 2 个相关提交
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 最终找到 8 个Excel文件变更
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\[调用]多语言.html (大小: 36419 bytes)
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\[调用]多语言.html
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - 进度: 46% - 处理文件: MatchModel.xlsx
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\MainInterfaceShow.html
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: Purchase.xlsx
2025-07-29 14:36:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:36:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:36:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\Purchase.html (大小: 8082 bytes)
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\Purchase.html
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:36:33 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:36:33 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:36:38 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:36:38 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:36:38 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\MatchModelV2.html
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: WindowsView.xlsx
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\WindowsView.html (大小: 24164 bytes)
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\WindowsView.html
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: 游戏常量.xlsx
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\游戏常量.html (大小: 4481 bytes)
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\游戏常量.html
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 进度: 83% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\Toy_Match_3D_埋点方案.html
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753770979_f022be37
2025-07-29 14:37:53 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:38:16 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:41:43 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753770979_f022be37
2025-07-29 14:42:07 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:42:08 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:42:09 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:42:24 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:42:40 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: c27ad30a - Merge remote-tracking branch 'origin/feature/fixbug_3.29.0_zdx' into qa_test_3.29.0
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 找到 2 个相关提交
2025-07-29 14:47:23 - SimpleConfigCompare-TM3D - INFO - 最终找到 8 个Excel文件变更
2025-07-29 14:47:23 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:47:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:47:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:47:25 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:47:25 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:47:25 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\[调用]多语言.html
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - INFO - 进度: 46% - 处理文件: WindowsView.xlsx
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\WindowsView.html
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试安全路径对比: WindowsView
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\WindowsView_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\WindowsView_new
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: WindowsView
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\WindowsView.html
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比也失败: WindowsView
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:47:32 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:47:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:47:37 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:47:37 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:47:37 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\MatchModelV2.html
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试安全路径对比: MatchModelV2
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\MatchModelV2_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\MatchModelV2_new
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: MatchModelV2
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\MatchModelV2.html
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比也失败: MatchModelV2
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: Purchase.xlsx
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\Purchase.html
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试安全路径对比: Purchase
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Purchase_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Purchase_new
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Purchase
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\Purchase.html
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比也失败: Purchase
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: 游戏常量.xlsx
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\游戏常量.html
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\MainInterfaceShow.html
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试安全路径对比: MainInterfaceShow
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\MainInterfaceShow_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\MainInterfaceShow_new
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: MainInterfaceShow
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\MainInterfaceShow.html
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比也失败: MainInterfaceShow
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:47:46 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:46 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\Toy_Match_3D_埋点方案.html
2025-07-29 14:47:46 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:46 - SimpleConfigCompare-TM3D - INFO - 进度: 83% - 处理文件: MatchModel.xlsx
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753771642_9d03a4c3
2025-07-29 14:49:20 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:52:47 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753771642_9d03a4c3
2025-07-29 14:55:16 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:55:22 - SimpleConfigCompare-TEST - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:55:22 - SimpleConfigCompare-TEST - INFO - 配置加载成功: TEST
2025-07-29 15:06:03 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 找到提交: c27ad30a - Merge remote-tracking branch 'origin/feature/fixbug_3.29.0_zdx' into qa_test_3.29.0
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 找到 2 个相关提交
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 最终找到 8 个Excel文件变更
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: Purchase.xlsx
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\Purchase.html (大小: 8082 bytes)
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\Purchase.html
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 进度: 46% - 处理文件: WindowsView.xlsx
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\WindowsView.html (大小: 24164 bytes)
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\WindowsView.html
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: [调用]多语言.xlsx
2025-07-29 15:06:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:06:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:06:11 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 15:06:11 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 15:06:11 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\[调用]多语言.html (大小: 36419 bytes)
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\[调用]多语言.html
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\MainInterfaceShow.html
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: 游戏常量.xlsx
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\游戏常量.html (大小: 4481 bytes)
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\游戏常量.html
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\Toy_Match_3D_埋点方案.html
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: MatchModelV2.xlsx
2025-07-29 15:06:21 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:06:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:06:25 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:06:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:06:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\MatchModelV2.html
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - 进度: 83% - 处理文件: MatchModel.xlsx
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753772764_f37d61b9
2025-07-29 15:06:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:06:40 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:06:43 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:07:14 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:07:15 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:07:15 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:07:15 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 找到提交: 92b7c69e - theme 25 models
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 15:07:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 15:07:22 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 15:07:22 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 15:07:22 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 15:08:21 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 15:08:21 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModel.xlsx
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: Purchase.xlsx
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Purchase.html (大小: 8082 bytes)
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Purchase.html
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: CardBadge.xlsx
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\CardBadge.html (大小: 3863 bytes)
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\CardBadge.html
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: [调用]多语言.xlsx
2025-07-29 15:08:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:08:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:08:28 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 15:08:28 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 15:08:28 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\[调用]多语言.html
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: MatchModelV2.xlsx
2025-07-29 15:08:34 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:08:34 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:08:39 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:08:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:08:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\MatchModelV2.html
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: Badge.xlsx
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Badge.html (大小: 7178 bytes)
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Badge.html
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 15:08:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:08:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:08:42 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\DartRivalsProgressRewards.html
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: 游戏常量.xlsx
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\游戏常量.html (大小: 4481 bytes)
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\游戏常量.html
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\DartRivalsProgress.html
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Toy_Match_3D_埋点方案.html
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: WindowsView.xlsx
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\WindowsView.html (大小: 24164 bytes)
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\WindowsView.html
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\MainInterfaceShow.html
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753772841_bd6c8931
2025-07-29 15:09:46 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:10:44 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:10:55 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:15:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:15:13 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:15:13 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:15:14 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 找到提交: 92b7c69e - theme 25 models
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 15:15:18 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 15:15:18 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 15:15:18 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 15:15:18 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 15:16:16 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 15:16:16 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: Purchase.xlsx
2025-07-29 15:16:17 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:16:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:16:17 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Purchase.html (大小: 8082 bytes)
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Purchase.html
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: WindowsView.xlsx
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\WindowsView.html (大小: 24164 bytes)
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\WindowsView.html
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: MatchModel.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: 游戏常量.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\游戏常量.html (大小: 4481 bytes)
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\游戏常量.html
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\DartRivalsProgressRewards.html
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 15:16:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 15:16:25 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: MatchModelV2.xlsx
2025-07-29 15:16:32 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:16:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:16:38 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:16:38 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:16:38 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\MatchModelV2.html
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Toy_Match_3D_埋点方案.html
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: [调用]多语言.xlsx
2025-07-29 15:16:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:16:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:16:43 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 15:16:43 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 15:16:43 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\[调用]多语言.html
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: Badge.xlsx
2025-07-29 15:16:45 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:16:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:16:45 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Badge.html (大小: 7178 bytes)
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Badge.html
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\DartRivalsProgress.html
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: CardBadge.xlsx
2025-07-29 15:16:48 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:16:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:16:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\CardBadge.html (大小: 3863 bytes)
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\CardBadge.html
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\MainInterfaceShow.html
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753773317_a58704ee
2025-07-29 15:24:58 - SimpleConfigCompare-TEST - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:24:58 - SimpleConfigCompare-TEST - INFO - 配置加载成功: TEST
