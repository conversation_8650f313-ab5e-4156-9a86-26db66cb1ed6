2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:08:55 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 找到提交: 4497a976 - Merge remote-tracking branch 'origin/feature/theme_26' into qa_test_3.29.0
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 找到提交: a7289e76 - 需求 【无尽礼包】付费节点之间免费项未领取完时，主界面icon文字显示Claim
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:08:56 - SimpleConfigCompare-TM3D - INFO - 找到 4 个相关提交
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 最终找到 4 个Excel文件变更
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:08:57 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\DartRivalsProgress.html
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:08:58 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:08:59 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:09:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:09:00 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\[调用]多语言.html (大小: 36495 bytes)
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\[调用]多语言.html
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:09:01 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:09:05 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:09:08 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:09:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:09:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\MatchModelV2.html
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:09:14 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753754897_c6794a0d\DartRivalsProgressRewards.html
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:09:15 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:09:16 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:09:16 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753754897_c6794a0d
2025-07-29 10:10:08 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:10:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:10:16 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:10:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:14:16 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753754897_c6794a0d
2025-07-29 10:15:47 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:15:51 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:17:26 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:00 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:04 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:17 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:27 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:18:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:19:09 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:19:24 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 找到提交: 4497a976 - Merge remote-tracking branch 'origin/feature/theme_26' into qa_test_3.29.0
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 找到提交: a7289e76 - 需求 【无尽礼包】付费节点之间免费项未领取完时，主界面icon文字显示Claim
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 找到 4 个相关提交
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 最终找到 4 个Excel文件变更
2025-07-29 10:19:25 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\DartRivalsProgress.html
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:19:26 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:19:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:19:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:19:29 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\[调用]多语言.html (大小: 36495 bytes)
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\[调用]多语言.html
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:19:30 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:19:36 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:19:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:19:41 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\MatchModelV2.html
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:19:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753755564_8511cf7f\DartRivalsProgressRewards.html
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:19:43 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753755564_8511cf7f
2025-07-29 10:23:40 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:24:43 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753755564_8511cf7f
2025-07-29 10:31:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:31:25 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 找到提交: 4497a976 - Merge remote-tracking branch 'origin/feature/theme_26' into qa_test_3.29.0
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 找到提交: a7289e76 - 需求 【无尽礼包】付费节点之间免费项未领取完时，主界面icon文字显示Claim
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:31:26 - SimpleConfigCompare-TM3D - INFO - 找到 4 个相关提交
2025-07-29 10:31:27 - SimpleConfigCompare-TM3D - INFO - 最终找到 4 个Excel文件变更
2025-07-29 10:31:27 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:31:36 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:31:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:31:45 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\MatchModelV2.html
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:31:47 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\DartRivalsProgress.html
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:31:48 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:31:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:31:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:31:50 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\[调用]多语言.html (大小: 36495 bytes)
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\[调用]多语言.html
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:31:51 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756285_cb946962\DartRivalsProgressRewards.html
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:31:52 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753756285_cb946962
2025-07-29 10:31:58 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:32:04 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:32:08 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:32:11 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:40:21 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: 4497a976 - Merge remote-tracking branch 'origin/feature/theme_26' into qa_test_3.29.0
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: a7289e76 - 需求 【无尽礼包】付费节点之间免费项未领取完时，主界面icon文字显示Claim
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 找到 4 个相关提交
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 最终找到 4 个Excel文件变更
2025-07-29 10:40:22 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:40:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:40:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:40:23 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\DartRivalsProgressRewards.html
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:40:24 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\DartRivalsProgress.html
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:40:25 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:40:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\[调用]多语言.html (大小: 36495 bytes)
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\[调用]多语言.html
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:40:28 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:40:38 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 10:40:38 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:40:48 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 10:40:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:40:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753756821_81b79d8a\MatchModelV2.html
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:40:49 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753756821_81b79d8a
2025-07-29 10:40:55 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:40:58 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:03 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:20 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:31 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:35 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:41:40 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:43:11 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:45:49 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753756821_81b79d8a
2025-07-29 10:47:19 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:23 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:28 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:31 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:32 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:47:32 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:47:32 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:47:32 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:47:46 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:47:46 - SimpleConfigCompare-TM3D - INFO - BCompare安装检测成功
2025-07-29 10:47:46 - SimpleConfigCompare-TM3D - INFO - BCompare脚本文件存在: D:\git\qa_tools\config\compare_folder.txt
2025-07-29 10:47:46 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:47:47 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:49:09 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:49:10 - SimpleConfigCompare-TM3D - ERROR - 配置加载失败: 'SimpleConfigCompareService' object has no attribute '_test_bcompare_installation'
2025-07-29 10:49:18 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:49:18 - SimpleConfigCompare-TM3D - ERROR - 配置加载失败: 'SimpleConfigCompareService' object has no attribute '_test_bcompare_installation'
2025-07-29 10:49:45 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:49:46 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:50:44 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:50:45 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:50:46 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 找到提交: 5e948de9 - fixbug 【card系统】获得勋章时点击物理返回,退出弹窗在勋章下层级
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 10:50:59 - SimpleConfigCompare-TM3D - INFO - 找到 9 个相关提交
2025-07-29 10:52:01 - SimpleConfigCompare-TM3D - INFO - 最终找到 12 个Excel文件变更
2025-07-29 10:52:01 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModelV2.xlsx
2025-07-29 10:52:10 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 10:52:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:52:18 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 10:52:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:52:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\MatchModelV2.html
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 进度: 44% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 10:52:20 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\DartRivalsProgressRewards.html
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 10:52:21 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: [调用]多语言.xlsx
2025-07-29 10:52:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:52:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:52:24 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\[调用]多语言.html (大小: 1465044 bytes)
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\[调用]多语言.html
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 10:52:25 - SimpleConfigCompare-TM3D - INFO - 进度: 60% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 10:52:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:52:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:52:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\DartRivalsProgress.html
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 10:52:27 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - INFO - 进度: 69% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 10:52:28 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: CardBadge.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 10:52:29 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\CardBadge.html (大小: 3863 bytes)
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\CardBadge.html
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: Badge.xlsx
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 10:52:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\Badge.html (大小: 7178 bytes)
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753757459_4cded443\Badge.html
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 10:52:31 - SimpleConfigCompare-TM3D - INFO - 进度: 85% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 10:52:32 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753757459_4cded443
2025-07-29 10:53:06 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:08 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:10 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:15 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:18 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:22 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:28 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:31 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:53:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:55:35 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 10:55:35 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:19:34 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:19:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:19:37 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:19:38 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:19:48 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:19:49 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:20:48 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:20:48 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:20:48 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:20:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:20:49 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\MainInterfaceShow.html
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:20:50 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:20:52 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:20:54 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:20:54 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\[调用]多语言.html (大小: 1465044 bytes)
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\[调用]多语言.html
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: WindowsView.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:20:55 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\WindowsView.html
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: Badge.xlsx
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:20:56 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Badge.html (大小: 7178 bytes)
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Badge.html
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: CardBadge.xlsx
2025-07-29 11:20:57 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\CardBadge.html
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:20:58 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:20:59 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:20:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:20:59 - SimpleConfigCompare-TM3D - INFO - 执行对比: Toy Match 3D_埋点方案
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Toy Match 3D_埋点方案.html
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - ERROR - 简化脚本文件不存在: D:\git\qa_tools\config\compare_folder_simple.txt
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:21:00 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\DartRivalsProgress.html
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:01 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:21:02 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:21:09 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:21:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:21:15 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:21:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:21:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\MatchModelV2.html
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:21:16 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:21:17 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:17 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:21:17 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: MatchModel.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: Purchase.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:21:19 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Purchase.html (大小: 8082 bytes)
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\Purchase.html
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: 游戏常量.xlsx
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:21:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\游戏常量.html
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:21:22 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759188_040e6060\DartRivalsProgressRewards.html
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:21:23 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:21:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:21:24 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:21:24 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:21:24 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753759188_040e6060
2025-07-29 11:21:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:21:43 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:21:47 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:22:19 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:22:20 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:23:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:23:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:24:32 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:32:05 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:32:06 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:32:07 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:32:09 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:33:08 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:33:08 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:33:16 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:33:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:33:23 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:33:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:33:23 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\MatchModelV2.html
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:33:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\DartRivalsProgress.html
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:26 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: WindowsView.xlsx
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:33:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\WindowsView.html
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Badge.xlsx
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:33:28 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Badge.html (大小: 7178 bytes)
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Badge.html
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:33:29 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:33:30 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:33:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:33:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: Toy Match 3D_埋点方案
2025-07-29 11:33:31 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:33:31 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Toy Match 3D_埋点方案.html
2025-07-29 11:33:31 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败，尝试简化脚本对比: Toy Match 3D_埋点方案
2025-07-29 11:33:31 - SimpleConfigCompare-TM3D - INFO - 尝试使用简化脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:33:32 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:33:32 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Toy Match 3D_埋点方案.html
2025-07-29 11:33:32 - SimpleConfigCompare-TM3D - WARNING - 简化脚本也失败，尝试安全脚本: Toy_Match_3D_埋点方案
2025-07-29 11:33:32 - SimpleConfigCompare-TM3D - INFO - 尝试使用安全脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 107: 脚本加载文件夹或文件失败
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Toy Match 3D_埋点方案.html
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - ERROR - 安全脚本对比也失败，返回码: 107
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:33:33 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\MainInterfaceShow.html
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:33:35 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModel.xlsx
2025-07-29 11:33:38 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:33:38 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:33:38 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:33:38 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: Purchase.xlsx
2025-07-29 11:33:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Purchase.html (大小: 8082 bytes)
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\Purchase.html
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:33:40 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: CardBadge.xlsx
2025-07-29 11:33:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:33:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:33:41 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\CardBadge.html
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:33:42 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\DartRivalsProgressRewards.html
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:33:43 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:33:45 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:33:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:33:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\[调用]多语言.html (大小: 1465042 bytes)
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\[调用]多语言.html
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: 游戏常量.xlsx
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:33:47 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753759929_84a007a0\游戏常量.html
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:33:48 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753759929_84a007a0
2025-07-29 11:39:47 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:39:47 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:39:47 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:39:48 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:40:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\DartRivalsProgressRewards.html
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:40:47 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:40:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:40:51 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:40:51 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\[调用]多语言.html (大小: 1465042 bytes)
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\[调用]多语言.html
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:40:52 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:40:59 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:40:59 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:41:05 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:41:05 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:41:05 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\MatchModelV2.html
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:06 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: 游戏常量.xlsx
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:41:07 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\游戏常量.html
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:41:08 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - INFO - 执行对比: Toy Match 3D_埋点方案
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Toy Match 3D_埋点方案.html
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试简化脚本对比: Toy Match 3D_埋点方案
2025-07-29 11:41:09 - SimpleConfigCompare-TM3D - INFO - 尝试使用简化脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:41:10 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:41:10 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Toy Match 3D_埋点方案.html
2025-07-29 11:41:10 - SimpleConfigCompare-TM3D - WARNING - 简化脚本也失败，尝试安全脚本: Toy_Match_3D_埋点方案
2025-07-29 11:41:10 - SimpleConfigCompare-TM3D - INFO - 尝试使用安全脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 107: 脚本加载文件夹或文件失败
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Toy Match 3D_埋点方案.html
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - ERROR - 安全脚本对比也失败，返回码: 107
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - ERROR - 简化脚本也失败: Toy Match 3D_埋点方案
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:41:11 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: CardBadge.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:41:12 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\CardBadge.html
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:41:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\MainInterfaceShow.html
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:41:14 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: MatchModel.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: Purchase.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:41:17 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Purchase.html (大小: 8082 bytes)
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Purchase.html
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Badge.xlsx
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:41:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Badge.html (大小: 7178 bytes)
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\Badge.html
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:19 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:41:20 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\DartRivalsProgress.html
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: WindowsView.xlsx
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:41:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760387_ac5540c4\WindowsView.html
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:41:22 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:41:23 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753760387_ac5540c4
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:41:59 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:43:02 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:43:02 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: 游戏常量.xlsx
2025-07-29 11:43:02 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\游戏常量.html
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:43:03 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: Badge.xlsx
2025-07-29 11:43:04 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:43:04 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:43:04 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Badge.html (大小: 7178 bytes)
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Badge.html
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:43:05 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:43:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:43:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:43:07 - SimpleConfigCompare-TM3D - INFO - 执行对比: [调用]多语言
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\[调用]多语言.html (大小: 1465042 bytes)
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\[调用]多语言.html
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:43:08 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Purchase.xlsx
2025-07-29 11:43:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:43:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:43:09 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Purchase.html (大小: 8082 bytes)
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Purchase.html
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:43:10 - SimpleConfigCompare-TM3D - INFO - 执行对比: Toy Match 3D_埋点方案
2025-07-29 11:43:11 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:43:11 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Toy Match 3D_埋点方案.html
2025-07-29 11:43:11 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试简化脚本对比: Toy Match 3D_埋点方案
2025-07-29 11:43:11 - SimpleConfigCompare-TM3D - INFO - 尝试使用简化脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:43:12 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:43:12 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Toy Match 3D_埋点方案.html
2025-07-29 11:43:12 - SimpleConfigCompare-TM3D - WARNING - 简化脚本也失败，尝试安全脚本: Toy_Match_3D_埋点方案
2025-07-29 11:43:12 - SimpleConfigCompare-TM3D - INFO - 尝试使用安全脚本重新对比: Toy_Match_3D_埋点方案
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 107: 脚本加载文件夹或文件失败
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\Toy Match 3D_埋点方案.html
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - ERROR - 安全脚本对比也失败，返回码: 107
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - ERROR - 简化脚本也失败: Toy Match 3D_埋点方案
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:43:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\MainInterfaceShow.html
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:43:14 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:43:15 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:15 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:43:15 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:43:21 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:43:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:43:25 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:43:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:43:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\MatchModelV2.html
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:43:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\DartRivalsProgress.html
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:43:28 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: MatchModel.xlsx
2025-07-29 11:43:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:43:29 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:43:29 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:43:29 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:43:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\DartRivalsProgressRewards.html
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: CardBadge.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:43:32 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\CardBadge.html
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:43:33 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: WindowsView.xlsx
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753760519_f3b44403\WindowsView.html
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:43:34 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:43:35 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:43:35 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753760519_f3b44403
2025-07-29 11:47:34 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:53:10 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:53:11 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:53:11 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:54:46 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:54:46 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:54:46 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:54:47 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:55:54 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:55:54 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModel.xlsx
2025-07-29 11:55:57 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:55:58 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\DartRivalsProgressRewards.html
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:55:59 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:56:01 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:56:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:56:02 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 11:56:02 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\调用_多语言_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\调用_多语言_new
2025-07-29 11:56:02 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\[调用]多语言.html (大小: 1464972 bytes)
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\[调用]多语言.html
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:56:03 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\DartRivalsProgress.html
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:56:04 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Purchase.xlsx
2025-07-29 11:56:05 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:56:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:56:06 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Purchase.html (大小: 8082 bytes)
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Purchase.html
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:56:07 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:56:08 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_埋点方案_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_埋点方案_new
2025-07-29 11:56:09 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Toy Match 3D_埋点方案.html
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:56:10 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: Badge.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:56:11 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Badge.html (大小: 7178 bytes)
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\Badge.html
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: WindowsView.xlsx
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:56:12 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\WindowsView.html
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: 游戏常量.xlsx
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:56:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\游戏常量.html
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: CardBadge.xlsx
2025-07-29 11:56:14 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\CardBadge.html
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:56:15 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:56:16 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:56:16 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:56:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:56:16 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:56:22 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:56:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:56:27 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:56:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:56:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\MatchModelV2.html
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:56:29 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761286_a5dcf333\MainInterfaceShow.html
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:56:30 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753761286_a5dcf333
2025-07-29 11:57:14 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:57:21 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:57:21 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:57:29 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:57:30 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:57:30 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:57:30 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:57:30 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:57:49 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:57:51 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: WindowsView.xlsx
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:58:31 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\WindowsView.html (大小: 24164 bytes)
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\WindowsView.html
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: Purchase.xlsx
2025-07-29 11:58:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Purchase.html (大小: 8082 bytes)
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Purchase.html
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 11:58:33 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 11:58:34 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:58:34 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:58:34 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\MainInterfaceShow.html
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 11:58:35 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\DartRivalsProgress.html
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 11:58:36 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 11:58:37 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:58:38 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 11:58:38 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 11:58:38 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_埋点方案_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_埋点方案_new
2025-07-29 11:58:38 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Toy Match 3D_埋点方案.html
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: Badge.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:58:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Badge.html (大小: 7178 bytes)
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\Badge.html
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 11:58:40 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: MatchModelV2.xlsx
2025-07-29 11:58:47 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:58:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:58:54 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 11:58:54 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:58:54 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\MatchModelV2.html
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: CardBadge.xlsx
2025-07-29 11:58:55 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\CardBadge.html (大小: 3863 bytes)
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\CardBadge.html
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:58:56 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:58:57 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 11:58:57 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: [调用]多语言.xlsx
2025-07-29 11:58:58 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:58:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:58:59 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 11:58:59 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\调用_多语言_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\调用_多语言_new
2025-07-29 11:58:59 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\[调用]多语言.html (大小: 1464972 bytes)
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\[调用]多语言.html
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:59:00 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\DartRivalsProgressRewards.html
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:59:01 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@83d772073b7c871ebd86c0c90dc04c771f1a69b5: 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: 游戏常量.xlsx
2025-07-29 11:59:02 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 执行对比: 游戏常量
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\游戏常量.html (大小: 4539 bytes)
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753761449_936adf2e\游戏常量.html
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 11:59:03 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: MatchModel.xlsx
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 11:59:07 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753761449_936adf2e
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 找到提交: 83d77207 - 场景26
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 11:59:16 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 11:59:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:06:29 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:06:29 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:06:30 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:06:41 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:06:41 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 12:06:41 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 12:06:42 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: Purchase.xlsx
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:08:06 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Purchase.html (大小: 8082 bytes)
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Purchase.html
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:08:07 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: MatchModelV2.xlsx
2025-07-29 12:08:13 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 12:08:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:08:18 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 12:08:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:08:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\MatchModelV2.html
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: CardBadge.xlsx
2025-07-29 12:08:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\CardBadge.html (大小: 3863 bytes)
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\CardBadge.html
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: WindowsView.xlsx
2025-07-29 12:08:20 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\WindowsView.html (大小: 24164 bytes)
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\WindowsView.html
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:08:21 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: 游戏常量.xlsx
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 12:08:22 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\游戏常量.html (大小: 4481 bytes)
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\游戏常量.html
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 12:08:23 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: MatchModel.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 12:08:25 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 12:08:26 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 12:08:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:08:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:08:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\DartRivalsProgress.html
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 12:08:28 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: guanqia.xlsx
2025-07-29 12:08:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:08:29 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:08:29 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\guanqia.html (大小: 10374 bytes)
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\guanqia.html
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Badge.xlsx
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:08:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Badge.html (大小: 7178 bytes)
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Badge.html
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:08:31 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\MainInterfaceShow.html
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 12:08:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 12:08:33 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 12:08:33 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 12:08:33 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_new
2025-07-29 12:08:33 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 12:08:34 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 12:08:34 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\Toy Match 3D_埋点方案.html
2025-07-29 12:08:34 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 12:08:34 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: [调用]多语言.xlsx
2025-07-29 12:08:35 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:08:36 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:08:36 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 12:08:36 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_477c04f0_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_477c04f0_new
2025-07-29 12:08:36 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\[调用]多语言.html (大小: 1464966 bytes)
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\[调用]多语言.html
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:08:37 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753762001_c91f7d92\DartRivalsProgressRewards.html
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 12:08:38 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753762001_c91f7d92
2025-07-29 12:13:38 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753762001_c91f7d92
2025-07-29 12:16:53 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:17:12 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:17:19 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:17:29 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:17:45 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:18:01 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 12:19:29 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 12:28:17 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:28:19 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 12:28:19 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:28:20 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 12:28:25 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 12:29:43 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 12:29:43 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: WindowsView.xlsx
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\WindowsView.html (大小: 24164 bytes)
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\WindowsView.html
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 12:29:44 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 12:29:45 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Toy Match 3D_埋点方案.html
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: Purchase.xlsx
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:29:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Purchase.html (大小: 8082 bytes)
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Purchase.html
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 12:29:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\MainInterfaceShow.html
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 12:29:48 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:29:49 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\DartRivalsProgress.html
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: guanqia.xlsx
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:29:50 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\guanqia.html (大小: 10374 bytes)
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\guanqia.html
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:29:51 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: 游戏常量.xlsx
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 12:29:52 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\游戏常量.html (大小: 4481 bytes)
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\游戏常量.html
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:29:53 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\DartRivalsProgressRewards.html
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: CardBadge.xlsx
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:29:54 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\CardBadge.html (大小: 3863 bytes)
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\CardBadge.html
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 12:29:55 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: MatchModel.xlsx
2025-07-29 12:29:58 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 12:29:58 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 12:29:58 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 12:29:58 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 12:29:59 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: [调用]多语言.xlsx
2025-07-29 12:30:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:30:01 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:30:01 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 12:30:01 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 12:30:01 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\[调用]多语言.html
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 12:30:03 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: MatchModelV2.xlsx
2025-07-29 12:30:08 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 12:30:08 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:30:13 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 12:30:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:30:13 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\MatchModelV2.html
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: Badge.xlsx
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:30:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Badge.html (大小: 7178 bytes)
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753763305_9db10b11\Badge.html
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 12:30:16 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 12:30:17 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753763305_9db10b11
2025-07-29 12:35:17 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753763305_9db10b11
2025-07-29 13:30:54 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:33:34 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:34:26 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:37:50 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:37:50 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:37:50 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 13:37:54 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:39:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\DartRivalsProgress.html
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:39:09 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: WindowsView.xlsx
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\WindowsView.html (大小: 24164 bytes)
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\WindowsView.html
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:39:10 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 13:39:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 13:39:11 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: [调用]多语言.xlsx
2025-07-29 13:39:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:39:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:39:13 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 13:39:13 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 13:39:13 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\[调用]多语言.html
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 13:39:14 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: MatchModel.xlsx
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: guanqia.xlsx
2025-07-29 13:39:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:39:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:39:17 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\guanqia.html (大小: 10374 bytes)
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\guanqia.html
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 13:39:18 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:39:19 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:39:19 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\DartRivalsProgressRewards.html
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:39:20 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: Purchase.xlsx
2025-07-29 13:39:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:39:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:39:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Purchase.html (大小: 8082 bytes)
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Purchase.html
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: CardBadge.xlsx
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:39:22 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\CardBadge.html (大小: 3863 bytes)
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\CardBadge.html
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:39:23 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\MainInterfaceShow.html
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:39:24 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 13:39:25 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Toy Match 3D_埋点方案.html
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: 游戏常量.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 13:39:26 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\游戏常量.html (大小: 4481 bytes)
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\游戏常量.html
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:39:27 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: MatchModelV2.xlsx
2025-07-29 13:39:32 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:39:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:39:38 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:39:38 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:39:38 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\MatchModelV2.html
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: Badge.xlsx
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:39:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Badge.html (大小: 7178 bytes)
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767474_437adf1c\Badge.html
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 13:39:40 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753767474_437adf1c
2025-07-29 13:39:50 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:40:46 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:40:47 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:40:47 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:40:57 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:40:57 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 13:40:57 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 13:40:58 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 13:42:11 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: CardBadge.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:42:12 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\CardBadge.html (大小: 3863 bytes)
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\CardBadge.html
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: Purchase.xlsx
2025-07-29 13:42:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Purchase.html (大小: 8082 bytes)
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Purchase.html
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:42:14 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: WindowsView.xlsx
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\WindowsView.html (大小: 24164 bytes)
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\WindowsView.html
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:42:15 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 13:42:16 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Toy Match 3D_埋点方案.html
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:17 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: guanqia.xlsx
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:42:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\guanqia.html (大小: 10374 bytes)
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\guanqia.html
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:42:19 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\DartRivalsProgress.html
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 13:42:20 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: [调用]多语言.xlsx
2025-07-29 13:42:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:42:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:42:23 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 13:42:23 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 13:42:23 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\[调用]多语言.html
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:42:24 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 13:42:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:42:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:42:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\DartRivalsProgressRewards.html
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:42:26 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: MatchModel.xlsx
2025-07-29 13:42:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Badge.xlsx
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:42:28 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Badge.html (大小: 7178 bytes)
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\Badge.html
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:42:29 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: MatchModelV2.xlsx
2025-07-29 13:42:34 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:42:34 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:42:40 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:42:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:42:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\MatchModelV2.html
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: 游戏常量.xlsx
2025-07-29 13:42:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\游戏常量.html (大小: 4481 bytes)
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\游戏常量.html
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:42:42 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 13:42:43 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:42:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:42:43 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9\MainInterfaceShow.html
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 13:42:44 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753767657_5d8e4ac9
2025-07-29 13:47:44 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753767657_5d8e4ac9
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 13:48:29 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 13:48:30 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: 游戏常量.xlsx
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 13:49:41 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\游戏常量.html (大小: 4481 bytes)
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\游戏常量.html
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 13:49:42 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: Purchase.xlsx
2025-07-29 13:49:43 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:49:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:49:43 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Purchase.html (大小: 8082 bytes)
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Purchase.html
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 13:49:44 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\DartRivalsProgressRewards.html
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 13:49:45 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Badge.xlsx
2025-07-29 13:49:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:49:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:49:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Badge.html (大小: 7178 bytes)
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Badge.html
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: WindowsView.xlsx
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:49:47 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\WindowsView.html (大小: 24164 bytes)
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\WindowsView.html
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 13:49:48 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: MatchModel.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:49:51 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\MainInterfaceShow.html
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:49:52 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\DartRivalsProgress.html
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 13:49:53 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: MatchModelV2.xlsx
2025-07-29 13:50:00 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:50:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:50:05 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 13:50:05 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:50:05 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\MatchModelV2.html
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: CardBadge.xlsx
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:50:07 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\CardBadge.html (大小: 3863 bytes)
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\CardBadge.html
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 13:50:08 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 13:50:09 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\Toy Match 3D_埋点方案.html
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: guanqia.xlsx
2025-07-29 13:50:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\guanqia.html (大小: 10374 bytes)
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\guanqia.html
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 13:50:11 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: [调用]多语言.xlsx
2025-07-29 13:50:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:50:14 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:50:14 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 13:50:14 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 13:50:14 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753768109_52b27f6d\[调用]多语言.html
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 13:50:15 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 13:50:16 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 13:50:16 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 13:50:16 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 13:50:16 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753768109_52b27f6d
2025-07-29 13:55:16 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753768109_52b27f6d
2025-07-29 13:55:34 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:39 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:41 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:44 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:46 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:49 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:52 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:57 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:55:59 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 13:58:21 - SimpleConfigCompare-TEST - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 13:58:21 - SimpleConfigCompare-TEST - INFO - 配置加载成功: TEST
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:03:37 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 最终找到 19 个Excel文件变更
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: Purchase.xlsx
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:04:52 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Purchase.html (大小: 8082 bytes)
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Purchase.html
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:04:53 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:04:54 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 14:04:54 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:04:55 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:04:56 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:04:56 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:04:56 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:04:56 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\[调用]多语言.html
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - INFO - 进度: 47% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:04:57 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - INFO - 进度: 50% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 14:04:58 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 进度: 55% - 处理文件: 游戏常量.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:04:59 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\游戏常量.html (大小: 4481 bytes)
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\游戏常量.html
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:05:00 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: MatchModel.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 进度: 61% - 处理文件: Badge.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:05:02 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Badge.html (大小: 7178 bytes)
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Badge.html
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 进度: 63% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:05:03 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgressRewards.html
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 进度: 66% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:05:04 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\DartRivalsProgress.html
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - INFO - 进度: 68% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:05:05 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@65d31179976a133ae38a0e1ad2ef8ad665b8457a: 404: 404 File Not Found
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 进度: 74% - 处理文件: guanqia.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 14:05:06 - SimpleConfigCompare-TM3D - INFO - 执行对比: guanqia
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\guanqia.html (大小: 10374 bytes)
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\guanqia.html
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/guanqia.xlsx
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: CardBadge.xlsx
2025-07-29 14:05:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\CardBadge.html (大小: 3863 bytes)
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\CardBadge.html
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:05:08 - SimpleConfigCompare-TM3D - INFO - 进度: 79% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:05:15 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:05:15 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:05:21 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:05:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:05:21 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\MatchModelV2.html (大小: 236242 bytes)
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\MatchModelV2.html
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 进度: 82% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:05:22 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\MainInterfaceShow.html
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:05:23 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:05:24 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\Toy Match 3D_埋点方案.html
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: WindowsView.xlsx
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:05:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\WindowsView.html (大小: 24164 bytes)
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769017_ce852422\WindowsView.html
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:05:26 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753769017_ce852422
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:05:39 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 找到提交: 65d31179 - fix(GameObjectPool): 修复对象池中父对象为空导致的异常- 在 GameObjectPool.cs 中，为 ins.transform.SetParent 添加异常处理
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:05:40 - SimpleConfigCompare-TM3D - INFO - 找到 11 个相关提交
2025-07-29 14:05:47 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:05:48 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:05:48 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:05:48 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:05:49 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:05:49 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:05:49 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:05:50 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:06:10 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:06:10 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:06:10 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:06:11 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:06:11 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:06:12 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:06:12 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:06:12 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:06:12 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:06:16 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:06:17 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:06:17 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:06:17 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:07:31 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:07:31 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:07:31 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:07:32 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 找到提交: 92b7c69e - theme 25 models
2025-07-29 14:07:35 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:07:36 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:07:36 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:07:36 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:07:36 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:08:37 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\DartRivalsProgressRewards.html
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 14:08:38 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\DartRivalsProgress.html
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:08:39 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: CardBadge.xlsx
2025-07-29 14:08:40 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:08:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:08:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\CardBadge.html (大小: 3863 bytes)
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\CardBadge.html
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: Purchase.xlsx
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:08:41 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Purchase.html (大小: 8082 bytes)
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Purchase.html
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:08:42 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:08:51 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:08:51 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:08:58 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:08:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:08:58 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\MatchModelV2.html
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:08:59 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:09:00 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\MainInterfaceShow.html
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:09:01 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: MatchModel.xlsx
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 14:09:03 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: 游戏常量.xlsx
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:09:04 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\游戏常量.html (大小: 4481 bytes)
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\游戏常量.html
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:09:05 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:09:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:09:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:09:07 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:09:07 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:09:07 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\[调用]多语言.html
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:09:08 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: Badge.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:09:09 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Badge.html (大小: 7178 bytes)
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Badge.html
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: WindowsView.xlsx
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:09:10 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\WindowsView.html (大小: 24164 bytes)
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\WindowsView.html
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:11 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 14:09:12 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy Match 3D_埋点方案
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:09:13 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753769255_b623f1e9\Toy Match 3D_埋点方案.html
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:09:16 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:09:17 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753769255_b623f1e9
2025-07-29 14:16:15 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:16:21 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:16:23 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:18:27 - SimpleConfigCompare-TEST - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:18:27 - SimpleConfigCompare-TEST - INFO - 配置加载成功: TEST
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 找到提交: 92b7c69e - theme 25 models
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:20:11 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 14:21:14 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 14:21:14 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:21:16 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:21:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:21:18 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:21:18 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:21:18 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\[调用]多语言.html
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 14:21:19 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:21:20 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:21:20 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\DartRivalsProgress.html
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 14:21:21 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: MatchModel.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 14:21:25 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: Badge.xlsx
2025-07-29 14:21:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:21:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:21:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Badge.html (大小: 7178 bytes)
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Badge.html
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:21:27 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\MainInterfaceShow.html
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:21:28 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\DartRivalsProgressRewards.html
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: Purchase.xlsx
2025-07-29 14:21:29 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Purchase.html (大小: 8082 bytes)
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Purchase.html
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:21:30 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:21:39 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:21:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:21:45 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:21:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:21:45 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\MatchModelV2.html
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: CardBadge.xlsx
2025-07-29 14:21:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\CardBadge.html (大小: 3863 bytes)
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\CardBadge.html
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 14:21:47 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: WindowsView.xlsx
2025-07-29 14:21:48 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:21:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:21:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\WindowsView.html (大小: 24164 bytes)
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\WindowsView.html
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: 游戏常量.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:21:49 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\游戏常量.html (大小: 4481 bytes)
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\游戏常量.html
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 14:21:50 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 14:21:51 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 14:21:51 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:21:53 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770011_264d24ac\Toy_Match_3D_埋点方案.html
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:21:54 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753770011_264d24ac
2025-07-29 14:26:54 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753770011_264d24ac
2025-07-29 14:29:50 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:31:32 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:35:59 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 找到提交: c27ad30a - Merge remote-tracking branch 'origin/feature/fixbug_3.29.0_zdx' into qa_test_3.29.0
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 找到 2 个相关提交
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 最终找到 8 个Excel文件变更
2025-07-29 14:36:19 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:36:21 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\[调用]多语言.html (大小: 36419 bytes)
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\[调用]多语言.html
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:36:23 - SimpleConfigCompare-TM3D - INFO - 进度: 46% - 处理文件: MatchModel.xlsx
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:36:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\MainInterfaceShow.html
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:36:25 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: Purchase.xlsx
2025-07-29 14:36:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:36:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:36:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\Purchase.html (大小: 8082 bytes)
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\Purchase.html
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:36:27 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:36:33 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:36:33 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:36:38 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:36:38 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:36:38 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\MatchModelV2.html
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: WindowsView.xlsx
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:36:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\WindowsView.html (大小: 24164 bytes)
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\WindowsView.html
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: 游戏常量.xlsx
2025-07-29 14:36:40 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\游戏常量.html (大小: 4481 bytes)
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\游戏常量.html
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:36:41 - SimpleConfigCompare-TM3D - INFO - 进度: 83% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:36:42 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753770979_f022be37\Toy_Match_3D_埋点方案.html
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:36:43 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753770979_f022be37
2025-07-29 14:37:53 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:38:16 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:41:43 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753770979_f022be37
2025-07-29 14:42:07 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:42:08 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:42:09 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:42:24 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:42:40 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: c27ad30a - Merge remote-tracking branch 'origin/feature/fixbug_3.29.0_zdx' into qa_test_3.29.0
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 14:47:22 - SimpleConfigCompare-TM3D - INFO - 找到 2 个相关提交
2025-07-29 14:47:23 - SimpleConfigCompare-TM3D - INFO - 最终找到 8 个Excel文件变更
2025-07-29 14:47:23 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: [调用]多语言.xlsx
2025-07-29 14:47:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:47:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 14:47:25 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 14:47:25 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 14:47:25 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\[调用]多语言.html
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - INFO - 进度: 46% - 处理文件: WindowsView.xlsx
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 14:47:26 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\WindowsView.html
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试安全路径对比: WindowsView
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\WindowsView_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\WindowsView_new
2025-07-29 14:47:27 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: WindowsView
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\WindowsView.html
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比也失败: WindowsView
2025-07-29 14:47:28 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: MatchModelV2.xlsx
2025-07-29 14:47:32 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:47:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:47:37 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 14:47:37 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 14:47:37 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\MatchModelV2.html
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试安全路径对比: MatchModelV2
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\MatchModelV2_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\MatchModelV2_new
2025-07-29 14:47:38 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: MatchModelV2
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\MatchModelV2.html
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比也失败: MatchModelV2
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: Purchase.xlsx
2025-07-29 14:47:39 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\Purchase.html
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试安全路径对比: Purchase
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Purchase_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Purchase_new
2025-07-29 14:47:40 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Purchase
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\Purchase.html
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比也失败: Purchase
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: 游戏常量.xlsx
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 14:47:41 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\游戏常量.html
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 14:47:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\MainInterfaceShow.html
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - WARNING - 主脚本失败（返回码106），尝试安全路径对比: MainInterfaceShow
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\MainInterfaceShow_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\MainInterfaceShow_new
2025-07-29 14:47:43 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: MainInterfaceShow
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\MainInterfaceShow.html
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比也失败: MainInterfaceShow
2025-07-29 14:47:44 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 14:47:45 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 14:47:46 - SimpleConfigCompare-TM3D - WARNING - BCompare返回码 106: 脚本语法错误
2025-07-29 14:47:46 - SimpleConfigCompare-TM3D - WARNING - 报告文件未生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753771642_9d03a4c3\Toy_Match_3D_埋点方案.html
2025-07-29 14:47:46 - SimpleConfigCompare-TM3D - ERROR - 安全路径对比失败，返回码: 106
2025-07-29 14:47:46 - SimpleConfigCompare-TM3D - INFO - 进度: 83% - 处理文件: MatchModel.xlsx
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 14:47:47 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753771642_9d03a4c3
2025-07-29 14:49:20 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:52:47 - SimpleConfigCompare-TM3D - INFO - 清理工作目录: D:\git\qa_tools\services\config_test\TM3D_qa_test_3.29.0_1753771642_9d03a4c3
2025-07-29 14:55:16 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 14:55:22 - SimpleConfigCompare-TEST - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 14:55:22 - SimpleConfigCompare-TEST - INFO - 配置加载成功: TEST
2025-07-29 15:06:03 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:06:04 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 找到提交: c27ad30a - Merge remote-tracking branch 'origin/feature/fixbug_3.29.0_zdx' into qa_test_3.29.0
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 15:06:05 - SimpleConfigCompare-TM3D - INFO - 找到 2 个相关提交
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 最终找到 8 个Excel文件变更
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: Purchase.xlsx
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:06:06 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\Purchase.html (大小: 8082 bytes)
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\Purchase.html
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 进度: 46% - 处理文件: WindowsView.xlsx
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:06:07 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\WindowsView.html (大小: 24164 bytes)
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\WindowsView.html
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:06:09 - SimpleConfigCompare-TM3D - INFO - 进度: 52% - 处理文件: [调用]多语言.xlsx
2025-07-29 15:06:10 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:06:11 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:06:11 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 15:06:11 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 15:06:11 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\[调用]多语言.html (大小: 36419 bytes)
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\[调用]多语言.html
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 进度: 58% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:06:12 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\MainInterfaceShow.html
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: 游戏常量.xlsx
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 15:06:13 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\游戏常量.html (大小: 4481 bytes)
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\游戏常量.html
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 进度: 71% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 15:06:14 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\Toy_Match_3D_埋点方案.html
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:06:15 - SimpleConfigCompare-TM3D - INFO - 进度: 77% - 处理文件: MatchModelV2.xlsx
2025-07-29 15:06:21 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:06:21 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:06:25 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:06:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:06:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\MatchModelV2.html (大小: 108105 bytes)
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772764_f37d61b9\MatchModelV2.html
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:06:27 - SimpleConfigCompare-TM3D - INFO - 进度: 83% - 处理文件: MatchModel.xlsx
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 15:06:28 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753772764_f37d61b9
2025-07-29 15:06:37 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:06:40 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:06:43 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:07:14 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:07:15 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:07:15 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:07:15 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 找到提交: 92b7c69e - theme 25 models
2025-07-29 15:07:21 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 15:07:22 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 15:07:22 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 15:07:22 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 15:07:22 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 15:08:21 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 15:08:21 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MatchModel.xlsx
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:23 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: Purchase.xlsx
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:08:24 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Purchase.html (大小: 8082 bytes)
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Purchase.html
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: CardBadge.xlsx
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:08:25 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\CardBadge.html (大小: 3863 bytes)
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\CardBadge.html
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:08:26 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: [调用]多语言.xlsx
2025-07-29 15:08:27 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:08:28 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:08:28 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 15:08:28 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 15:08:28 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\[调用]多语言.html
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:08:29 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: MatchModelV2.xlsx
2025-07-29 15:08:34 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:08:34 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:08:39 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:08:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:08:39 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\MatchModelV2.html
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: Badge.xlsx
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:08:40 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Badge.html (大小: 7178 bytes)
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Badge.html
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 15:08:41 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 15:08:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:08:42 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:08:42 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\DartRivalsProgressRewards.html
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: 游戏常量.xlsx
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 15:08:43 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\游戏常量.html (大小: 4481 bytes)
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\游戏常量.html
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:08:44 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\DartRivalsProgress.html
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:45 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 15:08:46 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 15:08:47 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\Toy_Match_3D_埋点方案.html
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: WindowsView.xlsx
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:08:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\WindowsView.html (大小: 24164 bytes)
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\WindowsView.html
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 15:08:49 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753772841_bd6c8931\MainInterfaceShow.html
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:08:50 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 15:08:51 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753772841_bd6c8931
2025-07-29 15:09:46 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:10:44 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:10:55 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:15:13 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:15:13 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:15:13 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:15:14 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 找到提交: 92b7c69e - theme 25 models
2025-07-29 15:15:17 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 15:15:18 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 15:15:18 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 15:15:18 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 15:15:18 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 15:16:16 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 15:16:16 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: Purchase.xlsx
2025-07-29 15:16:17 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:16:17 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:16:17 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Purchase.html (大小: 8082 bytes)
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Purchase.html
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: WindowsView.xlsx
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:16:18 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\WindowsView.html (大小: 24164 bytes)
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\WindowsView.html
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:16:19 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: MatchModel.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: 游戏常量.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 15:16:22 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\游戏常量.html (大小: 4481 bytes)
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\游戏常量.html
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:16:23 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\DartRivalsProgressRewards.html
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 15:16:24 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 15:16:25 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:25 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:25 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 15:16:25 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: MatchModelV2.xlsx
2025-07-29 15:16:32 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:16:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:16:38 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:16:38 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:16:38 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\MatchModelV2.html
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:16:39 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 15:16:40 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Toy_Match_3D_埋点方案.html
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:16:41 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: [调用]多语言.xlsx
2025-07-29 15:16:42 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:16:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:16:43 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 15:16:43 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 15:16:43 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\[调用]多语言.html
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:16:44 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: Badge.xlsx
2025-07-29 15:16:45 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:16:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:16:45 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Badge.html (大小: 7178 bytes)
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\Badge.html
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:16:46 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\DartRivalsProgress.html
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 15:16:47 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: CardBadge.xlsx
2025-07-29 15:16:48 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:16:48 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:16:48 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\CardBadge.html (大小: 3863 bytes)
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\CardBadge.html
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 15:16:49 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:16:50 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753773317_a58704ee\MainInterfaceShow.html
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 15:16:51 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753773317_a58704ee
2025-07-29 15:24:58 - SimpleConfigCompare-TEST - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:24:58 - SimpleConfigCompare-TEST - INFO - 配置加载成功: TEST
2025-07-29 15:27:28 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:27:28 - SimpleConfigCompare-TM3D - INFO - SimpleConfigCompareService 版本: 2025-01-28-v2-with-chinese-translation
2025-07-29 15:27:28 - SimpleConfigCompare-TM3D - INFO - 配置加载成功: TM3D
2025-07-29 15:27:28 - SimpleConfigCompare-TM3D - INFO - 进度: 0% - 开始配置对比...
2025-07-29 15:27:28 - SimpleConfigCompare-TM3D - INFO - 进度: 10% - 初始化GitLab客户端...
2025-07-29 15:27:29 - SimpleConfigCompare-TM3D - INFO - GitLab客户端初始化成功
2025-07-29 15:27:29 - SimpleConfigCompare-TM3D - INFO - 进度: 20% - 获取开始时间点提交...
2025-07-29 15:27:29 - SimpleConfigCompare-TM3D - INFO - 找到提交: 92b7c69e - theme 25 models
2025-07-29 15:27:29 - SimpleConfigCompare-TM3D - INFO - 进度: 30% - 获取结束时间点提交...
2025-07-29 15:27:29 - SimpleConfigCompare-TM3D - INFO - 找到提交: 17b6b34d - fixbug 【商店】商店礼包展示异常
2025-07-29 15:27:29 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 获取变更文件列表...
2025-07-29 15:27:29 - SimpleConfigCompare-TM3D - INFO - 获取变更文件列表...
2025-07-29 15:27:29 - SimpleConfigCompare-TM3D - INFO - 找到 10 个相关提交
2025-07-29 15:28:31 - SimpleConfigCompare-TM3D - INFO - 最终找到 18 个Excel文件变更
2025-07-29 15:28:31 - SimpleConfigCompare-TM3D - INFO - 进度: 40% - 处理文件: MainInterfaceShow.xlsx
2025-07-29 15:28:31 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:28:31 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:28:31 - SimpleConfigCompare-TM3D - INFO - 执行对比: MainInterfaceShow
2025-07-29 15:28:32 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:32 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\MainInterfaceShow.html (大小: 9695 bytes)
2025-07-29 15:28:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\MainInterfaceShow.html
2025-07-29 15:28:32 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/MainInterfaceShow.xlsx
2025-07-29 15:28:32 - SimpleConfigCompare-TM3D - INFO - 进度: 42% - 处理文件: CardBadge.xlsx
2025-07-29 15:28:32 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:28:32 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:28:32 - SimpleConfigCompare-TM3D - INFO - 执行对比: CardBadge
2025-07-29 15:28:33 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:33 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\CardBadge.html (大小: 3863 bytes)
2025-07-29 15:28:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\CardBadge.html
2025-07-29 15:28:33 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/cardsysNew/CardBadge.xlsx
2025-07-29 15:28:33 - SimpleConfigCompare-TM3D - INFO - 进度: 45% - 处理文件: DartRivalsProgress.xlsx
2025-07-29 15:28:33 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:28:33 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:28:33 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgress
2025-07-29 15:28:34 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:34 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\DartRivalsProgress.html (大小: 10811 bytes)
2025-07-29 15:28:34 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\DartRivalsProgress.html
2025-07-29 15:28:34 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgress.xlsx
2025-07-29 15:28:34 - SimpleConfigCompare-TM3D - INFO - 进度: 48% - 处理文件: WindowsView.xlsx
2025-07-29 15:28:34 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:28:34 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:28:34 - SimpleConfigCompare-TM3D - INFO - 执行对比: WindowsView
2025-07-29 15:28:35 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:35 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\WindowsView.html (大小: 24164 bytes)
2025-07-29 15:28:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\WindowsView.html
2025-07-29 15:28:35 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Common/WindowsView.xlsx
2025-07-29 15:28:35 - SimpleConfigCompare-TM3D - INFO - 进度: 51% - 处理文件: Purchase.xlsx
2025-07-29 15:28:35 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:28:36 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:28:36 - SimpleConfigCompare-TM3D - INFO - 执行对比: Purchase
2025-07-29 15:28:36 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:36 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\Purchase.html (大小: 8082 bytes)
2025-07-29 15:28:36 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\Purchase.html
2025-07-29 15:28:36 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/ActivityPurchase/Purchase.xlsx
2025-07-29 15:28:36 - SimpleConfigCompare-TM3D - INFO - 进度: 53% - 处理文件: [调用]多语言.xlsx
2025-07-29 15:28:38 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:28:39 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:28:39 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: [调用]多语言
2025-07-29 15:28:39 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\call_multilang_new
2025-07-29 15:28:39 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 调用_多语言
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\[调用]多语言.html (大小: 1464968 bytes)
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\[调用]多语言.html
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Language/[调用]多语言.xlsx
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 进度: 56% - 处理文件: packbaseNew_S9.xlsx
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/packbaseNew_S9.xlsx
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 进度: 59% - 处理文件: 游戏常量.xlsx
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: 游戏常量
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\file_ab5b351b_new
2025-07-29 15:28:40 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: 游戏常量
2025-07-29 15:28:41 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:41 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\游戏常量.html (大小: 4481 bytes)
2025-07-29 15:28:41 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\游戏常量.html
2025-07-29 15:28:41 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Constant/游戏常量.xlsx
2025-07-29 15:28:41 - SimpleConfigCompare-TM3D - INFO - 进度: 62% - 处理文件: MatchModel.xlsx
2025-07-29 15:28:43 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModel.xlsx
2025-07-29 15:28:43 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/Models/MatchModel.xlsx@17b6b34d35902681f7f8f342533b5798075430b3: 404: 404 File Not Found
2025-07-29 15:28:43 - SimpleConfigCompare-TM3D - WARNING - 处理新版本失败: toymatch3d/Excels/Config/Models/MatchModel.xlsx - 404: 404 File Not Found
2025-07-29 15:28:43 - SimpleConfigCompare-TM3D - INFO - 进度: 65% - 处理文件: WildexchangeNew_S9.xlsx
2025-07-29 15:28:43 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:28:43 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:28:43 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/WildexchangeNew_S9.xlsx
2025-07-29 15:28:43 - SimpleConfigCompare-TM3D - INFO - 进度: 67% - 处理文件: DartRivalsProgressRewards.xlsx
2025-07-29 15:28:44 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:28:44 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:28:44 - SimpleConfigCompare-TM3D - INFO - 执行对比: DartRivalsProgressRewards
2025-07-29 15:28:45 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:45 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\DartRivalsProgressRewards.html (大小: 15166 bytes)
2025-07-29 15:28:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\DartRivalsProgressRewards.html
2025-07-29 15:28:45 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/DartRivals/DartRivalsProgressRewards.xlsx
2025-07-29 15:28:45 - SimpleConfigCompare-TM3D - INFO - 进度: 70% - 处理文件: cardbaseNew_S9.xlsx
2025-07-29 15:28:45 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:28:45 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:28:45 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardbaseNew_S9.xlsx
2025-07-29 15:28:45 - SimpleConfigCompare-TM3D - INFO - 进度: 73% - 处理文件: MatchModelV2.xlsx
2025-07-29 15:28:50 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:28:50 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:28:55 - SimpleConfigCompare-TM3D - WARNING - 工作表 Sheet2 为空，跳过
2025-07-29 15:28:55 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:28:55 - SimpleConfigCompare-TM3D - INFO - 执行对比: MatchModelV2
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\MatchModelV2.html (大小: 108455 bytes)
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\MatchModelV2.html
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Models/MatchModelV2.xlsx
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - INFO - 进度: 76% - 处理文件: setbaseNew_S9.xlsx
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/setbaseNew_S9.xlsx
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - INFO - 进度: 78% - 处理文件: Badge.xlsx
2025-07-29 15:28:56 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:28:57 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:28:57 - SimpleConfigCompare-TM3D - INFO - 执行对比: Badge
2025-07-29 15:28:57 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:28:57 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\Badge.html (大小: 7178 bytes)
2025-07-29 15:28:57 - SimpleConfigCompare-TM3D - INFO - 对比完成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\Badge.html
2025-07-29 15:28:57 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Config/Avatar/Badge.xlsx
2025-07-29 15:28:57 - SimpleConfigCompare-TM3D - INFO - 进度: 81% - 处理文件: newcardconfig_S9.xlsx
2025-07-29 15:28:58 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:28:58 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:28:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/newcardconfig_S9.xlsx
2025-07-29 15:28:58 - SimpleConfigCompare-TM3D - INFO - 进度: 84% - 处理文件: cardrewardNew_S9.xlsx
2025-07-29 15:28:58 - SimpleConfigCompare-TM3D - ERROR - 下载文件失败 toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx@92b7c69e753f9c74a513cc0f845831e4abf2cc84: 404: 404 File Not Found
2025-07-29 15:28:58 - SimpleConfigCompare-TM3D - WARNING - 处理旧版本失败: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx - 404: 404 File Not Found
2025-07-29 15:28:58 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Config/cardsysNew/S9/cardrewardNew_S9.xlsx
2025-07-29 15:28:58 - SimpleConfigCompare-TM3D - INFO - 进度: 87% - 处理文件: Toy Match 3D_埋点方案.xlsx
2025-07-29 15:28:58 - SimpleConfigCompare-TM3D - INFO - 处理旧版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:28:59 - SimpleConfigCompare-TM3D - INFO - 处理新版本完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:28:59 - SimpleConfigCompare-TM3D - INFO - 检测到路径包含特殊字符，使用安全路径方案: Toy_Match_3D_埋点方案
2025-07-29 15:28:59 - SimpleConfigCompare-TM3D - INFO - 复制文件到安全路径: C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_old, C:\Users\<USER>\AppData\Local\Temp\bcompare_safe\Toy_Match_3D_trackingplan_new
2025-07-29 15:28:59 - SimpleConfigCompare-TM3D - INFO - 使用安全路径执行对比: Toy_Match_3D_埋点方案
2025-07-29 15:29:00 - SimpleConfigCompare-TM3D - INFO - BCompare执行成功: 成功
2025-07-29 15:29:00 - SimpleConfigCompare-TM3D - INFO - 报告文件已生成: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\Toy_Match_3D_埋点方案.html (大小: 6897 bytes)
2025-07-29 15:29:00 - SimpleConfigCompare-TM3D - INFO - 安全路径对比成功: D:\git\qa_tools\static\reports\TM3D_qa_test_3.29.0_1753774048_6c8cb85c\Toy_Match_3D_埋点方案.html
2025-07-29 15:29:00 - SimpleConfigCompare-TM3D - INFO - 对比完成: toymatch3d/Excels/Analytics/Toy Match 3D_埋点方案.xlsx
2025-07-29 15:29:00 - SimpleConfigCompare-TM3D - INFO - 进度: 100% - 对比完成
2025-07-29 15:29:00 - SimpleConfigCompare-TM3D - INFO - 数据库表初始化完成 - Schema: tm3d
2025-07-29 15:29:00 - SimpleConfigCompare-TM3D - INFO - 历史记录已保存到数据库 - Schema: tm3d, Task: TM3D_qa_test_3.29.0_1753774048_6c8cb85c
2025-07-29 15:29:08 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:29:11 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:29:15 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:29:18 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:29:23 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 15:29:27 - qa_platform - INFO - 获取到连接设备: []
2025-07-29 16:00:25 - qa_platform - INFO - 获取到连接设备: ['45170DLAQ001LL']
2025-07-29 16:00:28 - jenkins_service - INFO - 获取Jenkins任务列表: http://172.31.60.29:8080/
2025-07-29 16:00:28 - qa_platform - INFO - 获取到连接设备: ['45170DLAQ001LL']
2025-07-29 16:00:28 - jenkins_service - INFO - 获取到Jenkins任务: ['Merge_Android_Debug', 'Merge_iOS_Debug', 'ToyMatch3d_Android', 'ToyMatch3d_Android_Model', 'ToyMatch3d_Android_Release', 'ToyMatch3d_IOS', 'ToyMatch3d_IOS_Release', 'robot_test']
2025-07-29 16:00:29 - qa_platform - INFO - 获取到连接设备: ['45170DLAQ001LL']
2025-07-29 16:00:31 - jenkins_service - INFO - 获取任务 ToyMatch3d_Android 的安装包
2025-07-29 16:00:31 - jenkins_service - INFO - 获取任务 ToyMatch3d_Android 的安装包，URL: http://172.31.60.29:8080/job/ToyMatch3d_Android/ws/
2025-07-29 16:00:31 - jenkins_service - INFO - 找到安装包: TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk (位于: 【2795】qa_test_3.28.0_Debug)
2025-07-29 16:00:32 - jenkins_service - INFO - 找到安装包: TM3D_qa_test_3.29.0_07241832_v3.29.0_Debug.apk (位于: 【2797】qa_test_3.29.0_Debug)
2025-07-29 16:00:32 - jenkins_service - INFO - 找到安装包: TM3D_qa_test_3.29.0_07281206_v3.29.0_Debug.apk (位于: 【2804】qa_test_3.29.0_Debug)
2025-07-29 16:00:32 - jenkins_service - INFO - 找到安装包: TM3D_qa_test_3.29.0_07281536_v8.8.8_Debug.apk (位于: 【2805】qa_test_3.29.0_Debug)
2025-07-29 16:00:32 - jenkins_service - INFO - 任务 ToyMatch3d_Android 获取到 4 个安装包: [{'name': 'TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk', 'path': '【2795】qa_test_3.28.0_Debug/TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk', 'url': 'http://172.31.60.29:8080/job/ToyMatch3d_Android/ws/%E3%80%902795%E3%80%91qa_test_3.28.0_Debug/TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk', 'directory': '【2795】qa_test_3.28.0_Debug'}, {'name': 'TM3D_qa_test_3.29.0_07241832_v3.29.0_Debug.apk', 'path': '【2797】qa_test_3.29.0_Debug/TM3D_qa_test_3.29.0_07241832_v3.29.0_Debug.apk', 'url': 'http://172.31.60.29:8080/job/ToyMatch3d_Android/ws/%E3%80%902797%E3%80%91qa_test_3.29.0_Debug/TM3D_qa_test_3.29.0_07241832_v3.29.0_Debug.apk', 'directory': '【2797】qa_test_3.29.0_Debug'}, {'name': 'TM3D_qa_test_3.29.0_07281206_v3.29.0_Debug.apk', 'path': '【2804】qa_test_3.29.0_Debug/TM3D_qa_test_3.29.0_07281206_v3.29.0_Debug.apk', 'url': 'http://172.31.60.29:8080/job/ToyMatch3d_Android/ws/%E3%80%902804%E3%80%91qa_test_3.29.0_Debug/TM3D_qa_test_3.29.0_07281206_v3.29.0_Debug.apk', 'directory': '【2804】qa_test_3.29.0_Debug'}, {'name': 'TM3D_qa_test_3.29.0_07281536_v8.8.8_Debug.apk', 'path': '【2805】qa_test_3.29.0_Debug/TM3D_qa_test_3.29.0_07281536_v8.8.8_Debug.apk', 'url': 'http://172.31.60.29:8080/job/ToyMatch3d_Android/ws/%E3%80%902805%E3%80%91qa_test_3.29.0_Debug/TM3D_qa_test_3.29.0_07281536_v8.8.8_Debug.apk', 'directory': '【2805】qa_test_3.29.0_Debug'}]
2025-07-29 16:00:48 - request_service_TM3D - INFO - 成功加载项目 'TM3D' 的post_gm配置
2025-07-29 16:00:48 - request_service_TM3D - INFO - 发送POST请求到: https://gm-pre-front.playdayy.cn/backend/admin/user/reset
2025-07-29 16:00:51 - request_service_TM3D - INFO - 响应状态码: 200
2025-07-29 16:00:51 - request_service_TM3D - INFO - 响应体: {
  "errno": 0,
  "errmsg": "",
  "data": {
    "GameService": {
      "LogEntity": {
        "appName": "tm3d",
        "fid": "0_daaca2075df3db294bd3f54466d18120",
        "remark": "pixel9",
        "userId": 48,
        "userName": "尹鹏",
        "activity": "resetUser",
        "content": "【重置tm3d游戏账户（Pre环境）】:用户尹鹏刚刚重置了firebaseId：0_daaca2075df3db294bd3f54466d18120的用户信息，服务器时间：[2025-09-14T08:00:56.895Z]。 备注信息： pixel9"
      }
    }
  }
}
2025-07-29 16:00:51 - test_runner - INFO - 开始测试: Project=TM3D, Job=ToyMatch3d_Android, Package=【2795】qa_test_3.28.0_Debug/TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk, Case=newuser_guide, Device=45170DLAQ001LL
2025-07-29 16:00:51 - test_runner - INFO - 🟢 全局停止标志已重置
2025-07-29 16:00:51 - test_runner - INFO - 开始自动下载和安装应用...
2025-07-29 16:00:51 - test_runner - INFO - 下载安装包: TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk -> ./downloads\TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk
2025-07-29 16:00:51 - jenkins_service - INFO - 开始下载安装包: http://172.31.60.29:8080/job/ToyMatch3d_Android/ws/%E3%80%902795%E3%80%91qa_test_3.28.0_Debug/TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk
2025-07-29 16:01:07 - jenkins_service - INFO - 安装包下载成功: 【2795】qa_test_3.28.0_Debug/TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk -> ./downloads\TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk
2025-07-29 16:01:07 - test_runner - INFO - 卸载应用: com.toy.match.game
2025-07-29 16:01:07 - base_test - INFO - 应用卸载成功: com.toy.match.game
2025-07-29 16:01:07 - test_runner - INFO - 安装应用到设备: 45170DLAQ001LL
2025-07-29 16:01:08 - qa_platform - INFO - 获取到连接设备: ['45170DLAQ001LL']
2025-07-29 16:01:08 - base_test - INFO - 开始安装应用: ./downloads\TM3D_qa_test_3.28.0_07181728_v3.28.10_Debug.apk -> 45170DLAQ001LL
2025-07-29 16:01:31 - base_test - INFO - 应用安装成功: 45170DLAQ001LL
2025-07-29 16:01:31 - test_runner - INFO - 启动应用: com.toy.match.game
2025-07-29 16:01:31 - qa_platform - INFO - 获取到连接设备: ['45170DLAQ001LL']
2025-07-29 16:01:31 - base_test - INFO - 启动应用: com.toy.match.game on 45170DLAQ001LL
2025-07-29 16:01:32 - base_test - INFO - 应用启动成功: com.toy.match.game
2025-07-29 16:01:35 - test_runner - INFO - 🚀 测试用例 newuser_guide 已启动，开始执行...
2025-07-29 16:01:35 - test_runner - INFO - 清理设备 45170DLAQ001LL 的旧连接...
2025-07-29 16:01:35 - test_runner - INFO - 旧连接清理完成
2025-07-29 16:01:37 - test_runner - INFO - 为设备 45170DLAQ001LL 创建Poco连接...
2025-07-29 16:01:37 - DeviceTestManager_45170DLAQ001LL - INFO - 设备 45170DLAQ001LL 收到 android Poco启动请求
2025-07-29 16:01:37 - DeviceTestManager_45170DLAQ001LL - INFO - 设备 45170DLAQ001LL 开始测试会话
2025-07-29 16:01:37 - DeviceTestManager_45170DLAQ001LL - INFO - 设备 45170DLAQ001LL 创建 android Poco连接
2025-07-29 16:01:39 - android_privacy_handler - INFO - AndroidPoco连接成功: 45170DLAQ001LL
2025-07-29 16:01:39 - DeviceTestManager_45170DLAQ001LL - INFO - 设备 45170DLAQ001LL android Poco连接创建成功
2025-07-29 16:01:39 - PocoManager - INFO - 设备 45170DLAQ001LL 测试已启动，Poco类型: android
2025-07-29 16:01:39 - test_runner - INFO - Android Poco连接已创建，用于系统级操作
2025-07-29 16:01:39 - android_privacy_handler - INFO - 等待元素出现: android.widget.FrameLayout -> com.toy.match.game:id/acceptBtn, 超时: 50秒
2025-07-29 16:02:30 - android_privacy_handler - WARNING - 等待元素超时: android.widget.FrameLayout -> com.toy.match.game:id/acceptBtn
2025-07-29 16:02:30 - test_runner - INFO - 等待游戏启动，准备创建Unity Poco连接...
2025-07-29 16:02:40 - test_runner - INFO - 尝试创建Unity Poco连接 (第1/3次)...
2025-07-29 16:02:40 - DeviceTestManager_45170DLAQ001LL - INFO - 设备 45170DLAQ001LL 收到 unity Poco启动请求
2025-07-29 16:02:40 - DeviceTestManager_45170DLAQ001LL - INFO - 设备 45170DLAQ001LL 创建 unity Poco连接
2025-07-29 16:02:40 - poco_universal - INFO - 等待Unity游戏启动和Poco服务初始化(30秒)...
2025-07-29 16:02:40 - poco_universal - INFO - Unity Poco服务通常需要15-20秒完全启动，请耐心等待...
2025-07-29 16:03:10 - poco_universal - INFO - 开始连接Unity Poco服务 (最大重试8次，间隔3秒)
2025-07-29 16:03:10 - poco_universal - INFO - 尝试连接Unity Poco (第1/8次)
2025-07-29 16:03:10 - poco_universal - INFO - 目标设备: 45170DLAQ001LL
2025-07-29 16:03:10 - poco_universal - INFO - ADB设备连接成功: 45170DLAQ001LL
2025-07-29 16:03:10 - poco_universal - INFO - 创建Unity Poco实例...
2025-07-29 16:03:10 - poco_universal - INFO - Unity Poco连接测试完成，服务可用
2025-07-29 16:03:14 - poco_universal - INFO - Unity连接稳定性初始化完成
2025-07-29 16:03:14 - poco_universal - INFO - Unity Poco连接成功! 设备: 45170DLAQ001LL
2025-07-29 16:03:14 - DeviceTestManager_45170DLAQ001LL - INFO - 设备 45170DLAQ001LL unity Poco连接创建成功
2025-07-29 16:03:14 - PocoManager - INFO - 设备 45170DLAQ001LL 测试已启动，Poco类型: unity
2025-07-29 16:03:14 - test_runner - INFO - 验证Unity Poco连接稳定性...
2025-07-29 16:03:17 - test_runner - INFO - Unity Poco基础连接验证成功，屏幕尺寸: [1080.0, 2424.0]
2025-07-29 16:03:17 - poco_universal - INFO - 开始验证Unity Poco连接稳定性（3秒测试）...
2025-07-29 16:03:20 - poco_universal - INFO - 连接稳定性测试完成: 成功率 100.0% (8/8)
2025-07-29 16:03:20 - poco_universal - INFO - 连接稳定性验证通过: 100.0%
2025-07-29 16:03:20 - test_runner - INFO - Unity Poco连接稳定性验证通过
2025-07-29 16:03:20 - test_runner - INFO - 执行额外连接健康检查...
2025-07-29 16:03:20 - poco_universal - INFO - 执行测试前连接检查...
2025-07-29 16:03:20 - poco_universal - INFO - 连接测试通过，屏幕尺寸: [1080.0, 2424.0]
2025-07-29 16:03:20 - test_runner - INFO - Unity Poco连接健康检查通过
2025-07-29 16:03:20 - test_runner - INFO - Unity Poco连接已创建并验证成功，用于游戏内操作
2025-07-29 16:03:21 - jenkins_service - INFO - 获取任务 ToyMatch3d_Android 的最新构建号，URL: http://172.31.60.29:8080/job/ToyMatch3d_Android/api/json
2025-07-29 16:03:21 - jenkins_service - INFO - 任务 ToyMatch3d_Android 的最新构建号: 2809
2025-07-29 16:03:21 - PerformanceMonitor-TM3D - INFO - 开始监控应用性能: com.toy.match.game
2025-07-29 16:03:21 - PerformanceMonitor-TM3D - INFO - 性能监控已启动，会话ID: TM3D_20250729_160321
2025-07-29 16:03:21 - test_runner - INFO - 性能监控已启动
2025-07-29 16:03:21 - test_runner - INFO - 开始执行测试用例: newuser_guide
2025-07-29 16:03:21 - test_runner - INFO - 设备ID: 45170DLAQ001LL
2025-07-29 16:03:21 - test_runner - INFO - 测试参数: {'operation': 'reset', 'test_param': {'appName': 'tm3d', 'fid': '0_daaca2075df3db294bd3f54466d18120', 'remark': 'pixel9'}}
2025-07-29 16:03:21 - test_runner - INFO - 成功加载测试用例模块: services.airtest_func.testcase.newuser_guide
2025-07-29 16:03:21 - test_runner - INFO - 可用的Poco连接: Android=是, Unity=是
2025-07-29 16:03:21 - test_runner - INFO - 测试用例开始前检查Unity Poco连接...
2025-07-29 16:03:21 - poco_universal - INFO - 执行测试前连接检查...
2025-07-29 16:03:21 - poco_universal - INFO - 连接测试通过，屏幕尺寸: [1080.0, 2424.0]
2025-07-29 16:03:21 - test_runner - INFO - 使用函数 newuser_guide 执行测试用例
2025-07-29 16:03:21 - test_runner - INFO - 调用测试用例 newuser_guide，参数: ['device_id', 'test_data']
2025-07-29 16:03:21 - test_runner - INFO - 使用新模式调用测试用例，传递参数: ['device_id', 'test_data']
2025-07-29 16:03:21 - test_runner - INFO - 🎮 开始新手引导测试
2025-07-29 16:03:21 - test_runner - INFO - 🏆 初始化新手引导对象
2025-07-29 16:03:21 - test_runner - INFO - 🏆第1关
2025-07-29 16:03:21 - test_runner - INFO - 🛫<开始新手引导第1关测试>
2025-07-29 16:03:21 - poco_universal - INFO - 执行点击操作: PendingArticleHangPoint -> tiger1_orange1
2025-07-29 16:03:21 - PerformanceMonitor-TM3D - INFO - 正在检查设备功能支持情况...
2025-07-29 16:03:24 - PerformanceMonitor-TM3D - INFO - ✅ 支持的功能: cpu_frequencies
2025-07-29 16:03:24 - PerformanceMonitor-TM3D - INFO - ❌ 不支持的功能: gpu_frequency, gpu_utilization, gpu_render_info
2025-07-29 16:03:25 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.9, CPU: 88.9%, 内存: 938.02MB
2025-07-29 16:03:25 - poco_universal - INFO - 点击成功: PendingArticleHangPoint -> tiger1_orange1
2025-07-29 16:03:26 - poco_universal - INFO - 执行点击操作: PendingArticleHangPoint -> tiger1_orange1
2025-07-29 16:03:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.1, CPU: 14.88%, 内存: 937.31MB
2025-07-29 16:03:28 - poco_universal - INFO - 点击成功: PendingArticleHangPoint -> tiger1_orange1
2025-07-29 16:03:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.6, CPU: 14.25%, 内存: 936.92MB
2025-07-29 16:03:29 - poco_universal - INFO - 执行点击操作: PendingArticleHangPoint -> tiger1_orange1
2025-07-29 16:03:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.8, CPU: 15.65%, 内存: 937.68MB
2025-07-29 16:03:31 - poco_universal - INFO - 点击成功: PendingArticleHangPoint -> tiger1_orange1
2025-07-29 16:03:32 - poco_universal - INFO - 等待并点击元素: UIMgr -> des_txtPro, 超时: 180秒
2025-07-29 16:03:32 - poco_universal - INFO - 元素出现成功: UIMgr -> des_txtPro, 等待时间: 0.4秒
2025-07-29 16:03:32 - poco_universal - INFO - 开始点击元素: UIMgr -> des_txtPro
2025-07-29 16:03:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.9, CPU: 16.23%, 内存: 941.69MB
2025-07-29 16:03:34 - poco_universal - INFO - 点击成功: UIMgr -> des_txtPro
2025-07-29 16:03:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.7, CPU: 14.88%, 内存: 942.76MB
2025-07-29 16:03:35 - poco_universal - INFO - 执行点击操作: PendingArticleHangPoint -> frog1_green1
2025-07-29 16:03:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.7, CPU: 15.22%, 内存: 942.52MB
2025-07-29 16:03:37 - poco_universal - INFO - 点击成功: PendingArticleHangPoint -> frog1_green1
2025-07-29 16:03:38 - poco_universal - INFO - 执行点击操作: PendingArticleHangPoint -> frog1_green1
2025-07-29 16:03:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.5, CPU: 15.71%, 内存: 941.84MB
2025-07-29 16:03:40 - poco_universal - INFO - 点击成功: PendingArticleHangPoint -> frog1_green1
2025-07-29 16:03:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.1, CPU: 15.66%, 内存: 941.82MB
2025-07-29 16:03:41 - poco_universal - INFO - 执行点击操作: PendingArticleHangPoint -> frog1_green1
2025-07-29 16:03:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.5, CPU: 15.46%, 内存: 941.96MB
2025-07-29 16:03:43 - poco_universal - INFO - 点击成功: PendingArticleHangPoint -> frog1_green1
2025-07-29 16:03:44 - poco_universal - INFO - 执行点击操作: PendingArticleHangPoint -> unicorn1_pink1
2025-07-29 16:03:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.5, CPU: 14.96%, 内存: 944.75MB
2025-07-29 16:03:46 - poco_universal - INFO - 点击成功: PendingArticleHangPoint -> unicorn1_pink1
2025-07-29 16:03:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.6, CPU: 15.25%, 内存: 946.46MB
2025-07-29 16:03:47 - poco_universal - INFO - 执行点击操作: PendingArticleHangPoint -> unicorn1_pink1
2025-07-29 16:03:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.6, CPU: 15.1%, 内存: 951.73MB
2025-07-29 16:03:49 - poco_universal - INFO - 点击成功: PendingArticleHangPoint -> unicorn1_pink1
2025-07-29 16:03:50 - poco_universal - INFO - 执行点击操作: PendingArticleHangPoint -> unicorn1_pink1
2025-07-29 16:03:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.8, CPU: 15.04%, 内存: 955.82MB
2025-07-29 16:03:52 - poco_universal - INFO - 点击成功: PendingArticleHangPoint -> unicorn1_pink1
2025-07-29 16:03:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.1, CPU: 16.38%, 内存: 966.15MB
2025-07-29 16:03:53 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:03:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.1, CPU: 17.55%, 内存: 976.28MB
2025-07-29 16:03:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.1, CPU: 15.13%, 内存: 976.56MB
2025-07-29 16:03:57 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 4.1秒
2025-07-29 16:03:57 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:03:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.8, CPU: 15.78%, 内存: 978.38MB
2025-07-29 16:03:59 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:04:00 - test_runner - INFO - 🏆第2关
2025-07-29 16:04:00 - test_runner - INFO - 🛫<开始新手引导第2关测试>
2025-07-29 16:04:00 - poco_universal - INFO - 等待并点击元素: UIMgr -> des_txtPro, 超时: 180秒
2025-07-29 16:04:00 - poco_universal - INFO - 元素出现成功: UIMgr -> des_txtPro, 等待时间: 0.5秒
2025-07-29 16:04:00 - poco_universal - INFO - 开始点击元素: UIMgr -> des_txtPro
2025-07-29 16:04:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.2, CPU: 16.19%, 内存: 982.43MB
2025-07-29 16:04:02 - poco_universal - INFO - 点击成功: UIMgr -> des_txtPro
2025-07-29 16:04:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.0, CPU: 15.58%, 内存: 982.72MB
2025-07-29 16:04:05 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:04:05 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.8, CPU: 15.17%, 内存: 981.92MB
2025-07-29 16:04:05 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.4秒
2025-07-29 16:04:05 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:04:07 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.5, CPU: 14.68%, 内存: 982.63MB
2025-07-29 16:04:07 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:04:08 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:04:09 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.4, CPU: 16.09%, 内存: 982.29MB
2025-07-29 16:04:11 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.5, CPU: 15.72%, 内存: 982.75MB
2025-07-29 16:04:13 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.7, CPU: 17.18%, 内存: 983.05MB
2025-07-29 16:04:15 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.3, CPU: 15.83%, 内存: 982.28MB
2025-07-29 16:04:17 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.1, CPU: 16.01%, 内存: 984.65MB
2025-07-29 16:04:19 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.5, CPU: 17.02%, 内存: 985.46MB
2025-07-29 16:04:21 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 12.9秒
2025-07-29 16:04:21 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:04:21 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.8, CPU: 14.55%, 内存: 985.48MB
2025-07-29 16:04:23 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:04:23 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.1, CPU: 16.39%, 内存: 988.19MB
2025-07-29 16:04:24 - test_runner - INFO - 🏆第3关
2025-07-29 16:04:24 - test_runner - INFO - 🛫回大厅开启第3关，开启磁铁引导
2025-07-29 16:04:24 - poco_universal - INFO - 等待并点击元素: singleBtn_btn, 超时: 180秒
2025-07-29 16:04:25 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.0, CPU: 20.1%, 内存: 977.1MB
2025-07-29 16:04:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.8, CPU: 13.5%, 内存: 977.0MB
2025-07-29 16:04:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.8, CPU: 12.08%, 内存: 978.88MB
2025-07-29 16:04:30 - poco_universal - INFO - 元素出现成功: singleBtn_btn, 等待时间: 6.4秒
2025-07-29 16:04:30 - poco_universal - INFO - 开始点击元素: singleBtn_btn
2025-07-29 16:04:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.7, CPU: 13.19%, 内存: 980.49MB
2025-07-29 16:04:32 - poco_universal - INFO - 点击成功: singleBtn_btn
2025-07-29 16:04:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.2, CPU: 13.18%, 内存: 981.12MB
2025-07-29 16:04:35 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:04:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.9, CPU: 11.55%, 内存: 980.86MB
2025-07-29 16:04:35 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:04:35 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:04:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.7, CPU: 12.04%, 内存: 980.88MB
2025-07-29 16:04:37 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:04:38 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:04:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.0, CPU: 11.54%, 内存: 981.4MB
2025-07-29 16:04:40 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:04:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.3, CPU: 13.07%, 内存: 985.2MB
2025-07-29 16:04:41 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:04:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.2, CPU: 11.44%, 内存: 985.29MB
2025-07-29 16:04:44 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:04:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.9, CPU: 14.86%, 内存: 988.33MB
2025-07-29 16:04:45 - poco_universal - INFO - 等待并点击元素: UIMgr -> des_txtPro, 超时: 180秒
2025-07-29 16:04:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.2, CPU: 17.9%, 内存: 1012.98MB
2025-07-29 16:04:47 - poco_universal - INFO - 元素出现成功: UIMgr -> des_txtPro, 等待时间: 2.4秒
2025-07-29 16:04:47 - poco_universal - INFO - 开始点击元素: UIMgr -> des_txtPro
2025-07-29 16:04:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.7, CPU: 16.16%, 内存: 1014.12MB
2025-07-29 16:04:49 - poco_universal - INFO - 点击成功: UIMgr -> des_txtPro
2025-07-29 16:04:50 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> Combat1_btn, 超时: 180秒
2025-07-29 16:04:50 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> Combat1_btn, 等待时间: 0.5秒
2025-07-29 16:04:50 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> Combat1_btn
2025-07-29 16:04:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.0, CPU: 16.66%, 内存: 1013.74MB
2025-07-29 16:04:52 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> Combat1_btn
2025-07-29 16:04:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.6, CPU: 17.39%, 内存: 1014.66MB
2025-07-29 16:04:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.5, CPU: 15.94%, 内存: 1014.45MB
2025-07-29 16:04:55 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:04:56 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.5秒
2025-07-29 16:04:56 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:04:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.2, CPU: 15.23%, 内存: 1014.52MB
2025-07-29 16:04:57 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:04:58 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:04:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.3, CPU: 16.1%, 内存: 1014.53MB
2025-07-29 16:05:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.6, CPU: 17.45%, 内存: 1014.51MB
2025-07-29 16:05:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 32.6, CPU: 17.14%, 内存: 1014.62MB
2025-07-29 16:05:05 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.0, CPU: 17.43%, 内存: 1014.76MB
2025-07-29 16:05:07 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.6, CPU: 17.49%, 内存: 1014.76MB
2025-07-29 16:05:09 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.3, CPU: 16.5%, 内存: 1017.21MB
2025-07-29 16:05:11 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.8, CPU: 16.59%, 内存: 1021.58MB
2025-07-29 16:05:13 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 14.4秒
2025-07-29 16:05:13 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:05:13 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.8, CPU: 14.13%, 内存: 1021.13MB
2025-07-29 16:05:14 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:05:15 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.6, CPU: 15.93%, 内存: 1021.71MB
2025-07-29 16:05:15 - test_runner - INFO - 🏆第4关
2025-07-29 16:05:15 - test_runner - INFO - 🛫回大厅开启第4关
2025-07-29 16:05:17 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.6, CPU: 16.23%, 内存: 996.35MB
2025-07-29 16:05:17 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:05:18 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:05:18 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:05:19 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.6, CPU: 12.13%, 内存: 996.0MB
2025-07-29 16:05:20 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:05:21 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.6, CPU: 12.01%, 内存: 995.89MB
2025-07-29 16:05:21 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:05:23 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.6, CPU: 12.68%, 内存: 996.22MB
2025-07-29 16:05:23 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:05:24 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:05:25 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.3, CPU: 11.62%, 内存: 996.09MB
2025-07-29 16:05:26 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:05:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.0, CPU: 12.89%, 内存: 1001.14MB
2025-07-29 16:05:27 - poco_universal - INFO - 等待并点击元素: UIMgr -> green_btn, 超时: 180秒
2025-07-29 16:05:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 55.5, CPU: 18.84%, 内存: 1025.89MB
2025-07-29 16:05:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.5, CPU: 19.22%, 内存: 1025.33MB
2025-07-29 16:05:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.4, CPU: 18.42%, 内存: 1024.91MB
2025-07-29 16:05:33 - poco_universal - INFO - 元素出现成功: UIMgr -> green_btn, 等待时间: 6.0秒
2025-07-29 16:05:33 - poco_universal - INFO - 开始点击元素: UIMgr -> green_btn
2025-07-29 16:05:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 57.3, CPU: 19.76%, 内存: 1025.87MB
2025-07-29 16:05:35 - poco_universal - INFO - 点击成功: UIMgr -> green_btn
2025-07-29 16:05:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.6, CPU: 17.84%, 内存: 1025.3MB
2025-07-29 16:05:38 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:05:39 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:05:39 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:05:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 56.2, CPU: 18.39%, 内存: 1026.02MB
2025-07-29 16:05:41 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:05:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.2, CPU: 18.18%, 内存: 1025.81MB
2025-07-29 16:05:42 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:05:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.3, CPU: 18.2%, 内存: 1030.32MB
2025-07-29 16:05:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.1, CPU: 18.6%, 内存: 1039.33MB
2025-07-29 16:05:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.5, CPU: 18.19%, 内存: 1041.54MB
2025-07-29 16:05:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.5, CPU: 17.12%, 内存: 1041.33MB
2025-07-29 16:05:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.1, CPU: 17.3%, 内存: 1041.33MB
2025-07-29 16:05:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.6, CPU: 16.57%, 内存: 1041.4MB
2025-07-29 16:05:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.7, CPU: 15.9%, 内存: 1041.47MB
2025-07-29 16:05:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.1, CPU: 16.83%, 内存: 1041.56MB
2025-07-29 16:05:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.8, CPU: 16.76%, 内存: 1041.44MB
2025-07-29 16:06:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 32.7, CPU: 16.2%, 内存: 1042.9MB
2025-07-29 16:06:04 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.1, CPU: 17.07%, 内存: 1046.69MB
2025-07-29 16:06:05 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 23.5秒
2025-07-29 16:06:05 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:06:06 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.3, CPU: 14.16%, 内存: 1046.59MB
2025-07-29 16:06:07 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:06:08 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.0, CPU: 16.36%, 内存: 1047.72MB
2025-07-29 16:06:08 - test_runner - INFO - 🏆第5关
2025-07-29 16:06:08 - test_runner - INFO - 🛫回大厅开启第5关，开启弹弓引导
2025-07-29 16:06:08 - poco_universal - INFO - 等待并点击元素: singleBtn_btn, 超时: 180秒
2025-07-29 16:06:10 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.6, CPU: 16.73%, 内存: 1025.47MB
2025-07-29 16:06:10 - poco_universal - INFO - 元素出现成功: singleBtn_btn, 等待时间: 2.3秒
2025-07-29 16:06:10 - poco_universal - INFO - 开始点击元素: singleBtn_btn
2025-07-29 16:06:12 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.8, CPU: 13.22%, 内存: 1025.4MB
2025-07-29 16:06:12 - poco_universal - INFO - 点击成功: singleBtn_btn
2025-07-29 16:06:14 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.9, CPU: 12.47%, 内存: 1024.71MB
2025-07-29 16:06:15 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:06:15 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.4秒
2025-07-29 16:06:15 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:06:16 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.5, CPU: 11.26%, 内存: 1024.46MB
2025-07-29 16:06:18 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.0, CPU: 11.75%, 内存: 1024.47MB
2025-07-29 16:06:18 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:06:19 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:06:20 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.1, CPU: 11.25%, 内存: 1024.4MB
2025-07-29 16:06:21 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:06:21 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.8, CPU: 12.41%, 内存: 1024.73MB
2025-07-29 16:06:22 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:06:24 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.8, CPU: 11.47%, 内存: 1025.3MB
2025-07-29 16:06:24 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:06:25 - poco_universal - INFO - 等待并点击元素: UIMgr -> des_txtPro, 超时: 180秒
2025-07-29 16:06:25 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.7, CPU: 16.04%, 内存: 1025.33MB
2025-07-29 16:06:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.8, CPU: 16.52%, 内存: 1046.27MB
2025-07-29 16:06:27 - poco_universal - INFO - 元素出现成功: UIMgr -> des_txtPro, 等待时间: 2.5秒
2025-07-29 16:06:27 - poco_universal - INFO - 开始点击元素: UIMgr -> des_txtPro
2025-07-29 16:06:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.6, CPU: 17.12%, 内存: 1045.47MB
2025-07-29 16:06:29 - poco_universal - INFO - 点击成功: UIMgr -> des_txtPro
2025-07-29 16:06:30 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> Combat2_btn, 超时: 180秒
2025-07-29 16:06:31 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> Combat2_btn, 等待时间: 0.5秒
2025-07-29 16:06:31 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> Combat2_btn
2025-07-29 16:06:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.3, CPU: 16.75%, 内存: 1045.32MB
2025-07-29 16:06:33 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> Combat2_btn
2025-07-29 16:06:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 55.9, CPU: 16.6%, 内存: 1045.52MB
2025-07-29 16:06:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.3, CPU: 16.53%, 内存: 1045.13MB
2025-07-29 16:06:36 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:06:36 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:06:36 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:06:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.9, CPU: 15.89%, 内存: 1045.22MB
2025-07-29 16:06:38 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:06:39 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:06:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.4, CPU: 16.83%, 内存: 1045.5MB
2025-07-29 16:06:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.9, CPU: 17.46%, 内存: 1045.48MB
2025-07-29 16:06:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.2, CPU: 16.97%, 内存: 1045.5MB
2025-07-29 16:06:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.3, CPU: 16.21%, 内存: 1045.44MB
2025-07-29 16:06:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.3, CPU: 16.63%, 内存: 1045.36MB
2025-07-29 16:06:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.7, CPU: 17.28%, 内存: 1045.77MB
2025-07-29 16:06:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.6, CPU: 16.91%, 内存: 1045.81MB
2025-07-29 16:06:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.4, CPU: 16.39%, 内存: 1047.32MB
2025-07-29 16:06:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.4, CPU: 16.13%, 内存: 1050.16MB
2025-07-29 16:06:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.7, CPU: 14.48%, 内存: 1050.04MB
2025-07-29 16:06:59 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 19.6秒
2025-07-29 16:06:59 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:06:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.3, CPU: 13.66%, 内存: 1050.28MB
2025-07-29 16:07:00 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:07:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.6, CPU: 16.13%, 内存: 1050.99MB
2025-07-29 16:07:01 - test_runner - INFO - 🏆第6关
2025-07-29 16:07:01 - test_runner - INFO - 🛫回大厅开启第6关
2025-07-29 16:07:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.1, CPU: 15.29%, 内存: 1024.08MB
2025-07-29 16:07:03 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:07:04 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:07:04 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:07:05 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.6, CPU: 11.4%, 内存: 1023.7MB
2025-07-29 16:07:06 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:07:07 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:07:07 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.4, CPU: 11.1%, 内存: 1023.67MB
2025-07-29 16:07:09 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:07:09 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.9, CPU: 11.83%, 内存: 1023.85MB
2025-07-29 16:07:10 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:07:11 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 32.8, CPU: 11.49%, 内存: 1024.28MB
2025-07-29 16:07:12 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:07:13 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.1, CPU: 13.97%, 内存: 1019.57MB
2025-07-29 16:07:15 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.1, CPU: 17.23%, 内存: 1037.34MB
2025-07-29 16:07:15 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:07:16 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:07:16 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:07:17 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.6, CPU: 15.96%, 内存: 1038.32MB
2025-07-29 16:07:18 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:07:19 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:07:19 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.7, CPU: 16.37%, 内存: 1038.15MB
2025-07-29 16:07:21 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.5, CPU: 17.26%, 内存: 1040.4MB
2025-07-29 16:07:23 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.5, CPU: 18.24%, 内存: 1042.21MB
2025-07-29 16:07:25 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.0, CPU: 17.31%, 内存: 1042.12MB
2025-07-29 16:07:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.6, CPU: 17.64%, 内存: 1042.37MB
2025-07-29 16:07:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.4, CPU: 16.34%, 内存: 1042.83MB
2025-07-29 16:07:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.7, CPU: 17.37%, 内存: 1042.39MB
2025-07-29 16:07:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.5, CPU: 16.24%, 内存: 1042.49MB
2025-07-29 16:07:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 56.9, CPU: 16.41%, 内存: 1043.76MB
2025-07-29 16:07:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.0, CPU: 16.38%, 内存: 1051.21MB
2025-07-29 16:07:38 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.2, CPU: 15.22%, 内存: 1050.68MB
2025-07-29 16:07:40 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 21.0秒
2025-07-29 16:07:40 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:07:40 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.9, CPU: 13.99%, 内存: 1051.06MB
2025-07-29 16:07:41 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:07:42 - test_runner - INFO - 🏆第7关
2025-07-29 16:07:42 - test_runner - INFO - 🛫回大厅开启第7关，开启风车引导
2025-07-29 16:07:42 - poco_universal - INFO - 等待并点击元素: singleBtn_btn, 超时: 180秒
2025-07-29 16:07:42 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.6, CPU: 16.5%, 内存: 1049.94MB
2025-07-29 16:07:44 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.8, CPU: 16.01%, 内存: 1024.02MB
2025-07-29 16:07:45 - poco_universal - INFO - 元素出现成功: singleBtn_btn, 等待时间: 2.3秒
2025-07-29 16:07:45 - poco_universal - INFO - 开始点击元素: singleBtn_btn
2025-07-29 16:07:46 - poco_universal - INFO - 点击成功: singleBtn_btn
2025-07-29 16:07:46 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.6, CPU: 12.53%, 内存: 1024.07MB
2025-07-29 16:07:48 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.4, CPU: 11.35%, 内存: 1023.41MB
2025-07-29 16:07:49 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:07:50 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:07:50 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:07:50 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.8, CPU: 11.09%, 内存: 1023.23MB
2025-07-29 16:07:52 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:07:52 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.8, CPU: 11.72%, 内存: 1023.21MB
2025-07-29 16:07:53 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:07:54 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.3, CPU: 11.46%, 内存: 1023.22MB
2025-07-29 16:07:55 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:07:56 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:07:56 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.2, CPU: 11.97%, 内存: 1023.21MB
2025-07-29 16:07:58 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.0, CPU: 11.98%, 内存: 1023.89MB
2025-07-29 16:07:58 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:07:59 - poco_universal - INFO - 等待并点击元素: UIMgr -> des_txtPro, 超时: 180秒
2025-07-29 16:08:00 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.8, CPU: 17.24%, 内存: 1041.95MB
2025-07-29 16:08:02 - poco_universal - INFO - 元素出现成功: UIMgr -> des_txtPro, 等待时间: 2.6秒
2025-07-29 16:08:02 - poco_universal - INFO - 开始点击元素: UIMgr -> des_txtPro
2025-07-29 16:08:02 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.0, CPU: 17.89%, 内存: 1047.11MB
2025-07-29 16:08:04 - poco_universal - INFO - 点击成功: UIMgr -> des_txtPro
2025-07-29 16:08:04 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.1, CPU: 17.44%, 内存: 1046.87MB
2025-07-29 16:08:05 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> Combat3_btn, 超时: 180秒
2025-07-29 16:08:05 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> Combat3_btn, 等待时间: 0.6秒
2025-07-29 16:08:05 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> Combat3_btn
2025-07-29 16:08:06 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.3, CPU: 17.84%, 内存: 1047.15MB
2025-07-29 16:08:07 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> Combat3_btn
2025-07-29 16:08:08 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.9, CPU: 17.74%, 内存: 1060.47MB
2025-07-29 16:08:10 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.7, CPU: 17.44%, 内存: 1060.32MB
2025-07-29 16:08:10 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:08:11 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:08:11 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:08:12 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.6, CPU: 17.17%, 内存: 1061.75MB
2025-07-29 16:08:13 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:08:14 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:08:14 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.8, CPU: 17.35%, 内存: 1060.89MB
2025-07-29 16:08:16 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.7, CPU: 17.68%, 内存: 1062.21MB
2025-07-29 16:08:18 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.8, CPU: 17.79%, 内存: 1067.51MB
2025-07-29 16:08:20 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.8, CPU: 17.38%, 内存: 1076.08MB
2025-07-29 16:08:22 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.6, CPU: 17.71%, 内存: 1077.82MB
2025-07-29 16:08:24 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.8, CPU: 15.76%, 内存: 1077.52MB
2025-07-29 16:08:26 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.3, CPU: 15.97%, 内存: 1077.51MB
2025-07-29 16:08:28 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.7, CPU: 15.67%, 内存: 1077.58MB
2025-07-29 16:08:30 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.8, CPU: 15.65%, 内存: 1077.5MB
2025-07-29 16:08:32 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.5, CPU: 15.93%, 内存: 1077.54MB
2025-07-29 16:08:34 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.6, CPU: 15.73%, 内存: 1079.42MB
2025-07-29 16:08:36 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.3, CPU: 16.16%, 内存: 1082.77MB
2025-07-29 16:08:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.0, CPU: 15.21%, 内存: 1083.26MB
2025-07-29 16:08:39 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 25.5秒
2025-07-29 16:08:39 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:08:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.3, CPU: 13.99%, 内存: 1083.13MB
2025-07-29 16:08:41 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:08:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.4, CPU: 16.14%, 内存: 1084.66MB
2025-07-29 16:08:42 - test_runner - INFO - 🏆第8关
2025-07-29 16:08:42 - test_runner - INFO - 🛫回大厅开启第8关
2025-07-29 16:08:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.2, CPU: 15.6%, 内存: 1046.65MB
2025-07-29 16:08:44 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:08:44 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:08:44 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:08:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.0, CPU: 11.04%, 内存: 1046.18MB
2025-07-29 16:08:46 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:08:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.0, CPU: 10.82%, 内存: 1046.15MB
2025-07-29 16:08:47 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:08:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.1, CPU: 12.61%, 内存: 1046.45MB
2025-07-29 16:08:49 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:08:50 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:08:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.0, CPU: 11.37%, 内存: 1046.5MB
2025-07-29 16:08:53 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:08:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.9, CPU: 12.57%, 内存: 1046.96MB
2025-07-29 16:08:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.7, CPU: 17.09%, 内存: 1070.87MB
2025-07-29 16:08:56 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:08:56 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:08:56 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:08:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.0, CPU: 16.54%, 内存: 1073.06MB
2025-07-29 16:08:58 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:08:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.1, CPU: 18.15%, 内存: 1072.97MB
2025-07-29 16:08:59 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:09:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.4, CPU: 18.53%, 内存: 1072.97MB
2025-07-29 16:09:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.2, CPU: 18.58%, 内存: 1072.96MB
2025-07-29 16:09:05 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.1, CPU: 17.59%, 内存: 1073.36MB
2025-07-29 16:09:07 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.7, CPU: 18.09%, 内存: 1073.46MB
2025-07-29 16:09:09 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.1, CPU: 17.47%, 内存: 1072.67MB
2025-07-29 16:09:11 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.0, CPU: 17.54%, 内存: 1072.72MB
2025-07-29 16:09:13 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.9, CPU: 17.29%, 内存: 1073.01MB
2025-07-29 16:09:15 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.5, CPU: 16.78%, 内存: 1072.61MB
2025-07-29 16:09:17 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.0, CPU: 15.55%, 内存: 1072.62MB
2025-07-29 16:09:19 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.7, CPU: 16.38%, 内存: 1072.66MB
2025-07-29 16:09:21 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.2, CPU: 16.59%, 内存: 1073.0MB
2025-07-29 16:09:23 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.9, CPU: 16.07%, 内存: 1072.7MB
2025-07-29 16:09:25 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.3, CPU: 15.7%, 内存: 1073.09MB
2025-07-29 16:09:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.1, CPU: 15.74%, 内存: 1076.91MB
2025-07-29 16:09:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.0, CPU: 15.33%, 内存: 1077.88MB
2025-07-29 16:09:30 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 30.5秒
2025-07-29 16:09:30 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:09:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.0, CPU: 14.24%, 内存: 1077.76MB
2025-07-29 16:09:31 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:09:32 - test_runner - INFO - 🏆第9关
2025-07-29 16:09:32 - test_runner - INFO - 🛫回大厅开启第9关，开启冰球引导
2025-07-29 16:09:32 - poco_universal - INFO - 等待并点击元素: singleBtn_btn, 超时: 180秒
2025-07-29 16:09:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.1, CPU: 15.87%, 内存: 1052.7MB
2025-07-29 16:09:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.5, CPU: 15.44%, 内存: 1050.05MB
2025-07-29 16:09:35 - poco_universal - INFO - 元素出现成功: singleBtn_btn, 等待时间: 2.4秒
2025-07-29 16:09:35 - poco_universal - INFO - 开始点击元素: singleBtn_btn
2025-07-29 16:09:36 - poco_universal - INFO - 点击成功: singleBtn_btn
2025-07-29 16:09:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 55.1, CPU: 12.61%, 内存: 1050.26MB
2025-07-29 16:09:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.3, CPU: 11.67%, 内存: 1049.58MB
2025-07-29 16:09:39 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:09:40 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:09:40 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:09:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 56.7, CPU: 11.26%, 内存: 1049.42MB
2025-07-29 16:09:42 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:09:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 55.2, CPU: 11.68%, 内存: 1050.08MB
2025-07-29 16:09:43 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:09:44 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.7, CPU: 11.97%, 内存: 1049.61MB
2025-07-29 16:09:45 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:09:46 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:09:46 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.4, CPU: 11.48%, 内存: 1049.47MB
2025-07-29 16:09:48 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.9, CPU: 11.48%, 内存: 1043.88MB
2025-07-29 16:09:49 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:09:50 - poco_universal - INFO - 等待并点击元素: UIMgr -> des_txtPro, 超时: 180秒
2025-07-29 16:09:50 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 56.7, CPU: 17.55%, 内存: 1042.25MB
2025-07-29 16:09:52 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.7, CPU: 18.0%, 内存: 1065.64MB
2025-07-29 16:09:52 - poco_universal - INFO - 元素出现成功: UIMgr -> des_txtPro, 等待时间: 2.7秒
2025-07-29 16:09:52 - poco_universal - INFO - 开始点击元素: UIMgr -> des_txtPro
2025-07-29 16:09:54 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.3, CPU: 18.72%, 内存: 1067.33MB
2025-07-29 16:09:54 - poco_universal - INFO - 点击成功: UIMgr -> des_txtPro
2025-07-29 16:09:55 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> Combat4_btn, 超时: 180秒
2025-07-29 16:09:56 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> Combat4_btn, 等待时间: 0.7秒
2025-07-29 16:09:56 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> Combat4_btn
2025-07-29 16:09:56 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.2, CPU: 18.96%, 内存: 1069.69MB
2025-07-29 16:09:58 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> Combat4_btn
2025-07-29 16:09:58 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.1, CPU: 18.45%, 内存: 1073.07MB
2025-07-29 16:10:00 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.9, CPU: 18.82%, 内存: 1072.97MB
2025-07-29 16:10:01 - poco_universal - INFO - 开始获取UI树结构 (深度: 10, 包含不可见: False)
2025-07-29 16:10:02 - poco_universal - INFO - UI树获取完成，共296个节点
2025-07-29 16:10:02 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.6, CPU: 18.83%, 内存: 1074.4MB
2025-07-29 16:10:04 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:10:04 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.4, CPU: 19.14%, 内存: 1074.4MB
2025-07-29 16:10:04 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.7秒
2025-07-29 16:10:04 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:10:06 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.6, CPU: 18.22%, 内存: 1074.37MB
2025-07-29 16:10:06 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:10:07 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:10:08 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.4, CPU: 20.38%, 内存: 1074.84MB
2025-07-29 16:10:10 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.9, CPU: 18.95%, 内存: 1075.02MB
2025-07-29 16:10:12 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.0, CPU: 18.55%, 内存: 1075.16MB
2025-07-29 16:10:14 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.6, CPU: 18.68%, 内存: 1075.4MB
2025-07-29 16:10:16 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.1, CPU: 17.62%, 内存: 1077.8MB
2025-07-29 16:10:18 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.7, CPU: 17.96%, 内存: 1086.33MB
2025-07-29 16:10:20 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.8, CPU: 17.36%, 内存: 1091.3MB
2025-07-29 16:10:22 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.4, CPU: 17.49%, 内存: 1091.06MB
2025-07-29 16:10:24 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.5, CPU: 16.73%, 内存: 1091.64MB
2025-07-29 16:10:26 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.7, CPU: 16.58%, 内存: 1091.46MB
2025-07-29 16:10:28 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.1, CPU: 16.7%, 内存: 1091.64MB
2025-07-29 16:10:30 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.5, CPU: 17.11%, 内存: 1091.49MB
2025-07-29 16:10:32 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.3, CPU: 16.45%, 内存: 1091.49MB
2025-07-29 16:10:34 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.8, CPU: 16.13%, 内存: 1092.86MB
2025-07-29 16:10:36 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.3, CPU: 15.55%, 内存: 1097.82MB
2025-07-29 16:10:38 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.5, CPU: 15.24%, 内存: 1097.04MB
2025-07-29 16:10:38 - poco_universal - INFO - 继续等待元素: UIMgr -> BtnSingleContinue_btn, 已等待: 30.7秒
2025-07-29 16:10:39 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 31.6秒
2025-07-29 16:10:39 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:10:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.5, CPU: 14.23%, 内存: 1097.0MB
2025-07-29 16:10:40 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:10:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.1, CPU: 16.13%, 内存: 1094.28MB
2025-07-29 16:10:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.5, CPU: 16.16%, 内存: 1077.74MB
2025-07-29 16:10:44 - poco_universal - INFO - 元素存在性: UIMgr -> AdChestView -> close1_btn = False
2025-07-29 16:10:44 - test_runner - INFO - 🏆第10关
2025-07-29 16:10:44 - test_runner - INFO - 🛫回大厅开启第10关
2025-07-29 16:10:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.3, CPU: 11.96%, 内存: 1076.52MB
2025-07-29 16:10:46 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:10:46 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:10:46 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:10:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.4, CPU: 11.08%, 内存: 1076.3MB
2025-07-29 16:10:49 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:10:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.1, CPU: 11.3%, 内存: 1076.96MB
2025-07-29 16:10:50 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:10:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.6, CPU: 11.47%, 内存: 1076.77MB
2025-07-29 16:10:52 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:10:53 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:10:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.4, CPU: 11.61%, 内存: 1076.65MB
2025-07-29 16:10:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.8, CPU: 11.33%, 内存: 1077.31MB
2025-07-29 16:10:55 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:10:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.1, CPU: 16.74%, 内存: 1094.96MB
2025-07-29 16:10:58 - poco_universal - INFO - 开始获取UI树结构 (深度: 10, 包含不可见: False)
2025-07-29 16:10:59 - poco_universal - INFO - UI树获取完成，共278个节点
2025-07-29 16:10:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.6, CPU: 15.65%, 内存: 1097.37MB
2025-07-29 16:11:01 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:11:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.0, CPU: 17.82%, 内存: 1097.28MB
2025-07-29 16:11:01 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:11:01 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:11:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.2, CPU: 17.7%, 内存: 1097.26MB
2025-07-29 16:11:03 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:11:04 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:11:05 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.6, CPU: 17.96%, 内存: 1097.91MB
2025-07-29 16:11:07 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.6, CPU: 19.07%, 内存: 1097.69MB
2025-07-29 16:11:09 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.8, CPU: 18.81%, 内存: 1097.71MB
2025-07-29 16:11:11 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.6, CPU: 17.74%, 内存: 1097.69MB
2025-07-29 16:11:13 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.6, CPU: 17.34%, 内存: 1105.13MB
2025-07-29 16:11:15 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.2, CPU: 17.43%, 内存: 1112.32MB
2025-07-29 16:11:17 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.6, CPU: 16.87%, 内存: 1112.14MB
2025-07-29 16:11:19 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.2, CPU: 16.61%, 内存: 1112.36MB
2025-07-29 16:11:21 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.1, CPU: 16.66%, 内存: 1112.14MB
2025-07-29 16:11:23 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.8, CPU: 16.71%, 内存: 1112.24MB
2025-07-29 16:11:25 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.3, CPU: 16.72%, 内存: 1112.47MB
2025-07-29 16:11:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.3, CPU: 16.67%, 内存: 1112.59MB
2025-07-29 16:11:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.5, CPU: 15.93%, 内存: 1112.6MB
2025-07-29 16:11:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.2, CPU: 16.06%, 内存: 1113.95MB
2025-07-29 16:11:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.5, CPU: 16.15%, 内存: 1117.34MB
2025-07-29 16:11:34 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.3, CPU: 15.0%, 内存: 1117.92MB
2025-07-29 16:11:35 - poco_universal - INFO - 继续等待元素: UIMgr -> BtnSingleContinue_btn, 已等待: 30.7秒
2025-07-29 16:11:36 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 31.6秒
2025-07-29 16:11:36 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:11:36 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.0, CPU: 13.23%, 内存: 1117.35MB
2025-07-29 16:11:37 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:11:38 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.8, CPU: 15.4%, 内存: 1118.15MB
2025-07-29 16:11:40 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.7, CPU: 16.21%, 内存: 1092.74MB
2025-07-29 16:11:41 - poco_universal - INFO - 元素存在性: UIMgr -> AdChestView -> close1_btn = False
2025-07-29 16:11:41 - test_runner - INFO - 🏆第11关
2025-07-29 16:11:41 - test_runner - INFO - 🛫回大厅开启第11关,开启关前道具飞机引导
2025-07-29 16:11:42 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.5, CPU: 10.74%, 内存: 1092.38MB
2025-07-29 16:11:44 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.0, CPU: 10.74%, 内存: 1092.73MB
2025-07-29 16:11:46 - poco_universal - INFO - 等待并点击元素: MainMenuPanel -> HomePanel_com -> lv_txtPro, 超时: 180秒
2025-07-29 16:11:46 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.6, CPU: 10.54%, 内存: 1092.33MB
2025-07-29 16:11:46 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> HomePanel_com -> lv_txtPro, 等待时间: 0.5秒
2025-07-29 16:11:46 - poco_universal - INFO - 开始点击元素: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:11:48 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.9, CPU: 11.47%, 内存: 1092.84MB
2025-07-29 16:11:48 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:11:49 - poco_universal - INFO - 等待并点击元素: btnFlash_btn, 超时: 180秒
2025-07-29 16:11:50 - poco_universal - INFO - 元素出现成功: btnFlash_btn, 等待时间: 0.6秒
2025-07-29 16:11:50 - poco_universal - INFO - 开始点击元素: btnFlash_btn
2025-07-29 16:11:50 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 56.5, CPU: 9.02%, 内存: 1092.63MB
2025-07-29 16:11:51 - poco_universal - INFO - 点击成功: btnFlash_btn
2025-07-29 16:11:52 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.3, CPU: 10.57%, 内存: 1092.95MB
2025-07-29 16:11:53 - poco_universal - INFO - 等待并点击元素: Play, 超时: 180秒
2025-07-29 16:11:53 - poco_universal - INFO - 元素出现成功: Play, 等待时间: 0.6秒
2025-07-29 16:11:53 - poco_universal - INFO - 开始点击元素: Play
2025-07-29 16:11:54 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.3, CPU: 11.14%, 内存: 1092.97MB
2025-07-29 16:11:55 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:11:56 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.0, CPU: 14.26%, 内存: 1094.88MB
2025-07-29 16:11:58 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.2, CPU: 15.41%, 内存: 1112.91MB
2025-07-29 16:12:00 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.9, CPU: 16.72%, 内存: 1115.04MB
2025-07-29 16:12:02 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.1, CPU: 13.86%, 内存: 1114.83MB
2025-07-29 16:12:03 - poco_universal - INFO - 开始获取UI树结构 (深度: 10, 包含不可见: False)
2025-07-29 16:12:03 - poco_universal - INFO - UI树获取完成，共223个节点
2025-07-29 16:12:04 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.7, CPU: 13.1%, 内存: 1115.47MB
2025-07-29 16:12:05 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:12:06 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.6, CPU: 15.85%, 内存: 1115.4MB
2025-07-29 16:12:06 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.5秒
2025-07-29 16:12:06 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:12:08 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:12:08 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.0, CPU: 15.48%, 内存: 1115.05MB
2025-07-29 16:12:09 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:12:10 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.1, CPU: 16.06%, 内存: 1114.97MB
2025-07-29 16:12:12 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.4, CPU: 16.01%, 内存: 1114.96MB
2025-07-29 16:12:14 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.3, CPU: 15.78%, 内存: 1114.39MB
2025-07-29 16:12:16 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.5, CPU: 14.97%, 内存: 1114.4MB
2025-07-29 16:12:18 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.4, CPU: 15.29%, 内存: 1114.21MB
2025-07-29 16:12:20 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.1, CPU: 15.75%, 内存: 1114.38MB
2025-07-29 16:12:22 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.4, CPU: 15.46%, 内存: 1116.41MB
2025-07-29 16:12:24 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.8, CPU: 16.39%, 内存: 1116.91MB
2025-07-29 16:12:26 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.9, CPU: 13.73%, 内存: 1116.75MB
2025-07-29 16:12:26 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 17.2秒
2025-07-29 16:12:26 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:12:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.7, CPU: 14.29%, 内存: 1116.82MB
2025-07-29 16:12:27 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:12:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.9, CPU: 17.38%, 内存: 1092.24MB
2025-07-29 16:12:31 - poco_universal - INFO - 元素存在性: UIMgr -> AdChestView -> close1_btn = False
2025-07-29 16:12:31 - test_runner - INFO - 🏆第12关
2025-07-29 16:12:31 - test_runner - INFO - 🛫回大厅开启第12关,神秘宝箱引导
2025-07-29 16:12:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.1, CPU: 11.72%, 内存: 1091.45MB
2025-07-29 16:12:33 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:12:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.2, CPU: 10.73%, 内存: 1091.29MB
2025-07-29 16:12:33 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:12:33 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:12:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.1, CPU: 10.79%, 内存: 1091.25MB
2025-07-29 16:12:36 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:12:37 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:12:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.1, CPU: 10.72%, 内存: 1091.23MB
2025-07-29 16:12:39 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:12:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.8, CPU: 11.79%, 内存: 1091.52MB
2025-07-29 16:12:40 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:12:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.1, CPU: 11.35%, 内存: 1091.54MB
2025-07-29 16:12:42 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:12:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.8, CPU: 14.98%, 内存: 1092.98MB
2025-07-29 16:12:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.2, CPU: 16.64%, 内存: 1111.15MB
2025-07-29 16:12:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.6, CPU: 14.42%, 内存: 1109.3MB
2025-07-29 16:12:49 - poco_universal - INFO - 元素存在性: propMysteryBox1_white1 = True
2025-07-29 16:12:49 - poco_universal - INFO - 等待并点击元素: propMysteryBox1_white1, 超时: 180秒
2025-07-29 16:12:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.2, CPU: 15.22%, 内存: 1111.47MB
2025-07-29 16:12:49 - poco_universal - INFO - 元素出现成功: propMysteryBox1_white1, 等待时间: 0.6秒
2025-07-29 16:12:49 - poco_universal - INFO - 开始点击元素: propMysteryBox1_white1
2025-07-29 16:12:51 - poco_universal - INFO - 点击成功: propMysteryBox1_white1
2025-07-29 16:12:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.7, CPU: 17.65%, 内存: 1112.29MB
2025-07-29 16:12:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.7, CPU: 17.53%, 内存: 1112.17MB
2025-07-29 16:12:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.5, CPU: 16.51%, 内存: 1112.33MB
2025-07-29 16:12:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.0, CPU: 16.27%, 内存: 1111.97MB
2025-07-29 16:12:57 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleEffectPanel -> continueTip_tr, 超时: 180秒
2025-07-29 16:12:58 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleEffectPanel -> continueTip_tr, 等待时间: 0.6秒
2025-07-29 16:12:58 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleEffectPanel -> continueTip_tr
2025-07-29 16:12:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.5, CPU: 16.78%, 内存: 1112.56MB
2025-07-29 16:13:00 - poco_universal - INFO - 点击成功: UIMgr -> BattleEffectPanel -> continueTip_tr
2025-07-29 16:13:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.4, CPU: 16.44%, 内存: 1112.42MB
2025-07-29 16:13:03 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:13:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.8, CPU: 14.92%, 内存: 1112.5MB
2025-07-29 16:13:03 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:13:03 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:13:05 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.5, CPU: 16.14%, 内存: 1112.43MB
2025-07-29 16:13:05 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:13:06 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:13:07 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.0, CPU: 16.76%, 内存: 1112.56MB
2025-07-29 16:13:09 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.8, CPU: 16.33%, 内存: 1112.58MB
2025-07-29 16:13:10 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.1, CPU: 15.4%, 内存: 1112.5MB
2025-07-29 16:13:12 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.4, CPU: 16.48%, 内存: 1112.66MB
2025-07-29 16:13:14 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.1, CPU: 16.43%, 内存: 1112.5MB
2025-07-29 16:13:16 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.4, CPU: 15.69%, 内存: 1112.73MB
2025-07-29 16:13:18 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.9, CPU: 15.58%, 内存: 1112.56MB
2025-07-29 16:13:20 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.1, CPU: 15.47%, 内存: 1112.6MB
2025-07-29 16:13:22 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.6, CPU: 15.73%, 内存: 1112.54MB
2025-07-29 16:13:24 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.4, CPU: 15.42%, 内存: 1112.79MB
2025-07-29 16:13:26 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.8, CPU: 15.16%, 内存: 1114.03MB
2025-07-29 16:13:28 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.1, CPU: 15.18%, 内存: 1122.22MB
2025-07-29 16:13:30 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.1, CPU: 14.67%, 内存: 1124.21MB
2025-07-29 16:13:31 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 25.3秒
2025-07-29 16:13:31 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:13:32 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.5, CPU: 14.13%, 内存: 1123.74MB
2025-07-29 16:13:33 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:13:34 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.0, CPU: 15.11%, 内存: 1132.42MB
2025-07-29 16:13:34 - poco_universal - INFO - 等待并点击元素: UIMgr -> AdChestView -> close1_btn, 超时: 180秒
2025-07-29 16:13:35 - poco_universal - INFO - 元素出现成功: UIMgr -> AdChestView -> close1_btn, 等待时间: 1.3秒
2025-07-29 16:13:35 - poco_universal - INFO - 开始点击元素: UIMgr -> AdChestView -> close1_btn
2025-07-29 16:13:36 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.5, CPU: 14.15%, 内存: 1132.04MB
2025-07-29 16:13:37 - poco_universal - INFO - 点击成功: UIMgr -> AdChestView -> close1_btn
2025-07-29 16:13:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.8, CPU: 16.41%, 内存: 1133.16MB
2025-07-29 16:13:38 - test_runner - INFO - 🏆第13关
2025-07-29 16:13:38 - test_runner - INFO - 🛫回大厅开启第13关,开启关前道具闹钟引导
2025-07-29 16:13:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.2, CPU: 16.57%, 内存: 1099.08MB
2025-07-29 16:13:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.6, CPU: 10.89%, 内存: 1082.48MB
2025-07-29 16:13:43 - poco_universal - INFO - 等待并点击元素: MainMenuPanel -> HomePanel_com -> lv_txtPro, 超时: 180秒
2025-07-29 16:13:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.7, CPU: 10.58%, 内存: 1082.34MB
2025-07-29 16:13:44 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> HomePanel_com -> lv_txtPro, 等待时间: 0.5秒
2025-07-29 16:13:44 - poco_universal - INFO - 开始点击元素: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:13:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.0, CPU: 11.34%, 内存: 1084.54MB
2025-07-29 16:13:45 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:13:46 - poco_universal - INFO - 等待并点击元素: btnAddTime_btn, 超时: 180秒
2025-07-29 16:13:47 - poco_universal - INFO - 元素出现成功: btnAddTime_btn, 等待时间: 0.5秒
2025-07-29 16:13:47 - poco_universal - INFO - 开始点击元素: btnAddTime_btn
2025-07-29 16:13:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.3, CPU: 9.02%, 内存: 1084.02MB
2025-07-29 16:13:49 - poco_universal - INFO - 点击成功: btnAddTime_btn
2025-07-29 16:13:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.6, CPU: 10.39%, 内存: 1083.99MB
2025-07-29 16:13:50 - poco_universal - INFO - 等待并点击元素: Play, 超时: 180秒
2025-07-29 16:13:50 - poco_universal - INFO - 元素出现成功: Play, 等待时间: 0.5秒
2025-07-29 16:13:50 - poco_universal - INFO - 开始点击元素: Play
2025-07-29 16:13:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.2, CPU: 10.98%, 内存: 1084.09MB
2025-07-29 16:13:52 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:13:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.5, CPU: 14.99%, 内存: 1085.54MB
2025-07-29 16:13:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.7, CPU: 16.33%, 内存: 1107.57MB
2025-07-29 16:13:55 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:13:56 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:13:56 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:13:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.5, CPU: 17.28%, 内存: 1109.41MB
2025-07-29 16:13:57 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:13:58 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:13:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.5, CPU: 17.99%, 内存: 1109.24MB
2025-07-29 16:14:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.9, CPU: 17.32%, 内存: 1109.23MB
2025-07-29 16:14:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.1, CPU: 16.91%, 内存: 1109.31MB
2025-07-29 16:14:05 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.9, CPU: 17.34%, 内存: 1109.44MB
2025-07-29 16:14:07 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.4, CPU: 15.84%, 内存: 1109.22MB
2025-07-29 16:14:08 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.0, CPU: 16.7%, 内存: 1109.8MB
2025-07-29 16:14:10 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.4, CPU: 16.33%, 内存: 1109.72MB
2025-07-29 16:14:12 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.2, CPU: 15.43%, 内存: 1109.63MB
2025-07-29 16:14:14 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.2, CPU: 14.98%, 内存: 1109.63MB
2025-07-29 16:14:16 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.2, CPU: 16.08%, 内存: 1112.12MB
2025-07-29 16:14:18 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.5, CPU: 15.58%, 内存: 1112.04MB
2025-07-29 16:14:20 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.3, CPU: 15.11%, 内存: 1113.41MB
2025-07-29 16:14:22 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.6, CPU: 15.58%, 内存: 1123.24MB
2025-07-29 16:14:24 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.3, CPU: 15.28%, 内存: 1122.89MB
2025-07-29 16:14:25 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 26.8秒
2025-07-29 16:14:25 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:14:26 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.1, CPU: 13.58%, 内存: 1122.89MB
2025-07-29 16:14:27 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:14:28 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.4, CPU: 15.75%, 内存: 1123.63MB
2025-07-29 16:14:28 - test_runner - INFO - 🏆第14关
2025-07-29 16:14:28 - test_runner - INFO - 🛫回大厅开启第14关,开启困难关卡引导，有黄色的神秘盒子
2025-07-29 16:14:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.6, CPU: 15.32%, 内存: 1099.78MB
2025-07-29 16:14:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.2, CPU: 10.28%, 内存: 1099.44MB
2025-07-29 16:14:33 - poco_universal - INFO - 等待并点击元素: MainMenuPanel -> HomePanel_com -> lv_txtPro, 超时: 180秒
2025-07-29 16:14:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.2, CPU: 10.53%, 内存: 1099.63MB
2025-07-29 16:14:33 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> HomePanel_com -> lv_txtPro, 等待时间: 0.5秒
2025-07-29 16:14:33 - poco_universal - INFO - 开始点击元素: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:14:35 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:14:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.6, CPU: 11.6%, 内存: 1100.86MB
2025-07-29 16:14:36 - poco_universal - INFO - 等待并点击元素: warn_tr, 超时: 180秒
2025-07-29 16:14:37 - poco_universal - INFO - 元素出现成功: warn_tr, 等待时间: 0.6秒
2025-07-29 16:14:37 - poco_universal - INFO - 开始点击元素: warn_tr
2025-07-29 16:14:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.5, CPU: 8.58%, 内存: 1100.68MB
2025-07-29 16:14:38 - poco_universal - INFO - 点击成功: warn_tr
2025-07-29 16:14:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.1, CPU: 8.58%, 内存: 1100.62MB
2025-07-29 16:14:39 - poco_universal - INFO - 等待并点击元素: TextTitle_txtPro, 超时: 180秒
2025-07-29 16:14:40 - poco_universal - INFO - 元素出现成功: TextTitle_txtPro, 等待时间: 0.6秒
2025-07-29 16:14:40 - poco_universal - INFO - 开始点击元素: TextTitle_txtPro
2025-07-29 16:14:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.5, CPU: 8.84%, 内存: 1101.48MB
2025-07-29 16:14:42 - poco_universal - INFO - 点击成功: TextTitle_txtPro
2025-07-29 16:14:43 - poco_universal - INFO - 等待并点击元素: Play, 超时: 180秒
2025-07-29 16:14:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.9, CPU: 11.31%, 内存: 1100.83MB
2025-07-29 16:14:43 - poco_universal - INFO - 元素出现成功: Play, 等待时间: 0.5秒
2025-07-29 16:14:43 - poco_universal - INFO - 开始点击元素: Play
2025-07-29 16:14:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.0, CPU: 11.82%, 内存: 1101.02MB
2025-07-29 16:14:45 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:14:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.0, CPU: 15.84%, 内存: 1103.8MB
2025-07-29 16:14:48 - poco_universal - INFO - 开始获取UI树结构 (深度: 10, 包含不可见: False)
2025-07-29 16:14:49 - poco_universal - INFO - UI树获取完成，共248个节点
2025-07-29 16:14:49 - poco_universal - INFO - 等待并点击元素: propMysteryBox1_yellow1, 超时: 180秒
2025-07-29 16:14:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.7, CPU: 15.64%, 内存: 1121.05MB
2025-07-29 16:14:49 - poco_universal - INFO - 元素出现成功: propMysteryBox1_yellow1, 等待时间: 0.6秒
2025-07-29 16:14:49 - poco_universal - INFO - 开始点击元素: propMysteryBox1_yellow1
2025-07-29 16:14:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.3, CPU: 16.66%, 内存: 1120.93MB
2025-07-29 16:14:51 - poco_universal - INFO - 点击成功: propMysteryBox1_yellow1
2025-07-29 16:14:53 - poco_universal - INFO - 元素存在性: UIMgr -> BattleEffectPanel -> continueTip_tr = False
2025-07-29 16:14:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 55.5, CPU: 15.9%, 内存: 1120.76MB
2025-07-29 16:14:53 - poco_universal - INFO - 元素存在性: UIMgr -> AdChestView -> close1_btn = False
2025-07-29 16:14:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 57.2, CPU: 15.34%, 内存: 1120.65MB
2025-07-29 16:14:55 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:14:56 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:14:56 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:14:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.2, CPU: 15.34%, 内存: 1120.88MB
2025-07-29 16:14:58 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:14:59 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:14:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.6, CPU: 16.47%, 内存: 1120.63MB
2025-07-29 16:15:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.7, CPU: 16.78%, 内存: 1120.62MB
2025-07-29 16:15:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.4, CPU: 16.36%, 内存: 1120.62MB
2025-07-29 16:15:05 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.4, CPU: 15.68%, 内存: 1120.61MB
2025-07-29 16:15:06 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.2, CPU: 15.73%, 内存: 1120.84MB
2025-07-29 16:15:08 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.1, CPU: 15.46%, 内存: 1120.68MB
2025-07-29 16:15:10 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.8, CPU: 15.01%, 内存: 1120.72MB
2025-07-29 16:15:12 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.9, CPU: 14.9%, 内存: 1120.74MB
2025-07-29 16:15:14 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.8, CPU: 14.99%, 内存: 1120.65MB
2025-07-29 16:15:16 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.6, CPU: 15.2%, 内存: 1120.89MB
2025-07-29 16:15:18 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.1, CPU: 16.2%, 内存: 1112.67MB
2025-07-29 16:15:20 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.7, CPU: 15.99%, 内存: 1115.83MB
2025-07-29 16:15:22 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 38.8, CPU: 13.28%, 内存: 1115.75MB
2025-07-29 16:15:22 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 23.5秒
2025-07-29 16:15:22 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:15:24 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:15:24 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.5, CPU: 15.22%, 内存: 1124.32MB
2025-07-29 16:15:26 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.5, CPU: 14.48%, 内存: 1124.01MB
2025-07-29 16:15:27 - poco_universal - INFO - 元素存在性: UIMgr -> AdChestView -> close1_btn = True
2025-07-29 16:15:27 - poco_universal - INFO - 等待并点击元素: UIMgr -> AdChestView -> close1_btn, 超时: 180秒
2025-07-29 16:15:28 - poco_universal - INFO - 元素出现成功: UIMgr -> AdChestView -> close1_btn, 等待时间: 0.4秒
2025-07-29 16:15:28 - poco_universal - INFO - 开始点击元素: UIMgr -> AdChestView -> close1_btn
2025-07-29 16:15:28 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.9, CPU: 14.21%, 内存: 1124.13MB
2025-07-29 16:15:29 - poco_universal - INFO - 点击成功: UIMgr -> AdChestView -> close1_btn
2025-07-29 16:15:30 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.7, CPU: 16.17%, 内存: 1123.55MB
2025-07-29 16:15:30 - test_runner - INFO - 🏆第15关
2025-07-29 16:15:30 - test_runner - INFO - 🛫回大厅开启第15关有个金色的排行榜箱子
2025-07-29 16:15:32 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.1, CPU: 16.47%, 内存: 1093.42MB
2025-07-29 16:15:32 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:15:33 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:15:33 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:15:34 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.8, CPU: 10.75%, 内存: 1093.58MB
2025-07-29 16:15:35 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:15:36 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.6, CPU: 10.58%, 内存: 1093.41MB
2025-07-29 16:15:36 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:15:38 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.2, CPU: 11.13%, 内存: 1093.89MB
2025-07-29 16:15:38 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:15:39 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:15:40 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.9, CPU: 11.58%, 内存: 1093.6MB
2025-07-29 16:15:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.4, CPU: 11.22%, 内存: 1094.33MB
2025-07-29 16:15:42 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:15:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.4, CPU: 16.85%, 内存: 1098.69MB
2025-07-29 16:15:45 - poco_universal - INFO - 开始获取UI树结构 (深度: 10, 包含不可见: False)
2025-07-29 16:15:45 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.5, CPU: 15.45%, 内存: 1120.41MB
2025-07-29 16:15:45 - poco_universal - INFO - UI树获取完成，共274个节点
2025-07-29 16:15:45 - poco_universal - INFO - 等待并点击元素: leaderboardToilet1(Clone), 超时: 180秒
2025-07-29 16:15:46 - poco_universal - INFO - 元素出现成功: leaderboardToilet1(Clone), 等待时间: 0.6秒
2025-07-29 16:15:46 - poco_universal - INFO - 开始点击元素: leaderboardToilet1(Clone)
2025-07-29 16:15:47 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.9, CPU: 17.11%, 内存: 1121.98MB
2025-07-29 16:15:48 - poco_universal - INFO - 点击成功: leaderboardToilet1(Clone)
2025-07-29 16:15:49 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.2, CPU: 17.64%, 内存: 1121.57MB
2025-07-29 16:15:50 - poco_universal - INFO - 元素存在性: UIMgr -> BattleEffectPanel -> continueTip_tr = True
2025-07-29 16:15:50 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleEffectPanel -> continueTip_tr, 超时: 180秒
2025-07-29 16:15:50 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleEffectPanel -> continueTip_tr, 等待时间: 0.6秒
2025-07-29 16:15:50 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleEffectPanel -> continueTip_tr
2025-07-29 16:15:51 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.5, CPU: 17.95%, 内存: 1124.16MB
2025-07-29 16:15:52 - poco_universal - INFO - 点击成功: UIMgr -> BattleEffectPanel -> continueTip_tr
2025-07-29 16:15:53 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.8, CPU: 16.9%, 内存: 1124.36MB
2025-07-29 16:15:54 - poco_universal - INFO - 元素存在性: UIMgr -> AdChestView -> close1_btn = False
2025-07-29 16:15:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 53.4, CPU: 15.43%, 内存: 1123.73MB
2025-07-29 16:15:56 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:15:56 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:15:56 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:15:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 56.7, CPU: 16.51%, 内存: 1123.67MB
2025-07-29 16:15:58 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:15:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.5, CPU: 16.94%, 内存: 1123.67MB
2025-07-29 16:15:59 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:16:00 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 56.2, CPU: 18.02%, 内存: 1123.89MB
2025-07-29 16:16:02 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.9, CPU: 17.08%, 内存: 1124.75MB
2025-07-29 16:16:04 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 56.3, CPU: 17.22%, 内存: 1124.78MB
2025-07-29 16:16:06 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.3, CPU: 16.44%, 内存: 1124.78MB
2025-07-29 16:16:08 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 55.5, CPU: 17.42%, 内存: 1124.98MB
2025-07-29 16:16:10 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.9, CPU: 16.34%, 内存: 1124.91MB
2025-07-29 16:16:12 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.4, CPU: 16.27%, 内存: 1127.88MB
2025-07-29 16:16:14 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.1, CPU: 16.27%, 内存: 1132.03MB
2025-07-29 16:16:16 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.9, CPU: 15.95%, 内存: 1132.41MB
2025-07-29 16:16:18 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.5, CPU: 16.28%, 内存: 1132.26MB
2025-07-29 16:16:19 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.7, CPU: 16.47%, 内存: 1133.14MB
2025-07-29 16:16:21 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.7, CPU: 16.03%, 内存: 1133.71MB
2025-07-29 16:16:23 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.4, CPU: 15.48%, 内存: 1133.1MB
2025-07-29 16:16:25 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.2, CPU: 15.52%, 内存: 1136.35MB
2025-07-29 16:16:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.6, CPU: 15.34%, 内存: 1144.06MB
2025-07-29 16:16:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.3, CPU: 13.27%, 内存: 1143.66MB
2025-07-29 16:16:30 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 30.5秒
2025-07-29 16:16:30 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:16:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.5, CPU: 13.7%, 内存: 1143.65MB
2025-07-29 16:16:31 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:16:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.7, CPU: 18.02%, 内存: 1116.95MB
2025-07-29 16:16:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.7, CPU: 13.27%, 内存: 1116.2MB
2025-07-29 16:16:35 - poco_universal - INFO - 元素存在性: UIMgr -> AdChestView -> close1_btn = False
2025-07-29 16:16:35 - test_runner - INFO - 🏆第16关
2025-07-29 16:16:35 - test_runner - INFO - 🛫回大厅开启第16关
2025-07-29 16:16:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.2, CPU: 12.04%, 内存: 1116.06MB
2025-07-29 16:16:39 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.2, CPU: 12.58%, 内存: 1117.36MB
2025-07-29 16:16:40 - poco_universal - INFO - 开始循环检查特殊元素...
2025-07-29 16:16:40 - poco_universal - INFO - 第 1 轮特殊元素检查...
2025-07-29 16:16:41 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 37.6, CPU: 9.89%, 内存: 1116.89MB
2025-07-29 16:16:42 - poco_universal - INFO - 元素存在性: BtnClose_btn = True
2025-07-29 16:16:42 - poco_universal - INFO - 发现特殊元素: BtnClose_btn
2025-07-29 16:16:42 - poco_universal - INFO - 执行点击操作: BtnClose_btn
2025-07-29 16:16:43 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.5, CPU: 11.2%, 内存: 1116.87MB
2025-07-29 16:16:44 - poco_universal - INFO - 点击成功: BtnClose_btn
2025-07-29 16:16:44 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 41.3, CPU: 10.91%, 内存: 1117.08MB
2025-07-29 16:16:46 - poco_universal - INFO - 处理特殊元素完成: BtnClose_btn, 结果: True
2025-07-29 16:16:46 - poco_universal - INFO - 第 1 轮处理了 1 个特殊元素，等待 0.5 秒后进行下一轮检查...
2025-07-29 16:16:46 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.8, CPU: 11.14%, 内存: 1116.69MB
2025-07-29 16:16:47 - poco_universal - INFO - 第 2 轮特殊元素检查...
2025-07-29 16:16:48 - poco_universal - INFO - 元素存在性: BtnClose_btn = False
2025-07-29 16:16:48 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.5, CPU: 11.06%, 内存: 1117.96MB
2025-07-29 16:16:50 - poco_universal - INFO - 元素存在性: text_continue = False
2025-07-29 16:16:50 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 43.7, CPU: 10.8%, 内存: 1116.75MB
2025-07-29 16:16:51 - poco_universal - INFO - 元素存在性: singleBtn_btn = False
2025-07-29 16:16:52 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.6, CPU: 11.21%, 内存: 1116.75MB
2025-07-29 16:16:53 - poco_universal - INFO - 元素存在性: UIMgr -> green_btn = False
2025-07-29 16:16:54 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.3, CPU: 11.34%, 内存: 1117.05MB
2025-07-29 16:16:54 - poco_universal - INFO - 元素存在性: UIMgr -> BtnSingleContinue_btn = False
2025-07-29 16:16:56 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.9, CPU: 11.01%, 内存: 1116.74MB
2025-07-29 16:16:56 - poco_universal - INFO - 元素存在性: commonBtnGreenBig = False
2025-07-29 16:16:57 - poco_universal - INFO - 元素存在性: Close_btn = False
2025-07-29 16:16:58 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.7, CPU: 11.49%, 内存: 1116.74MB
2025-07-29 16:16:59 - poco_universal - INFO - 元素存在性: Btn_continue_an -> Btn_continue_btn = False
2025-07-29 16:17:00 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.3, CPU: 10.75%, 内存: 1116.69MB
2025-07-29 16:17:00 - poco_universal - INFO - 元素存在性: close_btn = False
2025-07-29 16:17:02 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 56.8, CPU: 11.13%, 内存: 1116.67MB
2025-07-29 16:17:02 - poco_universal - INFO - 元素存在性: Btn_continue_btn = False
2025-07-29 16:17:02 - poco_universal - INFO - 第 2 轮检查未发现特殊元素，处理完成
2025-07-29 16:17:02 - poco_universal - INFO - 特殊元素处理完成: 循环 2 轮，总共处理了 1 个特殊元素
2025-07-29 16:17:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 48.7, CPU: 11.19%, 内存: 1116.74MB
2025-07-29 16:17:04 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:17:04 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:17:04 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:17:05 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 52.6, CPU: 10.94%, 内存: 1116.72MB
2025-07-29 16:17:07 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:17:07 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 57.4, CPU: 10.71%, 内存: 1117.03MB
2025-07-29 16:17:08 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:17:09 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.5, CPU: 11.69%, 内存: 1121.5MB
2025-07-29 16:17:10 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:17:11 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:17:11 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 55.6, CPU: 11.53%, 内存: 1121.5MB
2025-07-29 16:17:13 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 49.9, CPU: 9.79%, 内存: 1122.16MB
2025-07-29 16:17:14 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:17:15 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.0, CPU: 17.05%, 内存: 1126.26MB
2025-07-29 16:17:17 - poco_universal - INFO - 等待并点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 超时: 180秒
2025-07-29 16:17:17 - poco_universal - INFO - 元素出现成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn, 等待时间: 0.6秒
2025-07-29 16:17:17 - poco_universal - INFO - 开始点击元素: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:17:17 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.2, CPU: 15.43%, 内存: 1144.72MB
2025-07-29 16:17:19 - poco_universal - INFO - 点击成功: UIMgr -> BattleMatchPanel -> BtnDebugAutoCollect_btn
2025-07-29 16:17:19 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.9, CPU: 17.08%, 内存: 1144.66MB
2025-07-29 16:17:20 - poco_universal - INFO - 等待并点击元素: UIMgr -> BtnSingleContinue_btn, 超时: 180秒
2025-07-29 16:17:21 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.9, CPU: 17.03%, 内存: 1144.27MB
2025-07-29 16:17:23 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 47.0, CPU: 16.92%, 内存: 1144.29MB
2025-07-29 16:17:25 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.0, CPU: 17.04%, 内存: 1144.28MB
2025-07-29 16:17:27 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.0, CPU: 16.61%, 内存: 1144.8MB
2025-07-29 16:17:29 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.7, CPU: 16.51%, 内存: 1153.28MB
2025-07-29 16:17:31 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.5, CPU: 16.38%, 内存: 1158.09MB
2025-07-29 16:17:33 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 35.2, CPU: 16.31%, 内存: 1159.63MB
2025-07-29 16:17:35 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 32.4, CPU: 15.7%, 内存: 1159.71MB
2025-07-29 16:17:37 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 33.4, CPU: 15.7%, 内存: 1159.86MB
2025-07-29 16:17:38 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.6, CPU: 15.31%, 内存: 1159.75MB
2025-07-29 16:17:40 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 34.9, CPU: 15.89%, 内存: 1160.15MB
2025-07-29 16:17:42 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 39.8, CPU: 15.38%, 内存: 1162.45MB
2025-07-29 16:17:44 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 36.4, CPU: 16.26%, 内存: 1166.06MB
2025-07-29 16:17:46 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.3, CPU: 13.61%, 内存: 1165.88MB
2025-07-29 16:17:47 - poco_universal - INFO - 元素出现成功: UIMgr -> BtnSingleContinue_btn, 等待时间: 26.6秒
2025-07-29 16:17:47 - poco_universal - INFO - 开始点击元素: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:17:48 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.2, CPU: 14.11%, 内存: 1165.74MB
2025-07-29 16:17:48 - poco_universal - INFO - 点击成功: UIMgr -> BtnSingleContinue_btn
2025-07-29 16:17:49 - test_runner - INFO - 🏆第17关
2025-07-29 16:17:49 - test_runner - INFO - 🛫回大厅开启第17关,关内飞机引导，神秘宝箱引导
2025-07-29 16:17:50 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 40.8, CPU: 17.35%, 内存: 1141.95MB
2025-07-29 16:17:51 - poco_universal - INFO - 等待元素出现: MainMenuPanel -> move_bg_tr, 超时: 180秒
2025-07-29 16:17:52 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 42.6, CPU: 13.68%, 内存: 1139.79MB
2025-07-29 16:17:52 - poco_universal - INFO - 元素出现成功: MainMenuPanel -> move_bg_tr, 等待时间: 0.5秒
2025-07-29 16:17:52 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> move_bg_tr
2025-07-29 16:17:54 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 46.2, CPU: 12.07%, 内存: 1139.39MB
2025-07-29 16:17:54 - poco_universal - INFO - 点击成功: MainMenuPanel -> move_bg_tr
2025-07-29 16:17:55 - poco_universal - INFO - 执行点击操作: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:17:55 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 44.2, CPU: 12.89%, 内存: 1139.39MB
2025-07-29 16:17:57 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 50.6, CPU: 12.46%, 内存: 1139.56MB
2025-07-29 16:17:57 - poco_universal - INFO - 点击成功: MainMenuPanel -> HomePanel_com -> lv_txtPro
2025-07-29 16:17:58 - poco_universal - INFO - 执行点击操作: Play
2025-07-29 16:17:59 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 45.8, CPU: 10.45%, 内存: 1139.39MB
2025-07-29 16:18:01 - poco_universal - INFO - 点击成功: Play
2025-07-29 16:18:01 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 54.9, CPU: 10.44%, 内存: 1140.48MB
2025-07-29 16:18:03 - PerformanceMonitor-TM3D - INFO - 性能数据 - FPS: 51.3, CPU: 17.42%, 内存: 1157.77MB
