#!/usr/bin/env python3
"""
测试BOM标记修复和基础对比配置
"""

import os
import sys
import tempfile
import pandas as pd
from pathlib import Path
sys.path.append('.')

def test_bom_removal():
    """测试BOM标记移除"""
    print("测试BOM标记移除...")
    
    # 创建测试数据
    test_data = {
        '列名1': ['数据1', '数据2', '数据3'],
        '列名2': ['值1', '值2', '值3'],
        '列名3': ['内容1', '内容2', '内容3']
    }
    
    df = pd.DataFrame(test_data)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 测试1: 使用utf-8-sig（会产生BOM）
        csv_with_bom = temp_path / "test_with_bom.csv"
        df.to_csv(csv_with_bom, index=False, encoding='utf-8-sig', header=True)
        
        # 测试2: 使用utf-8（不会产生BOM）
        csv_without_bom = temp_path / "test_without_bom.csv"
        df.to_csv(csv_without_bom, index=False, encoding='utf-8', header=True)
        
        # 检查BOM标记
        print("\n=== BOM检查 ===")
        
        # 检查带BOM的文件
        with open(csv_with_bom, 'rb') as f:
            first_bytes = f.read(3)
            has_bom = first_bytes == b'\xef\xbb\xbf'
            print(f"utf-8-sig文件: {csv_with_bom.name}")
            print(f"  前3字节: {first_bytes}")
            print(f"  包含BOM: {'是' if has_bom else '否'}")
        
        # 检查不带BOM的文件
        with open(csv_without_bom, 'rb') as f:
            first_bytes = f.read(3)
            has_bom = first_bytes == b'\xef\xbb\xbf'
            print(f"utf-8文件: {csv_without_bom.name}")
            print(f"  前3字节: {first_bytes}")
            print(f"  包含BOM: {'是' if has_bom else '否'}")
        
        # 检查文本内容
        print("\n=== 文本内容检查 ===")
        
        with open(csv_with_bom, 'r', encoding='utf-8-sig') as f:
            content_with_bom = f.readline().strip()
            print(f"带BOM文件第一行: '{content_with_bom}'")
        
        with open(csv_without_bom, 'r', encoding='utf-8') as f:
            content_without_bom = f.readline().strip()
            print(f"不带BOM文件第一行: '{content_without_bom}'")
        
        # 检查是否有隐藏字符
        with open(csv_with_bom, 'r', encoding='utf-8') as f:
            raw_content = f.readline()
            if raw_content.startswith('\ufeff'):
                print(f"❌ 带BOM文件在utf-8读取时有BOM字符: '\\ufeff{raw_content[1:].strip()}'")
            else:
                print(f"✅ 带BOM文件在utf-8读取时正常: '{raw_content.strip()}'")
        
        with open(csv_without_bom, 'r', encoding='utf-8') as f:
            raw_content = f.readline()
            if raw_content.startswith('\ufeff'):
                print(f"❌ 不带BOM文件有BOM字符: '\\ufeff{raw_content[1:].strip()}'")
            else:
                print(f"✅ 不带BOM文件正常: '{raw_content.strip()}'")

def test_compare_script():
    """测试BCompare脚本配置"""
    print("\n=== BCompare脚本配置检查 ===")
    
    script_path = Path("config/compare_folder.txt")
    
    if not script_path.exists():
        print("❌ BCompare脚本文件不存在")
        return
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("脚本内容:")
    print(content)
    
    # 检查关键配置
    checks = [
        ("criteria rules-based", "基础比较规则", True),
        ("load %1 %2", "加载文件夹", True),
        ("expand all", "展开所有子文件夹", True),
        ("select all.diff", "选择差异文件", True),
        ("file-report", "生成报告", True),
        ("file-format csv header-row", "CSV标题行设置", False),  # 应该不存在
        ("column-names", "显示列名", False)  # 应该不存在
    ]
    
    print("\n=== 配置检查 ===")
    for check_text, description, should_exist in checks:
        exists = check_text in content
        if should_exist:
            status = "✅" if exists else "❌"
            print(f"{status} {description}: {check_text} ({'存在' if exists else '不存在'})")
        else:
            status = "✅" if not exists else "❌"
            print(f"{status} {description}: {check_text} ({'已移除' if not exists else '仍存在'})")

def test_service_integration():
    """测试服务集成"""
    print("\n=== 服务集成测试 ===")
    
    try:
        from services.config_compare_service import SimpleConfigCompareService
        
        service = SimpleConfigCompareService('TEST')
        print(f"✅ 服务创建成功，版本: {service.VERSION}")
        
        # 测试清理方法
        if hasattr(service, '_clean_sheet_name'):
            test_names = ["Sheet1", "#事件数据", "用户配置"]
            print("\n工作表名称清理测试:")
            for name in test_names:
                clean_name = service._clean_sheet_name(name)
                print(f"  '{name}' -> '{clean_name}'")
        
    except Exception as e:
        print(f"❌ 服务集成测试失败: {e}")

if __name__ == "__main__":
    test_bom_removal()
    test_compare_script()
    test_service_integration()
