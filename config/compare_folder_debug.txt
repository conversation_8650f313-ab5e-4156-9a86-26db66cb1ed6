# BCompare 调试脚本 - 用于诊断问题
# 这个脚本包含详细的步骤，便于定位问题

# 设置比较模式为基于规则的比较
criteria rules-based

# 加载要比较的两个文件夹
# %1 = 旧版本文件夹
# %2 = 新版本文件夹
load %1 %2

# 展开所有子文件夹以显示所有文件
expand all

# 选择所有有差异的文件
select all.diff

# 生成文件对比报告
# layout:side-by-side = 并排显示
# options:display-mismatches = 只显示差异部分
# title:"%4" = 使用第4个参数作为报告标题
# output-to:%3 = 输出到第3个参数指定的文件
# output-options:html-color = 生成彩色HTML
file-report layout:side-by-side options:display-mismatches title:"%4" output-to:%3 output-options:html-color
