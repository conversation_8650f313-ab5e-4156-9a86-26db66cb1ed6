# 原生Poco使用指南

本文档介绍如何使用新添加的 `get_native_poco()` 方法来获取原生的poco对象，支持直接使用原生poco的所有功能和链式调用。

## 功能概述

新增的 `get_native_poco()` 方法允许您：
- 获取原生的 `UnityPoco` 或 `AndroidUiautomationPoco` 对象
- 使用原生poco的所有方法和属性
- 支持复杂的链式调用，如 `poco("SR_Canvas").offspring("SR_Content").child("Category(Clone)")[1].click()`
- 保持与现有封装poco的兼容性

## 使用方法

### 1. 通过 PocoManager 获取原生poco

```python
from services.airtest_func.PocoManager import PocoManager

# 获取Unity原生poco
native_poco = PocoManager.get_native_poco(device_id="your_device", poco_type='unity')

# 使用原生poco的链式调用
native_poco("SR_Canvas").offspring("SR_Content").child("Category(Clone)")[1].child("NumberOption(Clone)")[0].child("SR_Title").click()

# 使用text属性查找元素
native_poco(text="卡包Id").click()

# 获取Android原生poco
android_native = PocoManager.get_native_poco(device_id="your_device", poco_type='android')
android_native("android.widget.Button").offspring("android.widget.TextView")[0].click()
```

### 2. 通过实例获取原生poco

```python
from services.airtest_func.PocoManager import PocoManager

# 先获取封装的poco实例
unity_poco = PocoManager.get_poco(device_id="your_device", poco_type='unity')

# 获取原生poco对象
native_poco = unity_poco.get_native_poco()

# 使用原生poco
native_poco(text="开始游戏").click()
native_poco("MainMenu").child("SettingsButton").click()
```

### 3. 通过 SmartPoco 获取原生poco

```python
from services.airtest_func.PocoManager import SmartPoco

smart_poco = SmartPoco(device_id="your_device")

# 根据操作类型自动获取原生poco
game_native = smart_poco.get_native_poco('game')        # 返回Unity原生poco
system_native = smart_poco.get_native_poco('system')    # 返回Android原生poco
privacy_native = smart_poco.get_native_poco('privacy')  # 返回Android原生poco

# 直接获取特定类型的原生poco
unity_native = smart_poco.get_native_unity_poco()
android_native = smart_poco.get_native_android_poco()
```

## 实际使用示例

### Unity游戏操作示例

```python
# 获取Unity原生poco
native_poco = PocoManager.get_native_poco(device_id="R5CWC1899HY", poco_type='unity')

# 复杂的游戏UI操作
native_poco("SR_Canvas").offspring("SR_Content").child("Category(Clone)")[1].child("NumberOption(Clone)")[0].child("SR_Title").click()

# 处理背包系统
inventory_panel = native_poco("InventoryPanel")
if inventory_panel.exists():
    # 点击第一个物品
    inventory_panel.offspring("ItemGrid").child("Item")[0].click()
    
    # 查找特定稀有度的物品
    legendary_items = inventory_panel.offspring("Item").filter(lambda x: x.attr("rarity") == "legendary")
    if legendary_items:
        legendary_items[0].click()

# 使用text属性查找按钮
native_poco(text="卡包Id").click()
native_poco(text="确认购买").click()
```

### Android系统操作示例

```python
# 获取Android原生poco
native_poco = PocoManager.get_native_poco(device_id="R5CWC1899HY", poco_type='android')

# 处理权限弹窗
native_poco("android.widget.FrameLayout").offspring("android.widget.Button", text="允许").click()

# 复杂的Android UI导航
native_poco("android.widget.LinearLayout").child("android.widget.Button")[2].click()

# 输入文本
native_poco(className="android.widget.EditText").set_text("测试文本")

# 使用resourceId查找元素
native_poco(resourceId="com.example.app:id/confirm_button").click()
```

### 混合使用示例

```python
from services.airtest_func.PocoManager import SmartPoco

smart_poco = SmartPoco(device_id="your_device")

# 游戏内操作使用Unity原生poco
game_poco = smart_poco.get_native_poco('game')
game_poco("StartButton").click()
game_poco("SR_Canvas").offspring("MainMenu").child("PlayButton").click()

# 系统弹窗处理使用Android原生poco
system_poco = smart_poco.get_native_poco('system')
if system_poco(text="允许").exists():
    system_poco(text="允许").click()

# 购买流程使用Android原生poco
purchase_poco = smart_poco.get_native_poco('purchase')
purchase_poco("android.widget.Button", text="确认支付").click()
```

## 原生poco的优势

### 1. 完整的API支持
原生poco提供完整的API功能，包括：
- 复杂的元素查找和过滤
- 链式调用支持
- 高级属性获取
- 自定义等待条件

### 2. 更灵活的元素定位
```python
# 支持复杂的链式调用
element = native_poco("Parent").offspring("Child").child("GrandChild")[0]

# 支持属性过滤
buttons = native_poco("Button").filter(lambda x: x.attr("enabled") == True)

# 支持索引访问
first_item = native_poco("Item")[0]
last_item = native_poco("Item")[-1]
```

### 3. 高级功能访问
```python
# 获取元素属性
attrs = native_poco("Element").attr()

# 获取元素边界
bounds = native_poco("Element").get_bounds()

# 获取元素位置
position = native_poco("Element").get_position()

# 等待元素出现
native_poco("LoadingPanel").wait_for_appearance(timeout=10)

# 等待元素消失
native_poco("LoadingPanel").wait_for_disappearance(timeout=5)
```

## 注意事项

1. **连接状态检查**: 在使用原生poco前，确保连接有效
2. **异常处理**: 原生poco操作可能抛出不同的异常，需要适当处理
3. **兼容性**: 原生poco的API可能因版本而异
4. **性能**: 原生poco通常比封装poco有更好的性能

## 错误处理

```python
try:
    native_poco = PocoManager.get_native_poco(device_id="your_device", poco_type='unity')
    native_poco("Button").click()
except RuntimeError as e:
    print(f"连接错误: {e}")
except Exception as e:
    print(f"操作失败: {e}")
```

## 与封装poco的对比

| 特性 | 封装poco | 原生poco |
|------|----------|----------|
| 易用性 | 高 | 中等 |
| 功能完整性 | 部分 | 完整 |
| 链式调用 | 有限 | 完整支持 |
| 性能 | 中等 | 高 |
| 错误处理 | 统一 | 需要自行处理 |
| 学习成本 | 低 | 中等 |

## 总结

新增的 `get_native_poco()` 方法为您提供了更强大和灵活的poco操作能力，特别适合：
- 需要复杂UI操作的场景
- 需要使用poco高级功能的场景
- 对性能有较高要求的场景
- 需要与现有poco代码兼容的场景

通过合理使用原生poco，您可以实现更精确和高效的自动化测试操作。
