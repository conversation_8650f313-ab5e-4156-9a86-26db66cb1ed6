# 跨平台兼容性和CSV标题强制修复

## 问题概述

1. **CSV标题不一致问题** - 部分配置表在HTML中显示标题，部分不显示
2. **跨平台兼容性问题** - BComp.exe命令不兼容Mac系统

## 问题1：强制CSV第一行作为标题行

### 问题分析

**现象**：
- `MatchModelV2.xlsx` → HTML显示列标题 ✅
- `DartRivalsProgress.xlsx` → HTML不显示列标题 ❌

**根本原因**：
- 不同Excel文件的数据结构不同
- 智能检测算法可能误判
- 需要强制统一处理方式

### 解决方案

#### 1. 强制标题行处理策略

**新的处理逻辑**：
```python
# 无论是否检测到标题，都强制将第一行作为标题处理
if has_header:
    # 原本就是标题，正常处理
    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=0)
else:
    # 强制将第一行作为标题行
    first_row = df_raw.iloc[0]
    column_names = [str(val).strip() if pd.notna(val) else f"Column_{i+1}" 
                   for i, val in enumerate(first_row)]
    df = df_raw.iloc[1:].copy()
    df.columns = column_names
```

#### 2. 列名清理和标准化

**清理规则**：
- 移除换行符：`\n` → `_`
- 移除回车符：`\r` → `_`
- 空值处理：空值 → `Column_N`
- 去除首尾空格

**示例转换**：
```
原始第一行: ["ID", "名称\n说明", "", "数值"]
处理后列名: ["ID", "名称_说明", "Column_3", "数值"]
```

#### 3. 边界情况处理

**只有一行数据**：
```python
if len(df_raw) > 1:
    df = df_raw.iloc[1:].copy()  # 从第二行开始作为数据
else:
    df = pd.DataFrame(columns=column_names)  # 创建空DataFrame但保留列名
```

**空工作表**：
```python
if df_raw.empty:
    self.logger.warning(f"工作表 {sheet_name} 为空，跳过")
    continue
```

## 问题2：跨平台BCompare命令兼容

### 问题分析

**Windows vs Mac差异**：
- Windows: `BComp.exe`
- macOS: `bcomp` (无.exe扩展名)
- Linux: `bcompare`

### 解决方案

#### 1. 动态命令检测

新增 `_get_bcompare_command()` 方法：

```python
def _get_bcompare_command(self):
    """获取跨平台的BCompare命令"""
    import platform
    
    system = platform.system().lower()
    
    if system == 'windows':
        return 'BComp.exe'
    elif system == 'darwin':  # macOS
        mac_paths = [
            '/Applications/Beyond Compare.app/Contents/MacOS/bcomp',
            '/usr/local/bin/bcomp',
            'bcomp'
        ]
        for path in mac_paths:
            if os.path.exists(path) or path == 'bcomp':
                return path
        return 'bcomp'
    elif system == 'linux':
        return 'bcompare'
    else:
        return 'bcomp'
```

#### 2. 平台特定路径检测

**macOS路径优先级**：
1. `/Applications/Beyond Compare.app/Contents/MacOS/bcomp` (标准安装路径)
2. `/usr/local/bin/bcomp` (Homebrew安装路径)
3. `bcomp` (PATH中的命令)

#### 3. 输出重定向兼容

**跨平台重定向**：
```python
redirect_cmd = " >nul 2>&1" if os.name == 'nt' else " >/dev/null 2>&1"
```

- Windows: `>nul 2>&1`
- Unix/Linux/macOS: `>/dev/null 2>&1`

## 修复效果

### CSV标题一致性

**修复前**：
```
MatchModelV2.xlsx:
  - 检测到标题 → 显示原始列名 ✅
  
DartRivalsProgress.xlsx:
  - 未检测到标题 → 生成Column_1, Column_2... ❌
```

**修复后**：
```
所有Excel文件:
  - 强制第一行作为标题 → 显示实际第一行内容 ✅
  - 统一的标题显示格式 ✅
```

### 跨平台兼容性

**支持的平台**：
- ✅ Windows: `BComp.exe`
- ✅ macOS: `bcomp` (多路径检测)
- ✅ Linux: `bcompare`
- ✅ 其他Unix系统: `bcomp`

## 配置文件更新

### 主脚本简化

`config/compare_folder.txt`:
```
# 简化的CSV对比配置 - 通过数据预处理确保标题行
criteria rules-based
load %1 %2
expand all
select all.diff
file-report layout:side-by-side options:display-mismatches title:"%4" output-to:%3 output-options:html-color
```

### 专用CSV脚本

`config/compare_folder_csv_header.txt`:
```
# 专门处理CSV标题行的BCompare脚本
criteria rules-based
load %1 %2
expand all
table-format csv:header-row
select all.diff
file-report layout:side-by-side options:display-mismatches title:"%4" output-to:%3 output-options:html-color
```

## 测试验证

### CSV标题测试

1. **有明确标题的Excel**
   - 验证保持原始标题
   - 确认HTML正确显示

2. **数据行作为标题的Excel**
   - 验证第一行数据被用作列名
   - 确认HTML显示这些"数据标题"

3. **特殊字符处理**
   - 测试包含换行符的标题
   - 验证特殊字符清理

### 跨平台测试

1. **Windows环境**
   - 验证 `BComp.exe` 正常工作
   - 确认无弹窗执行

2. **macOS环境**
   - 测试标准安装路径检测
   - 验证Homebrew安装路径
   - 确认命令行执行

3. **Linux环境**
   - 验证 `bcompare` 命令
   - 测试包管理器安装版本

## 日志增强

### 详细的处理日志

```python
self.logger.debug(f"转换CSV: {csv_filename} (强制标题行: 是)")
self.logger.info(f"使用macOS BCompare命令: {path}")
self.logger.debug(f"强制将第一行作为标题: {sheet_name}")
```

### 平台检测日志

```python
self.logger.info(f"检测到操作系统: {system}")
self.logger.info(f"使用BCompare命令: {bcompare_cmd}")
```

## 注意事项

### CSV标题强制处理

- **数据丢失风险**: 第一行数据会被用作列名
- **适用场景**: 适合配置表等结构化数据
- **不适用场景**: 纯数据文件可能不合适

### 跨平台兼容性

- **路径检测**: macOS路径检测依赖文件系统
- **权限要求**: 需要执行BCompare的权限
- **版本要求**: 不同平台的BCompare版本可能有差异

## 总结

通过强制标题行处理和跨平台命令兼容，我们实现了：

1. **一致的用户体验** - 所有CSV对比都显示列标题
2. **跨平台支持** - Windows、macOS、Linux全平台兼容
3. **智能路径检测** - 自动找到正确的BCompare命令
4. **健壮的错误处理** - 边界情况和异常处理

这些改进使配置表对比功能更加稳定和通用。

---

*文档版本：v1.0*  
*最后更新：2025-01-28*
